{"version": 3, "file": "no-type-alias.js", "sourceRoot": "", "sources": ["../../src/rules/no-type-alias.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAmChC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,eAAe;IACrB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,iCAAiC;YAC9C,kBAAkB,EAChB,4DAA4D;SAC/D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,IAAI,EAAE;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAW;4BACX,kBAAkB;4BAClB,6BAA6B;yBAC9B;qBACF;oBACD,cAAc,EAAE;wBACd,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,aAAa,EAAE;wBACb,IAAI,EAAE;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAW;4BACX,kBAAkB;4BAClB,6BAA6B;yBAC9B;qBACF;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE;4BACJ,QAAQ;4BACR,OAAO;4BACP,WAAW;4BACX,kBAAkB;4BAClB,6BAA6B;yBAC9B;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,OAAO;YACrB,cAAc,EAAE,OAAO;YACvB,aAAa,EAAE,OAAO;YACtB,gBAAgB,EAAE,OAAO;SAC1B;KACF;IACD,MAAM,CACJ,OAAO,EACP,CAAC,EAAE,YAAY,EAAE,cAAc,EAAE,aAAa,EAAE,gBAAgB,EAAE,CAAC;QAEnE,MAAM,MAAM,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,6BAA6B,CAAC,CAAC;QACtE,MAAM,aAAa,GAAG;YACpB,QAAQ;YACR,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,YAAY,GAAG;YACnB,WAAW;YACX,kBAAkB;YAClB,6BAA6B;SAC9B,CAAC;QACF,MAAM,UAAU,GAAG,IAAI,GAAG,CAAC;YACzB,mCAAc,CAAC,WAAW;YAC1B,mCAAc,CAAC,eAAe;YAC9B,mCAAc,CAAC,aAAa;YAC5B,mCAAc,CAAC,WAAW;YAC1B,mCAAc,CAAC,mBAAmB;SACnC,CAAC,CAAC;QAEH;;;;;WAKG;QACH,SAAS,sBAAsB,CAC7B,UAAmB,EACnB,eAAuC,EACvC,OAAe;YAEf,OAAO,CACL,YAAY,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;gBACpC,CAAC,CAAC,UAAU;oBACV,CAAC,CAAC,eAAe,KAAK,mCAAc,CAAC,WAAW;wBAC9C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC;wBAC7B,CAAC,eAAe,KAAK,mCAAc,CAAC,kBAAkB;4BACpD,aAAa,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAC7C,CAAC;QACJ,CAAC;QAED;;;;;;;WAOG;QACH,SAAS,WAAW,CAClB,IAAmB,EACnB,eAAuC,EACvC,MAAe,EACf,IAAY;YAEZ,IAAI,MAAM,EAAE;gBACV,OAAO,OAAO,CAAC,MAAM,CAAC;oBACpB,IAAI;oBACJ,SAAS,EAAE,aAAa;oBACxB,IAAI,EAAE;wBACJ,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE;qBAC1B;iBACF,CAAC,CAAC;aACJ;YAED,OAAO,OAAO,CAAC,MAAM,CAAC;gBACpB,IAAI;gBACJ,SAAS,EAAE,oBAAoB;gBAC/B,IAAI,EAAE;oBACJ,eAAe,EACb,eAAe,KAAK,mCAAc,CAAC,WAAW;wBAC5C,CAAC,CAAC,OAAO;wBACT,CAAC,CAAC,cAAc;oBACpB,QAAQ,EAAE,IAAI;iBACf;aACF,CAAC,CAAC;QACL,CAAC;QAED;;;;;;WAMG;QACH,SAAS,mBAAmB,CAC1B,IAAmB,EACnB,aAAsB,KAAK;YAE3B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;gBACpD,WAAW;gBACX,IAAI,cAAc,KAAK,OAAO,EAAE;oBAC9B,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;iBACvE;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa,EAAE;gBAC1D,sBAAsB;gBACtB,IACE,aAAa,KAAK,OAAO;oBACzB,CAAC,sBAAsB,CACrB,UAAU,EACV,IAAI,CAAC,eAAe,EACpB,aAAc,CACf,EACD;oBACA,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;iBACtE;aACF;iBAAM,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,YAAY,EAAE;gBACzD,cAAc;gBACd,IACE,gBAAgB,KAAK,OAAO;oBAC5B,CAAC,sBAAsB,CACrB,UAAU,EACV,IAAI,CAAC,eAAe,EACpB,gBAAiB,CAClB,EACD;oBACA,WAAW,CACT,IAAI,CAAC,IAAI,EACT,IAAI,CAAC,eAAe,EACpB,UAAU,EACV,cAAc,CACf,CAAC;iBACH;aACF;iBAAM,IACL,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAC/B,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAC9B;gBACA,kBAAkB;gBAClB,IACE,YAAY,KAAK,OAAO;oBACxB,CAAC,sBAAsB,CACrB,UAAU,EACV,IAAI,CAAC,eAAe,EACpB,YAAa,CACd,EACD;oBACA,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,SAAS,CAAC,CAAC;iBACrE;aACF;iBAAM;gBACL,oCAAoC;gBACpC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;aACvE;QACH,CAAC;QAED;;WAEG;QACH,SAAS,QAAQ,CACf,IAAmB,EACnB,kBAA0C,IAAI;YAE9C,IACE,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,WAAW;gBACxC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,kBAAkB,EAC/C;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAkB,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;oBACtD,GAAG,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvC,OAAO,GAAG,CAAC;gBACb,CAAC,EAAE,EAAE,CAAC,CAAC;aACR;YACD,IAAI,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,mBAAmB,EAAE;gBACpD,OAAO,QAAQ,CAAC,IAAI,CAAC,cAAc,EAAE,eAAe,CAAC,CAAC;aACvD;YACD,OAAO,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,CAAC,CAAC;QACrC,CAAC;QAED,OAAO;YACL,sBAAsB,CAAC,IAAI;gBACzB,MAAM,KAAK,GAAG,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAC5C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;oBACtB,iCAAiC;oBACjC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBACrC;qBAAM;oBACL,wBAAwB;oBACxB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBACnB,mBAAmB,CAAC,IAAI,CAAC,CAAC;oBAC5B,CAAC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}