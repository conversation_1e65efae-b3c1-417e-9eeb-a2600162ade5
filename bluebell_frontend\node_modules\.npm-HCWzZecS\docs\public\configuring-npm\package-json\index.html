<!DOCTYPE html><html><head><script>"use strict";!function(){var i=(window.location.pathname.match(/^(\/(?:ipfs|ipns)\/[^/]+)/)||[])[1]||"";window.__GATSBY_IPFS_PATH_PREFIX__=i}();</script><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><style data-href="../../styles.e93b5499b63484750fba.css">code[class*=language-],pre[class*=language-]{color:#ccc;background:none;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:#2d2d2d}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal}.token.block-comment,.token.cdata,.token.comment,.token.doctype,.token.prolog{color:#999}.token.punctuation{color:#ccc}.token.attr-name,.token.deleted,.token.namespace,.token.tag{color:#e2777a}.token.function-name{color:#6196cc}.token.boolean,.token.function,.token.number{color:#f08d49}.token.class-name,.token.constant,.token.property,.token.symbol{color:#f8c555}.token.atrule,.token.builtin,.token.important,.token.keyword,.token.selector{color:#cc99cd}.token.attr-value,.token.char,.token.regex,.token.string,.token.variable{color:#7ec699}.token.entity,.token.operator,.token.url{color:#67cdcc}.token.bold,.token.important{font-weight:700}.token.italic{font-style:italic}.token.entity{cursor:help}.token.inserted{color:green}a,abbr,acronym,address,applet,article,aside,audio,b,big,blockquote,body,canvas,caption,center,cite,code,dd,del,details,dfn,div,dl,dt,em,embed,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,ins,kbd,label,legend,li,mark,menu,nav,object,ol,output,p,pre,q,ruby,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,u,ul,var,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:after,blockquote:before,q:after,q:before{content:"";content:none}table{border-collapse:collapse;border-spacing:0}[hidden]{display:none}html{font-family:Poppins,sans-serif}*{box-sizing:border-box}li,p{font-size:15px;line-height:1.7;font-weight:300}p,ul{padding:10px 0}strong{font-weight:700;color:#c3f}li{list-style-type:disc;list-style-position:inside;padding:8px 0}.documentation h1{font-size:42px;font-weight:600;padding:30px 0 10px}.documentation h2{font-size:22px;font-weight:300}.documentation h3{color:#c3f;font-size:22px;padding:30px 0 5px;font-weight:500}.documentation h4{font-weight:600;padding:20px 0 5px}.documentation p{display:inline-block}:not(pre)>code[class*=language-],pre[class*=language-]{border-radius:4px;background-color:#413844;font-size:13px}:not(pre)>code[class*=language-text]{background-color:rgba(204,139,216,.1);color:#413844;padding:2px 6px;border-radius:0;font-size:14px;font-weight:700;border-radius:1px;display:inline-block}.documentation a,a>code[class*=language-text]{color:#fb3b49;font-weight:600}p>code[class*=language-text]{display:inline-block}.documentation h1:before{content:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 27 26'%3E%3Cdefs%3E%3ClinearGradient id='a' x1='18.13' x2='25.6' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='.37' stop-color='%23fb8719'/%3E%3Cstop offset='.51' stop-color='%23fa8420'/%3E%3Cstop offset='.61' stop-color='%23f9802c'/%3E%3Cstop offset='.69' stop-color='%23f7793d'/%3E%3Cstop offset='.76' stop-color='%23f47053'/%3E%3Cstop offset='.82' stop-color='%23f1656e'/%3E%3Cstop offset='.87' stop-color='%23ed578f'/%3E%3Cstop offset='.92' stop-color='%23e948b5'/%3E%3Cstop offset='.97' stop-color='%23e437de'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='b' x1='17.89' x2='25.84' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='1' x2='18.69' y1='17.84' y2='17.84' xlink:href='%23a'/%3E%3ClinearGradient id='d' x1='.76' x2='18.93' y1='17.84' y2='17.84' xlink:href='%23b'/%3E%3ClinearGradient id='e' x1='1' x2='20.48' y1='7.33' y2='7.33' xlink:href='%23a'/%3E%3ClinearGradient id='f' x1='.76' x2='20.72' y1='7.33' y2='7.33' xlink:href='%23b'/%3E%3C/defs%3E%3Cpath fill='url(%23a)' stroke='url(%23b)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.34-.41L25 14.06l-5-11a.28.28 0 11.5-.23L25.58 14a.28.28 0 010 .28l-6.91 9.9a.28.28 0 01-.14.06z'/%3E%3Cpath fill='url(%23c)' stroke='url(%23d)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.14 0l-12-1.15a.28.28 0 01-.23-.09L1 11.81a.28.28 0 11.5-.23l5.07 11L18 23.68 13 13a.28.28 0 11.5-.23l5.12 11.12a.28.28 0 01-.09.35z'/%3E%3Cpath fill='url(%23e)' stroke='url(%23f)' stroke-miterlimit='10' stroke-width='.48' d='M13.4 13.12a.25.25 0 01-.14 0L1.25 12a.28.28 0 01-.2-.44L8 1.64a.28.28 0 01.25-.12l12 1.18a.28.28 0 01.2.44L13.51 13a.25.25 0 01-.11.12z'/%3E%3C/svg%3E");position:relative;display:inline-block;padding-right:8px;top:3px;width:28px}.active-sidebar-link{background-color:#ffebff}.active-navbar-link{border-bottom:3px solid #c3f}.header-link-class{margin-left:-24px}.disabled-body{overflow:hidden}</style><meta name="generator" content="Gatsby 2.18.18"/><title data-react-helmet="true"></title><style data-styled="UihHA jAtLxz bCnUTx bAGJfc hJcdbU kOyZtC eCQAUi fsnHHg bXQeSB dsecBh iPgskl bNiGAM gJQTGP fMOzaj" data-styled-version="4.4.1">
/* sc-component-id: links__NavLink-sc-19vgq0o-1 */
.kOyZtC{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .kOyZtC:hover{opacity:.5;}
/* sc-component-id: links__BasicNavLink-sc-19vgq0o-2 */
.eCQAUi{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .eCQAUi:hover{opacity:.5;}
/* sc-component-id: links__SidebarLink-sc-19vgq0o-3 */
.iPgskl{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#fb3b49;padding:10px;-webkit-transition:background-color .3s;transition:background-color .3s;} .iPgskl:hover{background-color:#ffebff;}
/* sc-component-id: Accordion__SectionButton-i8yhwx-0 */
.dsecBh{outline:none;background-color:transparent;cursor:pointer;color:red;border:none;font-size:18px;font-weight:bold;padding:5px 0;-webkit-transition:opacity .5s;transition:opacity .5s;} .dsecBh:after{background:center / contain no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNi41IDEwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZiM2I0OTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnVwLWNhcnJvdDwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNOC4yNS44NWExLjE1LDEuMTUsMCwwLDAtLjgxLjM0bC02LDZBMS4xNSwxLjE1LDAsMCwwLDMuMDYsOC44MUw4LjI1LDMuNjNsNS4xOSw1LjE5YTEuMTUsMS4xNSwwLDAsMCwxLjYzLTEuNjNsLTYtNkExLjE1LDEuMTUsMCwwLDAsOC4yNS44NVoiLz48L3N2Zz4=);content:'';height:11px;width:28px;display:inline-block;} .dsecBh:hover{opacity:.6;}
/* sc-component-id: DocLinks__LinkDesc-sc-1vrw6od-0 */
.bNiGAM{font-size:11px;line-height:1.5;text-transform:lowercase;display:block;font-weight:400;color:#767676;}
/* sc-component-id: Sidebar__Container-gs0c67-0 */
.bXQeSB{border-right:1px solid #86838333;padding:30px;height:100vh;display:none;width:380px;position:-webkit-sticky;position:sticky;overflow:scroll;padding-bottom:200px;top:54px;background-color:#ffffff;} @media screen and (min-width:48em){.bXQeSB{display:block;}}
/* sc-component-id: navbar__Container-kjuegf-0 */
.UihHA{width:100%;border-bottom:1px solid #86838333;position:-webkit-sticky;position:sticky;top:0;background-color:#ffffff;z-index:1;}
/* sc-component-id: navbar__Inner-kjuegf-1 */
.jAtLxz{border-top:3px solid;border-image:linear-gradient(139deg,#fb8817,#ff4b01,#c12127,#e02aff) 3;margin:auto;height:53px;padding:0 30px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}
/* sc-component-id: navbar__Logo-kjuegf-2 */
.bAGJfc{width:120px;padding:0px 5px;height:18px;vertical-align:middle;display:inline-block;-webkit-transition:opacity .5s;transition:opacity .5s;} .bAGJfc:hover{opacity:.8;}
/* sc-component-id: navbar__Links-kjuegf-3 */
.hJcdbU{display:none;} @media screen and (min-width:48em){.hJcdbU{display:block;margin-left:auto;}}
/* sc-component-id: navbar__Heart-kjuegf-4 */
.bCnUTx{font-size:15px;display:inline-block;}
/* sc-component-id: navbar__Hamburger-kjuegf-5 */
.fsnHHg{border:none;background:center no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMzUgMjMiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudCk7fS5jbHMtMntmaWxsOnVybCgjbGluZWFyLWdyYWRpZW50LTIpO30uY2xzLTN7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudC0zKTt9PC9zdHlsZT48bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhci1ncmFkaWVudCIgeTE9IjIiIHgyPSIzNSIgeTI9IjIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBvZmZzZXQ9IjAiIHN0b3AtY29sb3I9IiNmYjg4MTciLz48c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNlMDJhZmYiLz48L2xpbmVhckdyYWRpZW50PjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTIiIHkxPSIxMS41IiB5Mj0iMTEuNSIgeGxpbms6aHJlZj0iI2xpbmVhci1ncmFkaWVudCIvPjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTMiIHkxPSIyMSIgeTI9IjIxIiB4bGluazpocmVmPSIjbGluZWFyLWdyYWRpZW50Ii8+PC9kZWZzPjx0aXRsZT5oYW1idXJnZXI8L3RpdGxlPjxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjxyZWN0IGNsYXNzPSJjbHMtMiIgeT0iOS41IiB3aWR0aD0iMzUiIGhlaWdodD0iNCIgcng9IjIiIHJ5PSIyIi8+PHJlY3QgY2xhc3M9ImNscy0zIiB5PSIxOSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjwvc3ZnPg==);height:30px;width:30px;display:block;margin-left:auto;-webkit-transition:opacity .5s;transition:opacity .5s;cursor:pointer;} .fsnHHg:hover{opacity:.6;} @media screen and (min-width:48em){.fsnHHg{display:none;}}
/* sc-component-id: FoundTypo__Container-sc-1e373sc-0 */
.fMOzaj{margin:80px 0;border-top:1px solid black;padding:20px 0;}
/* sc-component-id: Page__Content-sc-4b62ym-0 */
.gJQTGP{max-width:760px;margin:auto;padding:0 30px 120px;}</style><link rel="icon" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="manifest" href="../../manifest.webmanifest"/><meta name="theme-color" content="#663399"/><link rel="apple-touch-icon" sizes="48x48" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="72x72" href="../../icons/icon-72x72.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="96x96" href="../../icons/icon-96x96.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="144x144" href="../../icons/icon-144x144.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="192x192" href="../../icons/icon-192x192.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="256x256" href="../../icons/icon-256x256.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="384x384" href="../../icons/icon-384x384.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="512x512" href="../../icons/icon-512x512.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2"/><style type="text/css">@font-face{font-family:Poppins;font-style:normal;font-weight:300;src:local('Poppins Light'),local('Poppins-Light'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:400;src:local('Poppins Regular'),local('Poppins-Regular'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:500;src:local('Poppins Medium'),local('Poppins-Medium'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:400;src:local('Inconsolata Regular'),local('Inconsolata-Regular'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5q4.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:700;src:local('Inconsolata Bold'),local('Inconsolata-Bold'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_o.woff) format('woff');font-display: swap;}</style><style type="text/css">
    .header-link-class.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }
    .header-link-class.after {
      display: inline-block;
      padding-left: 4px;
    }
    h1 .header-link-class svg,
    h2 .header-link-class svg,
    h3 .header-link-class svg,
    h4 .header-link-class svg,
    h5 .header-link-class svg,
    h6 .header-link-class svg {
      visibility: hidden;
    }
    h1:hover .header-link-class svg,
    h2:hover .header-link-class svg,
    h3:hover .header-link-class svg,
    h4:hover .header-link-class svg,
    h5:hover .header-link-class svg,
    h6:hover .header-link-class svg,
    h1 .header-link-class:focus svg,
    h2 .header-link-class:focus svg,
    h3 .header-link-class:focus svg,
    h4 .header-link-class:focus svg,
    h5 .header-link-class:focus svg,
    h6 .header-link-class:focus svg {
      visibility: visible;
    }
  </style><script>
    document.addEventListener("DOMContentLoaded", function(event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var offset = element.offsetTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function() {
            window.scrollTo(0, offset - 100)
          }), 0)
        }
      }
    })
  </script><link as="script" rel="preload" href="../../webpack-runtime-b622568e0ef6e093f777.js"/><link as="script" rel="preload" href="../../styles-de5e304580bcba768a01.js"/><link as="script" rel="preload" href="../../commons-4df35f6dbd2fdc25d817.js"/><link as="script" rel="preload" href="../../app-041f7e4f56e7debd8d98.js"/><link as="script" rel="preload" href="../../component---src-templates-page-js-7faf8ceb01991e80d244.js"/><link as="fetch" rel="preload" href="../../page-data/configuring-npm/package-json/page-data.json" crossorigin="anonymous"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" role="group" id="gatsby-focus-wrapper"><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="navbar__Container-kjuegf-0 UihHA css-4cffwv"><div class="navbar__Inner-kjuegf-1 jAtLxz css-4cffwv"><a href="../../"><style data-emotion-css="26z63x">.css-26z63x{box-sizing:border-box;margin:0;min-width:0;margin-left:4px;margin-right:24px;}</style><div class="navbar__Heart-kjuegf-4 bCnUTx css-26z63x">❤</div><style data-emotion-css="9taffg">.css-9taffg{box-sizing:border-box;margin:0;min-width:0;max-width:100%;height:auto;}</style><img src="data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDcgMTciPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMjMxZjIwO30uY2xzLTJ7ZmlsbDpub25lO308L3N0eWxlPjwvZGVmcz48dGl0bGU+Y2xpLWxvZ288L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTS41NCwxMy40aDYuNFYzLjY3aDMuMlYxMy40aDMuMlYuNDJILjU0Wk0zMS4yNi40MnYxM2g2LjRWMy42N2gzLjJWMTMuNGgzLjJWMy42N2gzLjE5VjEzLjRoMy4yVi40MlptLTksMy4yNWgzLjJ2Ni40OUgyMi4zWm0tNi40LDEzaDYuNFYxMy40aDYuNFYuNDJIMTUuOVoiLz48cmVjdCBjbGFzcz0iY2xzLTIiIHg9IjAuNTQiIHk9IjAuNDIiIHdpZHRoPSI0OS45MSIgaGVpZ2h0PSIxNi4yMiIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI2NS41OCAzLjU2IDY1LjU4IDkuODYgNzEuNjYgOS44NiA3MS42NiAxMy4wMiA2NS40NCAxMy4wMiA1OS4yIDEzLjA0IDU5LjIyIDAuNDEgNzEuNjYgMC40MSA3MS42NiAzLjU0IDY1LjU4IDMuNTYiLz48cG9seWdvbiBjbGFzcz0iY2xzLTEiIHBvaW50cz0iODAuNjIgMTAuMjMgODAuNjIgMC4zNiA3NC4yMyAwLjM2IDc0LjIzIDEzLjMgNzYuOTIgMTMuMyA4MC42MiAxMy4zIDg2LjQ3IDEzLjMgODYuNDcgMTAuMjMgODAuNjIgMTAuMjMiLz48cmVjdCBjbGFzcz0iY2xzLTEiIHg9IjEwMS4zMiIgeT0iOC4zNyIgd2lkdGg9IjEuOTkiIGhlaWdodD0iOC4yOSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTE0LjgzIC04OS43OSkgcm90YXRlKDkwKSIvPjxyZWN0IGNsYXNzPSJjbHMtMSIgeD0iODguMzMiIHk9IjAuMzYiIHdpZHRoPSI2LjM5IiBoZWlnaHQ9IjEyLjk0Ii8+PC9zdmc+" class="navbar__Logo-kjuegf-2 bAGJfc css-9taffg"/></a><ul class="navbar__Links-kjuegf-3 hJcdbU"><a class="links__NavLink-sc-19vgq0o-1 kOyZtC" href="../../cli-commands/npm/index.html">docs</a><a href="https://www.npmjs.com/" class="links__BasicNavLink-sc-19vgq0o-2 eCQAUi">npmjs.org</a></ul><button class="navbar__Hamburger-kjuegf-5 fsnHHg"></button></div></div><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-4cffwv"><nav class="Sidebar__Container-gs0c67-0 bXQeSB sidebar"><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">cli-commands</button><div><style data-emotion-css="l3rx45">.css-l3rx45{box-sizing:border-box;margin:0;min-width:0;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm">npm<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">javascript package manager</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-access">npm access<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Set access level on published packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-audit">npm audit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run a security audit</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bin">npm bin<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm bin folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bugs">npm bugs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bugs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-build">npm build<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Build a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bundle">npm bundle<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">REMOVED</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-cache">npm cache<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manipulates packages cache</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ci">npm ci<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-completion">npm completion<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Tab Completion for npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-config">npm config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage the npm configuration files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-dedupe">npm dedupe<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Reduce duplication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-deprecate">npm deprecate<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Deprecate a version of a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-docs">npm docs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Docs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-doctor">npm doctor<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check your environments</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-edit">npm edit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Edit an installed package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-fund">npm fund<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Retrieve funding information</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help">npm help<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Get help on npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help-search">npm help-search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search npm help documentation</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-hook">npm hook<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage registry hooks</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-init">npm init<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">create a package.json file</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install">npm install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-ci-test">npm install-ci-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-test">npm install-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install package(s) and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-link">npm link<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Symlink a package folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-logout">npm logout<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Log out of the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ls">npm ls<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">List installed packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-org">npm org<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-outdated">npm outdated<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check for outdated packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-owner">npm owner<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage package owners</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-pack">npm pack<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Create a tarball from a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ping">npm ping<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Ping npm registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prefix">npm prefix<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display prefix</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-profile">npm profile<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Change settings on your registry profile</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prune">npm prune<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove extraneous packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-publish">npm publish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Publish a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-rebuild">npm rebuild<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Rebuild a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-repo">npm repo<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Open package repository page in the browser</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-restart">npm restart<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Restart a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-root">npm root<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm root</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-run-script">npm run-script<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run arbitrary package scripts</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-search">npm search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search for packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-shrinkwrap">npm shrinkwrap<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Lock down dependency versions for publication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-star">npm star<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Mark your favorite packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stars">npm stars<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View packages marked as favorites</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-start">npm start<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Start a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stop">npm stop<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Stop a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-team">npm team<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage organization teams and team memberships</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-test">npm test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Test a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-token">npm token<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage your authentication tokens</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-uninstall">npm uninstall<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-unpublish">npm unpublish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package from the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-update">npm update<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Update a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-version">npm version<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bump a package version</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-view">npm view<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View registry info</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-whoami">npm whoami<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm username</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">configuring-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/folders">folders<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Folder Structures Used by npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/install">install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Download and install node and npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/npmrc">npmrc<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The npm config files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-lock-json">package-lock.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A manifestation of the manifest</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-locks">package-locks<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">An explanation of npm lockfiles</span></a></div><div class="css-l3rx45"><a aria-current="page" class="links__SidebarLink-sc-19vgq0o-3 iPgskl active-sidebar-link" href="../../configuring-npm/package-json">package.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Specifics of npm&#x27;s package.json handling</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/shrinkwrap-json">shrinkwrap.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A publishable lockfile</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">using-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/config">config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">More than you probably want to know about npm configuration</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/developers">developers<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Developer Guide</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/disputes">disputes<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Handling Module Name Disputes</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/orgs">orgs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Working with Teams &amp; Orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/registry">registry<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The JavaScript Package Registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/removal">removal<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Cleaning the Slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scope">scope<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Scoped packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scripts">scripts<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">How npm handles the &quot;scripts&quot; field</span></a></div></div></div></nav><style data-emotion-css="16vu25q">.css-16vu25q{box-sizing:border-box;margin:0;min-width:0;width:100%;}</style><div class="css-16vu25q"><div class="Page__Content-sc-4b62ym-0 gJQTGP documentation"><div><h1 id="packagejson5" style="position:relative;"><a href="#packagejson5" aria-label="packagejson5 permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>package.json</h1>
<h2 id="specifics-of-npms-packagejson-handling" style="position:relative;"><a href="#specifics-of-npms-packagejson-handling" aria-label="specifics of npms packagejson handling permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Specifics of npm's package.json handling</h2>
<h3 id="description" style="position:relative;"><a href="#description" aria-label="description permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Description</h3>
<p>This document is all you need to know about what's required in your package.json
file.  It must be actual JSON, not just a JavaScript object literal.</p>
<p>A lot of the behavior described in this document is affected by the config
settings described in <a href="../../using-npm/config"><code class="language-text">config</code></a>.</p>
<h3 id="name" style="position:relative;"><a href="#name" aria-label="name permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>name</h3>
<p>If you plan to publish your package, the <em>most</em> important things in your
package.json are the name and version fields as they will be required. The name
and version together form an identifier that is assumed to be completely unique.
Changes to the package should come along with changes to the version. If you don't
plan to publish your package, the name and version fields are optional.</p>
<p>The name is what your thing is called.</p>
<p>Some rules:</p>
<ul>
<li>The name must be less than or equal to 214 characters. This includes the scope for
scoped packages.</li>
<li>The names of scoped packages can begin with a dot or an underscore. This is not permitted without a scope.</li>
<li>New packages must not have uppercase letters in the name.</li>
<li>The name ends up being part of a URL, an argument on the command line, and a
folder name. Therefore, the name can't contain any non-URL-safe characters.</li>
</ul>
<p>Some tips:</p>
<ul>
<li>Don't use the same name as a core Node module.</li>
<li>Don't put "js" or "node" in the name.  It's assumed that it's js, since you're
writing a package.json file, and you can specify the engine using the "engines"
field.  (See below.)</li>
<li>The name will probably be passed as an argument to require(), so it should
be something short, but also reasonably descriptive.</li>
<li>You may want to check the npm registry to see if there's something by that name
already, before you get too attached to it. <a href="https://www.npmjs.com/">https://www.npmjs.com/</a></li>
</ul>
<p>A name can be optionally prefixed by a scope, e.g. <code class="language-text">@myorg/mypackage</code>. See
<a href="../../using-npm/scope"><code class="language-text">scope</code></a> for more detail.</p>
<h3 id="version" style="position:relative;"><a href="#version" aria-label="version permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>version</h3>
<p>If you plan to publish your package, the <em>most</em> important things in your
package.json are the name and version fields as they will be required. The name
and version together form an identifier that is assumed to be completely unique.
Changes to the package should come along with changes to the version. If you don't
plan to publish your package, the name and version fields are optional.</p>
<p>Version must be parseable by
<a href="https://github.com/isaacs/node-semver">node-semver</a>, which is bundled
with npm as a dependency.  (<code class="language-text">npm install semver</code> to use it yourself.)</p>
<p>More on version numbers and ranges at <a href="../../using-npm/semver">semver</a>.</p>
<h3 id="description-1" style="position:relative;"><a href="#description-1" aria-label="description 1 permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>description</h3>
<p>Put a description in it.  It's a string.  This helps people discover your
package, as it's listed in <code class="language-text">npm search</code>.</p>
<h3 id="keywords" style="position:relative;"><a href="#keywords" aria-label="keywords permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>keywords</h3>
<p>Put keywords in it.  It's an array of strings.  This helps people
discover your package as it's listed in <code class="language-text">npm search</code>.</p>
<h3 id="homepage" style="position:relative;"><a href="#homepage" aria-label="homepage permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>homepage</h3>
<p>The url to the project homepage.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"homepage"</span><span class="token operator">:</span> <span class="token string">"https://github.com/owner/project#readme"</span></code></pre></div>
<h3 id="bugs" style="position:relative;"><a href="#bugs" aria-label="bugs permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>bugs</h3>
<p>The url to your project's issue tracker and / or the email address to which
issues should be reported. These are helpful for people who encounter issues
with your package.</p>
<p>It should look like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"https://github.com/owner/project/issues"</span>
<span class="token punctuation">,</span> <span class="token property">"email"</span> <span class="token operator">:</span> <span class="token string">"<EMAIL>"</span>
<span class="token punctuation">}</span></code></pre></div>
<p>You can specify either one or both values. If you want to provide only a url,
you can specify the value for "bugs" as a simple string instead of an object.</p>
<p>If a url is provided, it will be used by the <code class="language-text">npm bugs</code> command.</p>
<h3 id="license" style="position:relative;"><a href="#license" aria-label="license permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>license</h3>
<p>You should specify a license for your package so that people know how they are
permitted to use it, and any restrictions you're placing on it.</p>
<p>If you're using a common license such as BSD-2-Clause or MIT, add a
current SPDX license identifier for the license you're using, like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"license"</span> <span class="token operator">:</span> <span class="token string">"BSD-3-Clause"</span> <span class="token punctuation">}</span></code></pre></div>
<p>You can check <a href="https://spdx.org/licenses/">the full list of SPDX license IDs</a>.
Ideally you should pick one that is
<a href="https://opensource.org/licenses/alphabetical">OSI</a> approved.</p>
<p>If your package is licensed under multiple common licenses, use an <a href="https://www.npmjs.com/package/spdx">SPDX license
expression syntax version 2.0 string</a>, like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"license"</span> <span class="token operator">:</span> <span class="token string">"(ISC OR GPL-3.0)"</span> <span class="token punctuation">}</span></code></pre></div>
<p>If you are using a license that hasn't been assigned an SPDX identifier, or if
you are using a custom license, use a string value like this one:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"license"</span> <span class="token operator">:</span> <span class="token string">"SEE LICENSE IN &lt;filename>"</span> <span class="token punctuation">}</span></code></pre></div>
<p>Then include a file named <code class="language-text">&lt;filename&gt;</code> at the top level of the package.</p>
<p>Some old packages used license objects or a "licenses" property containing an
array of license objects:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token comment">// Not valid metadata</span>
<span class="token punctuation">{</span> <span class="token property">"license"</span> <span class="token operator">:</span>
  <span class="token punctuation">{</span> <span class="token property">"type"</span> <span class="token operator">:</span> <span class="token string">"ISC"</span>
  <span class="token punctuation">,</span> <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"https://opensource.org/licenses/ISC"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span>

<span class="token comment">// Not valid metadata</span>
<span class="token punctuation">{</span> <span class="token property">"licenses"</span> <span class="token operator">:</span>
  <span class="token punctuation">[</span>
    <span class="token punctuation">{</span> <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"MIT"</span>
    <span class="token punctuation">,</span> <span class="token property">"url"</span><span class="token operator">:</span> <span class="token string">"https://www.opensource.org/licenses/mit-license.php"</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">,</span> <span class="token punctuation">{</span> <span class="token property">"type"</span><span class="token operator">:</span> <span class="token string">"Apache-2.0"</span>
    <span class="token punctuation">,</span> <span class="token property">"url"</span><span class="token operator">:</span> <span class="token string">"https://opensource.org/licenses/apache2.0.php"</span>
    <span class="token punctuation">}</span>
  <span class="token punctuation">]</span>
<span class="token punctuation">}</span></code></pre></div>
<p>Those styles are now deprecated. Instead, use SPDX expressions, like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"license"</span><span class="token operator">:</span> <span class="token string">"ISC"</span> <span class="token punctuation">}</span>

<span class="token punctuation">{</span> <span class="token property">"license"</span><span class="token operator">:</span> <span class="token string">"(MIT OR Apache-2.0)"</span> <span class="token punctuation">}</span></code></pre></div>
<p>Finally, if you do not wish to grant others the right to use a private or
unpublished package under any terms:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"license"</span><span class="token operator">:</span> <span class="token string">"UNLICENSED"</span> <span class="token punctuation">}</span></code></pre></div>
<p>Consider also setting <code class="language-text">&quot;private&quot;: true</code> to prevent accidental publication.</p>
<h3 id="people-fields-author-contributors" style="position:relative;"><a href="#people-fields-author-contributors" aria-label="people fields author contributors permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>people fields: author, contributors</h3>
<p>The "author" is one person.  "contributors" is an array of people.  A "person"
is an object with a "name" field and optionally "url" and "email", like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span> <span class="token operator">:</span> <span class="token string">"Barney Rubble"</span>
<span class="token punctuation">,</span> <span class="token property">"email"</span> <span class="token operator">:</span> <span class="token string">"<EMAIL>"</span>
<span class="token punctuation">,</span> <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"http://barnyrubble.tumblr.com/"</span>
<span class="token punctuation">}</span></code></pre></div>
<p>Or you can shorten that all into a single string, and npm will parse it for you:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token string">"Barney Rubble &lt;<EMAIL>> (http://barnyrubble.tumblr.com/)"</span></code></pre></div>
<p>Both email and url are optional either way.</p>
<p>npm also sets a top-level "maintainers" field with your npm user info.</p>
<h3 id="funding" style="position:relative;"><a href="#funding" aria-label="funding permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>funding</h3>
<p>You can specify an object containing an URL that provides up-to-date
information about ways to help fund development of your package, or
a string URL, or an array of these:</p>
<div class="gatsby-highlight" data-language="text"><pre class="language-text"><code class="language-text">&quot;funding&quot;: {
  &quot;type&quot; : &quot;individual&quot;,
  &quot;url&quot; : &quot;http://example.com/donate&quot;
}

&quot;funding&quot;: {
  &quot;type&quot; : &quot;patreon&quot;,
  &quot;url&quot; : &quot;https://www.patreon.com/my-account&quot;
}

&quot;funding&quot;: &quot;http://example.com/donate&quot;

&quot;funding&quot;: [
  {
    &quot;type&quot; : &quot;individual&quot;,
    &quot;url&quot; : &quot;http://example.com/donate&quot;
  },
  &quot;http://example.com/donateAlso&quot;,
  {
    &quot;type&quot; : &quot;patreon&quot;,
    &quot;url&quot; : &quot;https://www.patreon.com/my-account&quot;
  }
]</code></pre></div>
<p>Users can use the <code class="language-text">npm fund</code> subcommand to list the <code class="language-text">funding</code> URLs of all
dependencies of their project, direct and indirect. A shortcut to visit each
funding url is also available when providing the project name such as:
<code class="language-text">npm fund &lt;projectname&gt;</code> (when there are multiple URLs, the first one will be
visited)</p>
<h3 id="files" style="position:relative;"><a href="#files" aria-label="files permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>files</h3>
<p>The optional <code class="language-text">files</code> field is an array of file patterns that describes
the entries to be included when your package is installed as a
dependency. File patterns follow a similar syntax to <code class="language-text">.gitignore</code>, but
reversed: including a file, directory, or glob pattern (<code class="language-text">*</code>, <code class="language-text">**/*</code>, and such)
will make it so that file is included in the tarball when it's packed. Omitting
the field will make it default to <code class="language-text">[&quot;*&quot;]</code>, which means it will include all files.</p>
<p>Some special files and directories are also included or excluded regardless of
whether they exist in the <code class="language-text">files</code> array (see below).</p>
<p>You can also provide a <code class="language-text">.npmignore</code> file in the root of your package or
in subdirectories, which will keep files from being included. At the
root of your package it will not override the "files" field, but in
subdirectories it will. The <code class="language-text">.npmignore</code> file works just like a
<code class="language-text">.gitignore</code>. If there is a <code class="language-text">.gitignore</code> file, and <code class="language-text">.npmignore</code> is
missing, <code class="language-text">.gitignore</code>'s contents will be used instead.</p>
<p>Files included with the "package.json#files" field <em>cannot</em> be excluded
through <code class="language-text">.npmignore</code> or <code class="language-text">.gitignore</code>.</p>
<p>Certain files are always included, regardless of settings:</p>
<ul>
<li><code class="language-text">package.json</code></li>
<li><code class="language-text">README</code></li>
<li><code class="language-text">CHANGES</code> / <code class="language-text">CHANGELOG</code> / <code class="language-text">HISTORY</code></li>
<li><code class="language-text">LICENSE</code> / <code class="language-text">LICENCE</code></li>
<li><code class="language-text">NOTICE</code></li>
<li>The file in the "main" field</li>
</ul>
<p><code class="language-text">README</code>, <code class="language-text">CHANGES</code>, <code class="language-text">LICENSE</code> &#x26; <code class="language-text">NOTICE</code> can have any case and extension.</p>
<p>Conversely, some files are always ignored:</p>
<ul>
<li><code class="language-text">.git</code></li>
<li><code class="language-text">CVS</code></li>
<li><code class="language-text">.svn</code></li>
<li><code class="language-text">.hg</code></li>
<li><code class="language-text">.lock-wscript</code></li>
<li><code class="language-text">.wafpickle-N</code></li>
<li><code class="language-text">.*.swp</code></li>
<li><code class="language-text">.DS_Store</code></li>
<li><code class="language-text">._*</code></li>
<li><code class="language-text">npm-debug.log</code></li>
<li><code class="language-text">.npmrc</code></li>
<li><code class="language-text">node_modules</code></li>
<li><code class="language-text">config.gypi</code></li>
<li><code class="language-text">*.orig</code></li>
<li><code class="language-text">package-lock.json</code> (use shrinkwrap instead)</li>
</ul>
<h3 id="main" style="position:relative;"><a href="#main" aria-label="main permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>main</h3>
<p>The main field is a module ID that is the primary entry point to your program.
That is, if your package is named <code class="language-text">foo</code>, and a user installs it, and then does
<code class="language-text">require(&quot;foo&quot;)</code>, then your main module's exports object will be returned.</p>
<p>This should be a module ID relative to the root of your package folder.</p>
<p>For most modules, it makes the most sense to have a main script and often not
much else.</p>
<h3 id="browser" style="position:relative;"><a href="#browser" aria-label="browser permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>browser</h3>
<p>If your module is meant to be used client-side the browser field should be
used instead of the main field. This is helpful to hint users that it might
rely on primitives that aren't available in Node.js modules. (e.g. <code class="language-text">window</code>)</p>
<h3 id="bin" style="position:relative;"><a href="#bin" aria-label="bin permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>bin</h3>
<p>A lot of packages have one or more executable files that they'd like to
install into the PATH. npm makes this pretty easy (in fact, it uses this
feature to install the "npm" executable.)</p>
<p>To use this, supply a <code class="language-text">bin</code> field in your package.json which is a map of
command name to local file name. On install, npm will symlink that file into
<code class="language-text">prefix/bin</code> for global installs, or <code class="language-text">./node_modules/.bin/</code> for local
installs.</p>
<p>For example, myapp could have this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"bin"</span> <span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token property">"myapp"</span> <span class="token operator">:</span> <span class="token string">"./cli.js"</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span></code></pre></div>
<p>So, when you install myapp, it'll create a symlink from the <code class="language-text">cli.js</code> script to
<code class="language-text">/usr/local/bin/myapp</code>.</p>
<p>If you have a single executable, and its name should be the name
of the package, then you can just supply it as a string.  For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"my-program"</span>
<span class="token punctuation">,</span> <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"1.2.5"</span>
<span class="token punctuation">,</span> <span class="token property">"bin"</span><span class="token operator">:</span> <span class="token string">"./path/to/program"</span> <span class="token punctuation">}</span></code></pre></div>
<p>would be the same as this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"my-program"</span>
<span class="token punctuation">,</span> <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"1.2.5"</span>
<span class="token punctuation">,</span> <span class="token property">"bin"</span> <span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token property">"my-program"</span> <span class="token operator">:</span> <span class="token string">"./path/to/program"</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span></code></pre></div>
<p>Please make sure that your file(s) referenced in <code class="language-text">bin</code> starts with
<code class="language-text">#!/usr/bin/env node</code>, otherwise the scripts are started without the node
executable!</p>
<h3 id="man" style="position:relative;"><a href="#man" aria-label="man permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>man</h3>
<p>Specify either a single file or an array of filenames to put in place for the
<code class="language-text">man</code> program to find.</p>
<p>If only a single file is provided, then it's installed such that it is the
result from <code class="language-text">man &lt;pkgname&gt;</code>, regardless of its actual filename.  For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span> <span class="token operator">:</span> <span class="token string">"foo"</span>
<span class="token punctuation">,</span> <span class="token property">"version"</span> <span class="token operator">:</span> <span class="token string">"1.2.3"</span>
<span class="token punctuation">,</span> <span class="token property">"description"</span> <span class="token operator">:</span> <span class="token string">"A packaged foo fooer for fooing foos"</span>
<span class="token punctuation">,</span> <span class="token property">"main"</span> <span class="token operator">:</span> <span class="token string">"foo.js"</span>
<span class="token punctuation">,</span> <span class="token property">"man"</span> <span class="token operator">:</span> <span class="token string">"./man/doc.1"</span>
<span class="token punctuation">}</span></code></pre></div>
<p>would link the <code class="language-text">./man/doc.1</code> file in such that it is the target for <code class="language-text">man foo</code></p>
<p>If the filename doesn't start with the package name, then it's prefixed.
So, this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span> <span class="token operator">:</span> <span class="token string">"foo"</span>
<span class="token punctuation">,</span> <span class="token property">"version"</span> <span class="token operator">:</span> <span class="token string">"1.2.3"</span>
<span class="token punctuation">,</span> <span class="token property">"description"</span> <span class="token operator">:</span> <span class="token string">"A packaged foo fooer for fooing foos"</span>
<span class="token punctuation">,</span> <span class="token property">"main"</span> <span class="token operator">:</span> <span class="token string">"foo.js"</span>
<span class="token punctuation">,</span> <span class="token property">"man"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"./man/foo.1"</span><span class="token punctuation">,</span> <span class="token string">"./man/bar.1"</span> <span class="token punctuation">]</span>
<span class="token punctuation">}</span></code></pre></div>
<p>will create files to do <code class="language-text">man foo</code> and <code class="language-text">man foo-bar</code>.</p>
<p>Man files must end with a number, and optionally a <code class="language-text">.gz</code> suffix if they are
compressed.  The number dictates which man section the file is installed into.</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span> <span class="token operator">:</span> <span class="token string">"foo"</span>
<span class="token punctuation">,</span> <span class="token property">"version"</span> <span class="token operator">:</span> <span class="token string">"1.2.3"</span>
<span class="token punctuation">,</span> <span class="token property">"description"</span> <span class="token operator">:</span> <span class="token string">"A packaged foo fooer for fooing foos"</span>
<span class="token punctuation">,</span> <span class="token property">"main"</span> <span class="token operator">:</span> <span class="token string">"foo.js"</span>
<span class="token punctuation">,</span> <span class="token property">"man"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"./man/foo.1"</span><span class="token punctuation">,</span> <span class="token string">"./man/foo.2"</span> <span class="token punctuation">]</span>
<span class="token punctuation">}</span></code></pre></div>
<p>will create entries for <code class="language-text">man foo</code> and <code class="language-text">man 2 foo</code></p>
<h3 id="directories" style="position:relative;"><a href="#directories" aria-label="directories permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories</h3>
<p>The CommonJS <a href="http://wiki.commonjs.org/wiki/Packages/1.0">Packages</a> spec details a
few ways that you can indicate the structure of your package using a <code class="language-text">directories</code>
object. If you look at <a href="https://registry.npmjs.org/npm/latest">npm's package.json</a>,
you'll see that it has directories for doc, lib, and man.</p>
<p>In the future, this information may be used in other creative ways.</p>
<h4 id="directorieslib" style="position:relative;"><a href="#directorieslib" aria-label="directorieslib permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.lib</h4>
<p>Tell people where the bulk of your library is.  Nothing special is done
with the lib folder in any way, but it's useful meta info.</p>
<h4 id="directoriesbin" style="position:relative;"><a href="#directoriesbin" aria-label="directoriesbin permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.bin</h4>
<p>If you specify a <code class="language-text">bin</code> directory in <code class="language-text">directories.bin</code>, all the files in
that folder will be added.</p>
<p>Because of the way the <code class="language-text">bin</code> directive works, specifying both a
<code class="language-text">bin</code> path and setting <code class="language-text">directories.bin</code> is an error. If you want to
specify individual files, use <code class="language-text">bin</code>, and for all the files in an
existing <code class="language-text">bin</code> directory, use <code class="language-text">directories.bin</code>.</p>
<h4 id="directoriesman" style="position:relative;"><a href="#directoriesman" aria-label="directoriesman permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.man</h4>
<p>A folder that is full of man pages.  Sugar to generate a "man" array by
walking the folder.</p>
<h4 id="directoriesdoc" style="position:relative;"><a href="#directoriesdoc" aria-label="directoriesdoc permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.doc</h4>
<p>Put markdown files in here.  Eventually, these will be displayed nicely,
maybe, someday.</p>
<h4 id="directoriesexample" style="position:relative;"><a href="#directoriesexample" aria-label="directoriesexample permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.example</h4>
<p>Put example scripts in here.  Someday, it might be exposed in some clever way.</p>
<h4 id="directoriestest" style="position:relative;"><a href="#directoriestest" aria-label="directoriestest permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>directories.test</h4>
<p>Put your tests in here. It is currently not exposed, but it might be in the
future.</p>
<h3 id="repository" style="position:relative;"><a href="#repository" aria-label="repository permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>repository</h3>
<p>Specify the place where your code lives. This is helpful for people who
want to contribute.  If the git repo is on GitHub, then the <code class="language-text">npm docs</code>
command will be able to find you.</p>
<p>Do it like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"repository"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
  <span class="token property">"type"</span> <span class="token operator">:</span> <span class="token string">"git"</span><span class="token punctuation">,</span>
  <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"https://github.com/npm/cli.git"</span>
<span class="token punctuation">}</span>

<span class="token property">"repository"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
  <span class="token property">"type"</span> <span class="token operator">:</span> <span class="token string">"svn"</span><span class="token punctuation">,</span>
  <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"https://v8.googlecode.com/svn/trunk/"</span>
<span class="token punctuation">}</span></code></pre></div>
<p>The URL should be a publicly available (perhaps read-only) url that can be handed
directly to a VCS program without any modification.  It should not be a url to an
html project page that you put in your browser.  It's for computers.</p>
<p>For GitHub, GitHub gist, Bitbucket, or GitLab repositories you can use the same
shortcut syntax you use for <code class="language-text">npm install</code>:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"repository"</span><span class="token operator">:</span> <span class="token string">"npm/npm"</span>

<span class="token property">"repository"</span><span class="token operator">:</span> <span class="token string">"github:user/repo"</span>

<span class="token property">"repository"</span><span class="token operator">:</span> <span class="token string">"gist:11081aaa281"</span>

<span class="token property">"repository"</span><span class="token operator">:</span> <span class="token string">"bitbucket:user/repo"</span>

<span class="token property">"repository"</span><span class="token operator">:</span> <span class="token string">"gitlab:user/repo"</span></code></pre></div>
<p>If the <code class="language-text">package.json</code> for your package is not in the root directory (for example
if it is part of a monorepo), you can specify the directory in which it lives:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"repository"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
  <span class="token property">"type"</span> <span class="token operator">:</span> <span class="token string">"git"</span><span class="token punctuation">,</span>
  <span class="token property">"url"</span> <span class="token operator">:</span> <span class="token string">"https://github.com/facebook/react.git"</span><span class="token punctuation">,</span>
  <span class="token property">"directory"</span><span class="token operator">:</span> <span class="token string">"packages/react-dom"</span>
<span class="token punctuation">}</span></code></pre></div>
<h3 id="scripts" style="position:relative;"><a href="#scripts" aria-label="scripts permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>scripts</h3>
<p>The "scripts" property is a dictionary containing script commands that are run
at various times in the lifecycle of your package.  The key is the lifecycle
event, and the value is the command to run at that point.</p>
<p>See <a href="../../using-npm/scripts"><code class="language-text">scripts</code></a> to find out more about writing package scripts.</p>
<h3 id="config" style="position:relative;"><a href="#config" aria-label="config permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>config</h3>
<p>A "config" object can be used to set configuration parameters used in package
scripts that persist across upgrades.  For instance, if a package had the
following:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span> <span class="token operator">:</span> <span class="token string">"foo"</span>
<span class="token punctuation">,</span> <span class="token property">"config"</span> <span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token property">"port"</span> <span class="token operator">:</span> <span class="token string">"8080"</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span></code></pre></div>
<p>and then had a "start" command that then referenced the
<code class="language-text">npm_package_config_port</code> environment variable, then the user could
override that by doing <code class="language-text">npm config set foo:port 8001</code>.</p>
<p>See <a href="../../using-npm/config"><code class="language-text">config</code></a> and <a href="../../using-npm/scripts"><code class="language-text">scripts</code></a> for more on package
configs.</p>
<h3 id="dependencies" style="position:relative;"><a href="#dependencies" aria-label="dependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>dependencies</h3>
<p>Dependencies are specified in a simple object that maps a package name to a
version range. The version range is a string which has one or more
space-separated descriptors.  Dependencies can also be identified with a
tarball or git URL.</p>
<p><strong>Please do not put test harnesses or transpilers in your
<code class="language-text">dependencies</code> object.</strong>  See <code class="language-text">devDependencies</code>, below.</p>
<p>See <a href="../../using-npm/semver">semver</a> for more details about specifying version ranges.</p>
<ul>
<li><code class="language-text">version</code> Must match <code class="language-text">version</code> exactly</li>
<li><code class="language-text">&gt;version</code> Must be greater than <code class="language-text">version</code></li>
<li><code class="language-text">&gt;=version</code> etc</li>
<li><code class="language-text">&lt;version</code></li>
<li><code class="language-text">&lt;=version</code></li>
<li><code class="language-text">~version</code> "Approximately equivalent to version"  See <a href="../../using-npm/semver">semver</a></li>
<li><code class="language-text">^version</code> "Compatible with version"  See <a href="../../using-npm/semver">semver</a></li>
<li><code class="language-text">1.2.x</code> 1.2.0, 1.2.1, etc., but not 1.3.0</li>
<li><code class="language-text">http://...</code> See 'URLs as Dependencies' below</li>
<li><code class="language-text">*</code> Matches any version</li>
<li><code class="language-text">&quot;&quot;</code> (just an empty string) Same as <code class="language-text">*</code></li>
<li><code class="language-text">version1 - version2</code> Same as <code class="language-text">&gt;=version1 &lt;=version2</code>.</li>
<li><code class="language-text">range1 || range2</code> Passes if either range1 or range2 are satisfied.</li>
<li><code class="language-text">git...</code> See 'Git URLs as Dependencies' below</li>
<li><code class="language-text">user/repo</code> See 'GitHub URLs' below</li>
<li><code class="language-text">tag</code> A specific version tagged and published as <code class="language-text">tag</code>  See <a href="../../cli-commands/dist-tag"><code class="language-text">npm dist-tag</code></a></li>
<li><code class="language-text">path/path/path</code> See <a href="#local-paths">Local Paths</a> below</li>
</ul>
<p>For example, these are all valid:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"dependencies"</span> <span class="token operator">:</span>
  <span class="token punctuation">{</span> <span class="token property">"foo"</span> <span class="token operator">:</span> <span class="token string">"1.0.0 - 2.9999.9999"</span>
  <span class="token punctuation">,</span> <span class="token property">"bar"</span> <span class="token operator">:</span> <span class="token string">">=1.0.2 &lt;2.1.2"</span>
  <span class="token punctuation">,</span> <span class="token property">"baz"</span> <span class="token operator">:</span> <span class="token string">">1.0.2 &lt;=2.3.4"</span>
  <span class="token punctuation">,</span> <span class="token property">"boo"</span> <span class="token operator">:</span> <span class="token string">"2.0.1"</span>
  <span class="token punctuation">,</span> <span class="token property">"qux"</span> <span class="token operator">:</span> <span class="token string">"&lt;1.0.0 || >=2.3.1 &lt;2.4.5 || >=2.5.2 &lt;3.0.0"</span>
  <span class="token punctuation">,</span> <span class="token property">"asd"</span> <span class="token operator">:</span> <span class="token string">"http://asdf.com/asdf.tar.gz"</span>
  <span class="token punctuation">,</span> <span class="token property">"til"</span> <span class="token operator">:</span> <span class="token string">"~1.2"</span>
  <span class="token punctuation">,</span> <span class="token property">"elf"</span> <span class="token operator">:</span> <span class="token string">"~1.2.3"</span>
  <span class="token punctuation">,</span> <span class="token property">"two"</span> <span class="token operator">:</span> <span class="token string">"2.x"</span>
  <span class="token punctuation">,</span> <span class="token property">"thr"</span> <span class="token operator">:</span> <span class="token string">"3.3.x"</span>
  <span class="token punctuation">,</span> <span class="token property">"lat"</span> <span class="token operator">:</span> <span class="token string">"latest"</span>
  <span class="token punctuation">,</span> <span class="token property">"dyl"</span> <span class="token operator">:</span> <span class="token string">"file:../dyl"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span></code></pre></div>
<h4 id="urls-as-dependencies" style="position:relative;"><a href="#urls-as-dependencies" aria-label="urls as dependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>URLs as Dependencies</h4>
<p>You may specify a tarball URL in place of a version range.</p>
<p>This tarball will be downloaded and installed locally to your package at
install time.</p>
<h4 id="git-urls-as-dependencies" style="position:relative;"><a href="#git-urls-as-dependencies" aria-label="git urls as dependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Git URLs as Dependencies</h4>
<p>Git urls are of the form:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token operator">&lt;</span>protocol<span class="token operator">></span>://<span class="token punctuation">[</span><span class="token operator">&lt;</span>user<span class="token operator">></span><span class="token punctuation">[</span>:<span class="token operator">&lt;</span>password<span class="token operator">></span><span class="token punctuation">]</span>@<span class="token punctuation">]</span><span class="token operator">&lt;</span>hostname<span class="token operator">></span><span class="token punctuation">[</span>:<span class="token operator">&lt;</span>port<span class="token operator">></span><span class="token punctuation">]</span><span class="token punctuation">[</span>:<span class="token punctuation">]</span><span class="token punctuation">[</span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>path<span class="token operator">></span><span class="token punctuation">[</span><span class="token comment">#&lt;commit-ish> | #semver:&lt;semver>]</span></code></pre></div>
<p><code class="language-text">&lt;protocol&gt;</code> is one of <code class="language-text">git</code>, <code class="language-text">git+ssh</code>, <code class="language-text">git+http</code>, <code class="language-text">git+https</code>, or
<code class="language-text">git+file</code>.</p>
<p>If <code class="language-text">#&lt;commit-ish&gt;</code> is provided, it will be used to clone exactly that
commit. If the commit-ish has the format <code class="language-text">#semver:&lt;semver&gt;</code>, <code class="language-text">&lt;semver&gt;</code> can
be any valid semver range or exact version, and npm will look for any tags
or refs matching that range in the remote repository, much as it would for a
registry dependency. If neither <code class="language-text">#&lt;commit-ish&gt;</code> or <code class="language-text">#semver:&lt;semver&gt;</code> is
specified, then <code class="language-text">master</code> is used.</p>
<p>Examples:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">git+ssh://**************:npm/cli.git<span class="token comment">#v1.0.27</span>
git+ssh://**************:npm/cli<span class="token comment">#semver:^5.0</span>
git+https://<EMAIL>/npm/cli.git
git://github.com/npm/cli.git<span class="token comment">#v1.0.27</span></code></pre></div>
<h4 id="github-urls" style="position:relative;"><a href="#github-urls" aria-label="github urls permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>GitHub URLs</h4>
<p>As of version 1.1.65, you can refer to GitHub urls as just "foo":
"user/foo-project".  Just as with git URLs, a <code class="language-text">commit-ish</code> suffix can be
included.  For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span>
  <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"foo"</span><span class="token punctuation">,</span>
  <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"0.0.0"</span><span class="token punctuation">,</span>
  <span class="token property">"dependencies"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"express"</span><span class="token operator">:</span> <span class="token string">"expressjs/express"</span><span class="token punctuation">,</span>
    <span class="token property">"mocha"</span><span class="token operator">:</span> <span class="token string">"mochajs/mocha#4727d357ea"</span><span class="token punctuation">,</span>
    <span class="token property">"module"</span><span class="token operator">:</span> <span class="token string">"user/repo#feature\/branch"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span></code></pre></div>
<h4 id="local-paths" style="position:relative;"><a href="#local-paths" aria-label="local paths permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Local Paths</h4>
<p>As of version 2.0.0 you can provide a path to a local directory that contains a
package. Local paths can be saved using <code class="language-text">npm install -S</code> or
<code class="language-text">npm install --save</code>, using any of these forms:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token punctuation">..</span>/foo/bar
~/foo/bar
./foo/bar
/foo/bar</code></pre></div>
<p>in which case they will be normalized to a relative path and added to your
<code class="language-text">package.json</code>. For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span>
  <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"baz"</span><span class="token punctuation">,</span>
  <span class="token property">"dependencies"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"bar"</span><span class="token operator">:</span> <span class="token string">"file:../foo/bar"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span></code></pre></div>
<p>This feature is helpful for local offline development and creating
tests that require npm installing where you don't want to hit an
external server, but should not be used when publishing packages
to the public registry.</p>
<h3 id="devdependencies" style="position:relative;"><a href="#devdependencies" aria-label="devdependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>devDependencies</h3>
<p>If someone is planning on downloading and using your module in their
program, then they probably don't want or need to download and build
the external test or documentation framework that you use.</p>
<p>In this case, it's best to map these additional items in a <code class="language-text">devDependencies</code>
object.</p>
<p>These things will be installed when doing <code class="language-text">npm link</code> or <code class="language-text">npm install</code>
from the root of a package, and can be managed like any other npm
configuration param.  See <a href="../../using-npm/config"><code class="language-text">config</code></a> for more on the topic.</p>
<p>For build steps that are not platform-specific, such as compiling
CoffeeScript or other languages to JavaScript, use the <code class="language-text">prepare</code>
script to do this, and make the required package a devDependency.</p>
<p>For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"ethopia-waza"</span><span class="token punctuation">,</span>
  <span class="token property">"description"</span><span class="token operator">:</span> <span class="token string">"a delightfully fruity coffee varietal"</span><span class="token punctuation">,</span>
  <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"1.2.3"</span><span class="token punctuation">,</span>
  <span class="token property">"devDependencies"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"coffee-script"</span><span class="token operator">:</span> <span class="token string">"~1.6.3"</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token property">"scripts"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"prepare"</span><span class="token operator">:</span> <span class="token string">"coffee -o lib/ -c src/waza.coffee"</span>
  <span class="token punctuation">}</span><span class="token punctuation">,</span>
  <span class="token property">"main"</span><span class="token operator">:</span> <span class="token string">"lib/waza.js"</span>
<span class="token punctuation">}</span></code></pre></div>
<p>The <code class="language-text">prepare</code> script will be run before publishing, so that users
can consume the functionality without requiring them to compile it
themselves.  In dev mode (ie, locally running <code class="language-text">npm install</code>), it'll
run this script as well, so that you can test it easily.</p>
<h3 id="peerdependencies" style="position:relative;"><a href="#peerdependencies" aria-label="peerdependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>peerDependencies</h3>
<p>In some cases, you want to express the compatibility of your package with a
host tool or library, while not necessarily doing a <code class="language-text">require</code> of this host.
This is usually referred to as a <em>plugin</em>. Notably, your module may be exposing
a specific interface, expected and specified by the host documentation.</p>
<p>For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span>
  <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"tea-latte"</span><span class="token punctuation">,</span>
  <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"1.3.5"</span><span class="token punctuation">,</span>
  <span class="token property">"peerDependencies"</span><span class="token operator">:</span> <span class="token punctuation">{</span>
    <span class="token property">"tea"</span><span class="token operator">:</span> <span class="token string">"2.x"</span>
  <span class="token punctuation">}</span>
<span class="token punctuation">}</span></code></pre></div>
<p>This ensures your package <code class="language-text">tea-latte</code> can be installed <em>along</em> with the second
major version of the host package <code class="language-text">tea</code> only. <code class="language-text">npm install tea-latte</code> could
possibly yield the following dependency graph:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">├── tea-latte@1.3.5
└── tea@2.2.0</code></pre></div>
<p><strong>NOTE: npm versions 1 and 2 will automatically install <code class="language-text">peerDependencies</code> if
they are not explicitly depended upon higher in the dependency tree. In the
next major version of npm (npm@3), this will no longer be the case. You will
receive a warning that the peerDependency is not installed instead.</strong> The
behavior in npms 1 &#x26; 2 was frequently confusing and could easily put you into
dependency hell, a situation that npm is designed to avoid as much as possible.</p>
<p>Trying to install another plugin with a conflicting requirement will cause an
error. For this reason, make sure your plugin requirement is as broad as
possible, and not to lock it down to specific patch versions.</p>
<p>Assuming the host complies with <a href="https://semver.org/">semver</a>, only changes in
the host package's major version will break your plugin. Thus, if you've worked
with every 1.x version of the host package, use <code class="language-text">&quot;^1.0&quot;</code> or <code class="language-text">&quot;1.x&quot;</code> to express
this. If you depend on features introduced in 1.5.2, use <code class="language-text">&quot;&gt;= 1.5.2 &lt; 2&quot;</code>.</p>
<h3 id="bundleddependencies" style="position:relative;"><a href="#bundleddependencies" aria-label="bundleddependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>bundledDependencies</h3>
<p>This defines an array of package names that will be bundled when publishing
the package.</p>
<p>In cases where you need to preserve npm packages locally or have them
available through a single file download, you can bundle the packages in a
tarball file by specifying the package names in the <code class="language-text">bundledDependencies</code>
array and executing <code class="language-text">npm pack</code>.</p>
<p>For example:</p>
<p>If we define a package.json like this:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span>
  <span class="token property">"name"</span><span class="token operator">:</span> <span class="token string">"awesome-web-framework"</span><span class="token punctuation">,</span>
  <span class="token property">"version"</span><span class="token operator">:</span> <span class="token string">"1.0.0"</span><span class="token punctuation">,</span>
  <span class="token property">"bundledDependencies"</span><span class="token operator">:</span> <span class="token punctuation">[</span>
    <span class="token string">"renderized"</span><span class="token punctuation">,</span> <span class="token string">"super-streams"</span>
  <span class="token punctuation">]</span>
<span class="token punctuation">}</span></code></pre></div>
<p>we can obtain <code class="language-text">awesome-web-framework-1.0.0.tgz</code> file by running <code class="language-text">npm pack</code>.
This file contains the dependencies <code class="language-text">renderized</code> and <code class="language-text">super-streams</code> which
can be installed in a new project by executing <code class="language-text">npm install
awesome-web-framework-1.0.0.tgz</code>.  Note that the package names do not include
any versions, as that information is specified in <code class="language-text">dependencies</code>.</p>
<p>If this is spelled <code class="language-text">&quot;bundleDependencies&quot;</code>, then that is also honored.</p>
<h3 id="optionaldependencies" style="position:relative;"><a href="#optionaldependencies" aria-label="optionaldependencies permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>optionalDependencies</h3>
<p>If a dependency can be used, but you would like npm to proceed if it cannot be
found or fails to install, then you may put it in the <code class="language-text">optionalDependencies</code>
object.  This is a map of package name to version or url, just like the
<code class="language-text">dependencies</code> object.  The difference is that build failures do not cause
installation to fail.  Running <code class="language-text">npm install --no-optional</code> will prevent these
dependencies from being installed.</p>
<p>It is still your program's responsibility to handle the lack of the
dependency.  For example, something like this:</p>
<div class="gatsby-highlight" data-language="js"><pre class="language-js"><code class="language-js"><span class="token keyword">try</span> <span class="token punctuation">{</span>
  <span class="token keyword">var</span> foo <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'foo'</span><span class="token punctuation">)</span>
  <span class="token keyword">var</span> fooVersion <span class="token operator">=</span> <span class="token function">require</span><span class="token punctuation">(</span><span class="token string">'foo/package.json'</span><span class="token punctuation">)</span><span class="token punctuation">.</span>version
<span class="token punctuation">}</span> <span class="token keyword">catch</span> <span class="token punctuation">(</span>er<span class="token punctuation">)</span> <span class="token punctuation">{</span>
  foo <span class="token operator">=</span> <span class="token keyword">null</span>
<span class="token punctuation">}</span>
<span class="token keyword">if</span> <span class="token punctuation">(</span> <span class="token function">notGoodFooVersion</span><span class="token punctuation">(</span>fooVersion<span class="token punctuation">)</span> <span class="token punctuation">)</span> <span class="token punctuation">{</span>
  foo <span class="token operator">=</span> <span class="token keyword">null</span>
<span class="token punctuation">}</span>

<span class="token comment">// .. then later in your program ..</span>

<span class="token keyword">if</span> <span class="token punctuation">(</span>foo<span class="token punctuation">)</span> <span class="token punctuation">{</span>
  foo<span class="token punctuation">.</span><span class="token function">doFooThings</span><span class="token punctuation">(</span><span class="token punctuation">)</span>
<span class="token punctuation">}</span></code></pre></div>
<p>Entries in <code class="language-text">optionalDependencies</code> will override entries of the same name in
<code class="language-text">dependencies</code>, so it's usually best to only put in one place.</p>
<h3 id="engines" style="position:relative;"><a href="#engines" aria-label="engines permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>engines</h3>
<p>You can specify the version of node that your stuff works on:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"engines"</span> <span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token property">"node"</span> <span class="token operator">:</span> <span class="token string">">=0.10.3 &lt;0.12"</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span></code></pre></div>
<p>And, like with dependencies, if you don't specify the version (or if you
specify "*" as the version), then any version of node will do.</p>
<p>If you specify an "engines" field, then npm will require that "node" be
somewhere on that list. If "engines" is omitted, then npm will just assume
that it works on node.</p>
<p>You can also use the "engines" field to specify which versions of npm
are capable of properly installing your program.  For example:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token punctuation">{</span> <span class="token property">"engines"</span> <span class="token operator">:</span> <span class="token punctuation">{</span> <span class="token property">"npm"</span> <span class="token operator">:</span> <span class="token string">"~1.0.20"</span> <span class="token punctuation">}</span> <span class="token punctuation">}</span></code></pre></div>
<p>Unless the user has set the <code class="language-text">engine-strict</code> config flag, this
field is advisory only and will only produce warnings when your package is installed as a dependency.</p>
<h3 id="enginestrict" style="position:relative;"><a href="#enginestrict" aria-label="enginestrict permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>engineStrict</h3>
<p><strong>This feature was removed in npm 3.0.0</strong></p>
<p>Prior to npm 3.0.0, this feature was used to treat this package as if the
user had set <code class="language-text">engine-strict</code>. It is no longer used.</p>
<h3 id="os" style="position:relative;"><a href="#os" aria-label="os permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>os</h3>
<p>You can specify which operating systems your
module will run on:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"os"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"darwin"</span><span class="token punctuation">,</span> <span class="token string">"linux"</span> <span class="token punctuation">]</span></code></pre></div>
<p>You can also blacklist instead of whitelist operating systems,
just prepend the blacklisted os with a '!':</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"os"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"!win32"</span> <span class="token punctuation">]</span></code></pre></div>
<p>The host operating system is determined by <code class="language-text">process.platform</code></p>
<p>It is allowed to both blacklist, and whitelist, although there isn't any
good reason to do this.</p>
<h3 id="cpu" style="position:relative;"><a href="#cpu" aria-label="cpu permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>cpu</h3>
<p>If your code only runs on certain cpu architectures,
you can specify which ones.</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"cpu"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"x64"</span><span class="token punctuation">,</span> <span class="token string">"ia32"</span> <span class="token punctuation">]</span></code></pre></div>
<p>Like the <code class="language-text">os</code> option, you can also blacklist architectures:</p>
<div class="gatsby-highlight" data-language="json"><pre class="language-json"><code class="language-json"><span class="token property">"cpu"</span> <span class="token operator">:</span> <span class="token punctuation">[</span> <span class="token string">"!arm"</span><span class="token punctuation">,</span> <span class="token string">"!mips"</span> <span class="token punctuation">]</span></code></pre></div>
<p>The host architecture is determined by <code class="language-text">process.arch</code></p>
<h3 id="preferglobal" style="position:relative;"><a href="#preferglobal" aria-label="preferglobal permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>preferGlobal</h3>
<p><strong>DEPRECATED</strong></p>
<p>This option used to trigger an npm warning, but it will no longer warn. It is
purely there for informational purposes. It is now recommended that you install
any binaries as local devDependencies wherever possible.</p>
<h3 id="private" style="position:relative;"><a href="#private" aria-label="private permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>private</h3>
<p>If you set <code class="language-text">&quot;private&quot;: true</code> in your package.json, then npm will refuse
to publish it.</p>
<p>This is a way to prevent accidental publication of private repositories.  If
you would like to ensure that a given package is only ever published to a
specific registry (for example, an internal registry), then use the
<code class="language-text">publishConfig</code> dictionary described below to override the <code class="language-text">registry</code> config
param at publish-time.</p>
<h3 id="publishconfig" style="position:relative;"><a href="#publishconfig" aria-label="publishconfig permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>publishConfig</h3>
<p>This is a set of config values that will be used at publish-time. It's
especially handy if you want to set the tag, registry or access, so that
you can ensure that a given package is not tagged with "latest", published
to the global public registry or that a scoped module is private by default.</p>
<p>Any config values can be overridden, but only "tag", "registry" and "access"
probably matter for the purposes of publishing.</p>
<p>See <a href="../../using-npm/config"><code class="language-text">config</code></a> to see the list of config options that can be
overridden.</p>
<h3 id="default-values" style="position:relative;"><a href="#default-values" aria-label="default values permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>DEFAULT VALUES</h3>
<p>npm will default some values based on package contents.</p>
<ul>
<li>
<p><code class="language-text">&quot;scripts&quot;: {&quot;start&quot;: &quot;node server.js&quot;}</code></p>
<p>If there is a <code class="language-text">server.js</code> file in the root of your package, then npm
will default the <code class="language-text">start</code> command to <code class="language-text">node server.js</code>.</p>
</li>
<li>
<p><code class="language-text">&quot;scripts&quot;:{&quot;install&quot;: &quot;node-gyp rebuild&quot;}</code></p>
<p>If there is a <code class="language-text">binding.gyp</code> file in the root of your package and you have not defined an <code class="language-text">install</code> or <code class="language-text">preinstall</code> script, npm will
default the <code class="language-text">install</code> command to compile using node-gyp.</p>
</li>
<li>
<p><code class="language-text">&quot;contributors&quot;: [...]</code></p>
<p>If there is an <code class="language-text">AUTHORS</code> file in the root of your package, npm will
treat each line as a <code class="language-text">Name &lt;email&gt; (url)</code> format, where email and url
are optional.  Lines which start with a <code class="language-text">#</code> or are blank, will be
ignored.</p>
</li>
</ul>
<h3 id="see-also" style="position:relative;"><a href="#see-also" aria-label="see also permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>SEE ALSO</h3>
<ul>
<li><a href="../../using-npm/semver">semver</a></li>
<li><a href="../../cli-commands/init">npm init</a></li>
<li><a href="../../cli-commands/version">npm version</a></li>
<li><a href="../../cli-commands/config">npm config</a></li>
<li><a href="../../cli-commands/help">npm help</a></li>
<li><a href="../../cli-commands/install">npm install</a></li>
<li><a href="../../cli-commands/publish">npm publish</a></li>
<li><a href="../../cli-commands/uninstall">npm uninstall</a></li>
</ul></div><div class="FoundTypo__Container-sc-1e373sc-0 fMOzaj"><p><span role="img" aria-label="eyes-emoji">👀</span> Found a typo? <a href="https://github.com/npm/cli/">Let us know!</a></p><p>The current stable version of npm is <a href="https://github.com/npm/cli/">here</a>. To upgrade, run: <code class="language-text">npm install npm@latest -g</code></p><p>To report bugs or submit feature requests for the docs, please post <a href="https://npm.community/c/support/docs-needed">here</a>. Submit npm issues <a href="https://npm.community/c/bugs">here.</a></p></div><script>
          var anchors = document.querySelectorAll(".sidebar a, .documentation a")
          Array.prototype.slice.call(anchors).map(function(el) {
            if (el.href.match(/file:\/\//)) {
              el.href = el.href + "/index.html"
            }
          })
          </script></div></div></div></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/configuring-npm/package-json";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-041f7e4f56e7debd8d98.js"],"component---src-templates-page-js":["/component---src-templates-page-js-7faf8ceb01991e80d244.js"],"component---src-pages-404-js":["/component---src-pages-404-js-6c8c4e2e908a7101a231.js"],"component---src-pages-index-js":["/component---src-pages-index-js-6b93f80c513be8d7330c.js"]};/*]]>*/</script><script src="../../component---src-templates-page-js-7faf8ceb01991e80d244.js" async=""></script><script src="../../app-041f7e4f56e7debd8d98.js" async=""></script><script src="../../commons-4df35f6dbd2fdc25d817.js" async=""></script><script src="../../styles-de5e304580bcba768a01.js" async=""></script><script src="../../webpack-runtime-b622568e0ef6e093f777.js" async=""></script></body></html>