---
section: cli-commands 
title: npm-stars
description: View packages marked as favorites
---

# npm-stars(1)

## View packages marked as favorites

### Synopsis
```bash
npm stars [<user>]
```

### Description

If you have starred a lot of neat things and want to find them again
quickly this command lets you do just that.

You may also want to see your friend's favorite packages, in this case
you will most certainly enjoy this command.

### See Also

* [npm star](/cli-commands/star)
* [npm view](/cli-commands/view)
* [npm whoami](/cli-commands/whoami)
* [npm adduser](/cli-commands/adduser)
