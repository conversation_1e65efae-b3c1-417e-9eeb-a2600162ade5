{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Publish.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Publish.vue", "mtime": 1600574148000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiUHVibGlzaCIsCiAgZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIHRpdGxlOiAiIiwKICAgICAgY29udGVudDogIiIsCiAgICAgIHNob3dDb21tdW5pdHlMaXN0OiBmYWxzZSwKICAgICAgc2VsZWN0Q29tbXVuaXR5OiB7fSwKICAgICAgY29tbXVuaXR5TGlzdDogW10KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBzdWJtaXQoKSB7CiAgICAgIHRoaXMuJGF4aW9zKHsKICAgICAgICBtZXRob2Q6ICJwb3N0IiwKICAgICAgICB1cmw6ICIvcG9zdCIsCiAgICAgICAgZGF0YTogSlNPTi5zdHJpbmdpZnkoewogICAgICAgICAgdGl0bGU6IHRoaXMudGl0bGUsCiAgICAgICAgICBjb250ZW50OiB0aGlzLmNvbnRlbnQsCiAgICAgICAgICBjb21tdW5pdHlfaWQ6IHRoaXMuc2VsZWN0Q29tbXVuaXR5LmlkCiAgICAgICAgfSkKICAgICAgfSkKICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5kYXRhKTsKICAgICAgICAgIGlmIChyZXNwb25zZS5jb2RlID09IDEwMDApIHsKICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goeyBwYXRoOiB0aGlzLnJlZGlyZWN0IHx8ICIvIiB9KTsKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGNvbnNvbGUubG9nKHJlc3BvbnNlLm1zZyk7CiAgICAgICAgICB9CiAgICAgICAgfSkKICAgICAgICAuY2F0Y2goZXJyb3IgPT4gewogICAgICAgICAgY29uc29sZS5sb2coZXJyb3IpOwogICAgICAgIH0pOwogICAgfSwKICAgIGdldENvbW11bml0eUxpc3QoKSB7CiAgICAgIHRoaXMuJGF4aW9zKHsKICAgICAgICBtZXRob2Q6ICJnZXQiLAogICAgICAgIHVybDogIi9jb21tdW5pdHkiCiAgICAgIH0pCiAgICAgICAgLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UuZGF0YSk7CiAgICAgICAgICBpZiAocmVzcG9uc2UuY29kZSA9PSAxMDAwKSB7CiAgICAgICAgICAgIHRoaXMuY29tbXVuaXR5TGlzdCA9IHJlc3BvbnNlLmRhdGE7CiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBjb25zb2xlLmxvZyhyZXNwb25zZS5tc2cpOwogICAgICAgICAgfQogICAgICAgIH0pCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsKICAgICAgICAgIGNvbnNvbGUubG9nKGVycm9yKTsKICAgICAgICB9KTsKICAgIH0sCiAgICBzaG93Q29tbXVuaXR5KCl7CiAgICAgIHRoaXMuc2hvd0NvbW11bml0eUxpc3QgPSAhdGhpcy5zaG93Q29tbXVuaXR5TGlzdDsKICAgIH0sCiAgICBzZWxlY3RlZChpbmRleCkgewogICAgICB0aGlzLnNlbGVjdENvbW11bml0eSA9IHRoaXMuY29tbXVuaXR5TGlzdFtpbmRleF07CiAgICAgIHRoaXMuc2hvd0NvbW11bml0eUxpc3QgPSBmYWxzZTsKICAgICAgY29uc29sZS5sb2codGhpcy5zZWxlY3RDb21tdW5pdHkpCiAgICB9CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbigpIHsKICAgIHRoaXMuZ2V0Q29tbXVuaXR5TGlzdCgpOwogIH0KfTsK"}, {"version": 3, "sources": ["Publish.vue"], "names": [], "mappings": ";AAoEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Publish.vue", "sourceRoot": "src/views", "sourcesContent": ["\n<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <div class=\"post-name\">我好想写点什么</div>\n      <div class=\"post-type\">\n        <input type=\"text\" class=\"post-type-value\" placeholder=\"选择一个频道\" v-model=\"selectCommunity.name\" @click=\"showCommunity()\"/>\n        <ul class=\"post-type-options\" v-show=\"showCommunityList\">\n          <li class=\"post-type-cell\"\n            v-for=\"(community, index) in communityList\"\n            :key=\"community.id\"\n            @click=\"selected(index)\"\n          >\n            {{community.name}}\n          </li>\n        </ul>\n        <i class=\"p-icon\"></i>\n      </div>\n      <div class=\"post-content\">\n        <ul class=\"cat\">\n          <li class=\"cat-item active\">\n            <i class=\"iconfont icon-edit\"></i>post\n          </li>\n          <li class=\"cat-item\">\n            <i class=\"iconfont icon-image\"></i>image/video\n          </li>\n        </ul>\n        <div class=\"post-sub-container\">\n          <div class=\"post-sub-header\">\n            <textarea class=\"post-title\" id cols=\"30\" rows=\"10\" v-model=\"title\" placeholder=\"标题\"></textarea>\n            <span class=\"textarea-num\">0/300</span>\n          </div>\n          <!---此处放置富文本--->\n          <div class=\"post-text-con\">\n            <textarea\n              class=\"post-content-t\"\n              id\n              cols=\"30\"\n              rows=\"10\"\n              v-model=\"content\"\n              placeholder=\"内容\"\n            ></textarea>\n          </div>\n        </div>\n        <div class=\"post-footer\">\n          <div class=\"btns\">\n            <button class=\"btn\">取消</button>\n            <button class=\"btn\" @click=\"submit()\">发表</button>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"right\">\n      <div class=\"post-rank\">\n        <h5 class=\"p-r-title\">\n          <i class=\"p-r-icon\"></i>发帖规范\n        </h5>\n        <ul class=\"p-r-content\">\n          <li class=\"p-r-item\">1.网络不是法外之地</li>\n          <li class=\"p-r-item\">2.网络不是法外之地</li>\n          <li class=\"p-r-item\">3.网络不是法外之地</li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Publish\",\n  data() {\n    return {\n      title: \"\",\n      content: \"\",\n      showCommunityList: false,\n      selectCommunity: {},\n      communityList: []\n    };\n  },\n  methods: {\n    submit() {\n      this.$axios({\n        method: \"post\",\n        url: \"/post\",\n        data: JSON.stringify({\n          title: this.title,\n          content: this.content,\n          community_id: this.selectCommunity.id\n        })\n      })\n        .then(response => {\n          console.log(response.data);\n          if (response.code == 1000) {\n            this.$router.push({ path: this.redirect || \"/\" });\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    getCommunityList() {\n      this.$axios({\n        method: \"get\",\n        url: \"/community\"\n      })\n        .then(response => {\n          console.log(response.data);\n          if (response.code == 1000) {\n            this.communityList = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    showCommunity(){\n      this.showCommunityList = !this.showCommunityList;\n    },\n    selected(index) {\n      this.selectCommunity = this.communityList[index];\n      this.showCommunityList = false;\n      console.log(this.selectCommunity)\n    }\n  },\n  mounted: function() {\n    this.getCommunityList();\n  }\n};\n</script>\n<style lang=\"less\" scoped>\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 0 auto;\n  padding: 20px 24px;\n  margin-top: 48px;\n  .left {\n    flex-grow: 1;\n    max-width: 740px;\n    word-break: break-word;\n    flex: 1;\n    margin: 32px;\n    margin-right: 12px;\n    padding-bottom: 30px;\n    position: relative;\n    .post-name {\n      padding: 4px;\n      margin: 16px 0;\n      border-bottom: solid 1px #edeff1;\n      display: -webkit-flex;\n      display: flex;\n      justify-content: space-between;\n      .p-btn {\n        font-size: 12px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 24px;\n        text-transform: uppercase;\n        border: none;\n        padding: 0;\n        margin-left: 10px;\n        color: #0079d3;\n      }\n      .p-num {\n        font-size: 12px;\n        font-weight: 400;\n        line-height: 16px;\n        background: #878a8c;\n        border-radius: 2px;\n        color: #ffffff;\n        margin-left: 4px;\n        padding: 1px 3px;\n      }\n    }\n    .post-type {\n      position: relative;\n      box-sizing: border-box;\n      width: 300px;\n      height: 40px;\n      border-radius: 4px;\n      transition: box-shadow 0.2s ease;\n      box-shadow: 0 0 0 0 #ffffff;\n      border: 1px solid #edeff1;\n      background-color: #ffffff;\n      padding-left: 10px;\n      position: relative;\n      .post-type-value {\n        font-size: 14px;\n        font-weight: 500;\n        line-height: 40px;\n        width: 100%;\n        vertical-align: middle;\n        color: #1c1c1c;\n        background-color: transparent;\n        cursor: pointer;\n      }\n      .post-type-options {\n        position: absolute;\n        width: 100%;\n        background-color: white;\n        left: 0;\n        z-index: 1;\n        border-radius: 4px;\n        .post-type-cell {\n          margin: 14px 8px 5px;\n          font-size: 14px;\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          padding-bottom: 8px;\n          color: #1c1c1c;\n          cursor: pointer;\n        }\n      }\n      .p-icon {\n        width: 0;\n        height: 0;\n        border-top: 5px solid #878a8c;\n        border-right: 5px solid transparent;\n        border-bottom: 5px solid transparent;\n        border-left: 5px solid transparent;\n        margin-left: 10px;\n        position: absolute;\n        top: 50%;\n        right: 10px;\n        cursor: pointer;\n      }\n    }\n    .post-content {\n      background-color: #ffffff;\n      margin: 10px 0;\n      padding-bottom: 15px;\n      border-radius: 5px;\n      .cat {\n        display: flex;\n        display: -webkit-flex;\n        justify-content: space-between;\n        align-items: center;\n        width: 100%;\n        height: 53px;\n        .cat-item {\n          padding: 10px 0;\n          width: 50%;\n          height: 40px;\n          line-height: 40px;\n          text-align: center;\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          border-right: 1px solid #edeff1;\n          color: #878a8c;\n          .iconfont {\n            margin-right: 4px;\n          }\n        }\n        .active {\n          color: #0079d3;\n          font-weight: bolder;\n          background: none;\n        }\n      }\n      .post-sub-container {\n        padding: 16px;\n        .post-sub-header {\n          position: relative;\n          .post-title {\n            resize: none;\n            box-sizing: border-box;\n            overflow: hidden;\n            display: block;\n            width: 100%;\n            height: 40px;\n            padding: 0 0 0 10px;\n            outline: none;\n            border: 1px solid #edeff1;\n            border-radius: 4px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 400;\n            line-height: 40px;\n          }\n          .textarea-num {\n            font-size: 10px;\n            font-weight: 700;\n            letter-spacing: 0.5px;\n            line-height: 12px;\n            text-transform: uppercase;\n            bottom: 12px;\n            color: #878a8c;\n            pointer-events: none;\n            position: absolute;\n            right: 12px;\n          }\n        }\n        .post-text-con {\n          width: 100%;\n          height: 200px;\n          border: 1px solid #edeff1;\n          margin-top: 20px;\n          .post-content-t {\n            resize: none;\n            box-sizing: border-box;\n            overflow: hidden;\n            display: block;\n            width: 100%;\n            height: 200px;\n            padding: 12px 8px;\n            outline: none;\n            border: 1px solid #edeff1;\n            border-radius: 4px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 400;\n            line-height: 21px;\n          }\n        }\n      }\n      .post-footer {\n        display: flex;\n        display: -webkit-flex;\n        margin: 0 16px;\n        justify-content: flex-end;\n        .sign {\n          display: flex;\n          display: -webkit-flex;\n          .sign-item {\n            list-style: none;\n            padding: 5px 8px;\n            border: 1px solid #edeff1;\n            margin-right: 10px;\n            color: #878a8c;\n            font-size: 12px;\n            font-weight: 700;\n          }\n        }\n        .btns {\n          .btn {\n            border: 1px solid transparent;\n            border-radius: 4px;\n            box-sizing: border-box;\n            text-align: center;\n            text-decoration: none;\n            font-size: 12px;\n            font-weight: 700;\n            letter-spacing: 0.5px;\n            line-height: 24px;\n            text-transform: uppercase;\n            padding: 3px 16px;\n            background: #0079d3;\n            color: #ffffff;\n            margin-left: 8px;\n            cursor: pointer;\n          }\n        }\n      }\n      .alias {\n        background-color: #f6f7f8;\n        border-radius: 0 0 6px 6px;\n        border-top: solid 1px #edeff1;\n        display: -ms-flexbox;\n        display: flex;\n        -ms-flex-flow: column;\n        flex-flow: column;\n        padding: 8px 16px 21px;\n        position: relative;\n        .send-post {\n          font-size: 14px;\n          font-weight: 500;\n          line-height: 18px;\n          color: #1c1c1c;\n          margin-right: 4px;\n        }\n        .connect {\n          font-size: 14px;\n          font-weight: 500;\n          line-height: 18px;\n          color: #0079d3;\n          display: block;\n          margin-right: 4px;\n          margin-top: 10px;\n        }\n      }\n    }\n  }\n  .right {\n    flex-grow: 0;\n    width: 312px;\n    margin-top: 62px;\n    .post-rank {\n      background-color: #ffffff;\n      border-radius: 4px;\n      margin-top: 15px;\n      padding: 12px;\n      .p-r-title {\n        display: flex;\n        display: -webkit-flex;\n        align-items: center;\n        .p-r-icon {\n          width: 40px;\n          height: 40px;\n          background: url(\"../assets/images/avatar.png\") no-repeat;\n          background-size: cover;\n          margin-right: 10px;\n        }\n        font-size: 16px;\n        font-weight: 500;\n        line-height: 20px;\n        -ms-flex-align: center;\n        align-items: center;\n        border-bottom: 1px solid #edeff1;\n        color: #1c1c1c;\n        padding-bottom: 10px;\n        // display: -ms-flexbox;\n        // display: flex;\n      }\n      .p-r-content {\n        display: flex;\n        display: -webkit-flex;\n        flex-direction: column;\n        .p-r-item {\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          color: #1c1c1c;\n          padding: 10px 5px;\n        }\n      }\n    }\n  }\n}\n</style>"]}]}