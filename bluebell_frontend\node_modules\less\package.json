{"name": "less", "version": "3.11.1", "description": "Leaner CSS", "homepage": "http://lesscss.org", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "contributors": ["The Core Less Team"], "bugs": {"url": "https://github.com/less/less.js/issues"}, "repository": {"type": "git", "url": "https://github.com/less/less.js.git"}, "master": {"url": "https://github.com/less/less.js/blob/master/", "raw": "https://raw.githubusercontent.com/less/less.js/master/"}, "license": "Apache-2.0", "bin": {"lessc": "./bin/lessc"}, "main": "index", "module": "./lib/less-node/index", "directories": {"test": "./test"}, "browser": "./dist/less.js", "engines": {"node": ">=6"}, "scripts": {"test": "grunt test", "grunt": "grunt"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "mime": "^1.4.1", "mkdirp": "^0.5.0", "promise": "^7.1.1", "request": "^2.83.0", "source-map": "~0.6.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.3.3", "@typescript-eslint/parser": "^2.3.3", "benny": "^3.6.12", "bootstrap-less-port": "0.3.0", "chai": "^4.2.0", "diff": "^3.2.0", "fs-extra": "^8.1.0", "git-rev": "^0.2.1", "globby": "^10.0.1", "grunt": "^1.0.4", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-eslint": "^21.1.0", "grunt-saucelabs": "^9.0.1", "grunt-shell": "^1.3.0", "html-template-tag": "^3.2.0", "import-module": "file:test/import-module", "jit-grunt": "^0.10.0", "less-plugin-autoprefix": "^1.5.1", "less-plugin-clean-css": "^1.5.1", "minimist": "^1.2.0", "mocha": "^6.2.1", "mocha-headless-chrome": "^2.0.3", "mocha-teamcity-reporter": "^3.0.0", "performance-now": "^0.2.0", "phin": "^2.2.3", "promise": "^7.1.1", "read-glob": "^3.0.0", "rollup": "^1.17.0", "rollup-plugin-commonjs": "^10.0.1", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-terser": "^5.1.1", "rollup-plugin-typescript2": "^0.24.3", "semver": "^6.3.0", "time-grunt": "^1.3.0", "ts-node": "^8.4.1", "typescript": "^3.6.3", "uikit": "2.27.4"}, "keywords": ["compile less", "css nesting", "css variable", "css", "gradients css", "gradients css3", "less compiler", "less css", "less mixins", "less", "less.js", "lesscss", "mixins", "nested css", "parser", "preprocessor", "bootstrap css", "bootstrap less", "style", "styles", "stylesheet", "variables in css", "css less"], "rawcurrent": "https://raw.github.com/less/less.js/v", "sourcearchive": "https://github.com/less/less.js/archive/v", "dependencies": {"clone": "^2.1.2", "tslib": "^1.10.0"}}