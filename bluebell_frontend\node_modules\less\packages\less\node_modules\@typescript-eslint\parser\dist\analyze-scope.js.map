{"version": 3, "file": "analyze-scope.js", "sourceRoot": "", "sources": ["../src/analyze-scope.ts"], "names": [], "mappings": ";;AAAA,8EAI+C;AAC/C,6DAA0D;AAG1D,yDAAqD;AACrD,iDAAiE;AAEjE;;;;GAIG;AACH,SAAS,cAAc,CAAC,MAAW;IACjC,OAAO,mBAAmB,CAAC,UAAoB,IAAS,EAAE,UAAe;QACvE,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,UAAU,CAAC,CAAC;QAEpC,0EAA0E;QAC1E,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,QAAQ,EAAE;YACZ,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC5B;IACH,CAAC,CAAC;AACJ,CAAC;AAED,MAAM,cAAe,SAAQ,kCAAa,CAAC,cAAc;IACvD,YACE,OAA4C,EAC5C,WAAgB,EAChB,QAA8C;QAE9C,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,CAAC,CAAC;IACxC,CAAC;IAED,UAAU,CAAC,IAAyB;QAClC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;QACvB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,YAAY,CAAC,IAA2B;QACtC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,aAAa,CAAC,IAA4B;QACxC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC1C,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,WAAW,CAAC,IAA0B;QACpC,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SAC/C;IACH,CAAC;IAED,mBAAmB,CAAC,IAAkC;QACpD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QAC3B,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC;SAC9C;IACH,CAAC;CACF;AAED,MAAM,UAAW,SAAQ,kCAAa,CAAC,UAAwB;IAG7D,YAAY,OAAY,EAAE,YAA0B;QAClD,KAAK,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;QAC7B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;;;;OAKG;IACH,YAAY,CACV,IAAO,EACP,OAA4C,EAC5C,QAA8C;QAE9C,IAAI,CAAC,IAAI,EAAE;YACT,OAAO;SACR;QAED,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;YACjC,QAAQ,GAAG,OAAO,CAAC;YACnB,OAAO,GAAG,EAAE,qBAAqB,EAAE,KAAK,EAAE,CAAC;SAC5C;QAED,MAAM,OAAO,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;QACjE,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAEpB,IAAI,OAAO,CAAC,qBAAqB,EAAE;YACjC,aAAa;YACb,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAClD;IACH,CAAC;IAED;;;;OAIG;IACH,aAAa,CACX,IAGoC;QAEpC,MAAM,EAAE,IAAI,EAAE,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QACpE,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEvC,oBAAoB;QACpB,IAAI,IAAI,KAAK,qBAAqB,IAAI,EAAE,EAAE;YACxC,UAAU,CAAC,QAAQ,CACjB,EAAE,EACF,IAAI,kCAAa,CAAC,UAAU,CAC1B,cAAc,EACd,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CACF,CAAC;YAEF,sEAAsE;YACtE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAE,CAAC;YAC3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACpC,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IACE,GAAG,CAAC,IAAI,KAAK,cAAc;oBAC3B,aAAa;oBACb,GAAG,CAAC,IAAI,CAAC,IAAI,KAAK,mBAAmB,EACrC;oBACA,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClB,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBACzB,MAAM;iBACP;aACF;SACF;aAAM,IAAI,IAAI,KAAK,oBAAoB,IAAI,EAAE,EAAE;YAC9C,YAAY,CAAC,iCAAiC,CAAC,IAAI,CAAC,CAAC;SACtD;QAED,2BAA2B;QAC3B,YAAY,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,uBAAuB,CAAC,CAAC;QACrE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEvC,8BAA8B;QAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE3B,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACtC,IAAI,CAAC,YAAY,CACf,MAAM,CAAC,CAAC,CAAC,EACT,EAAE,qBAAqB,EAAE,IAAI,EAAE,EAC/B,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBAChB,IACE,OAAO,CAAC,IAAI,KAAK,mCAAc,CAAC,UAAU;oBAC1C,OAAO,CAAC,IAAI,KAAK,MAAM,EACvB;oBACA,UAAU,CAAC,QAAQ,CACjB,OAAO,EACP,IAAI,kCAAa,CAAC,mBAAmB,CACnC,OAAO,EACP,IAAI,EACJ,CAAC,EACD,IAAI,CAAC,IAAI,CACV,CACF,CAAC;oBACF,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;iBACrE;YACH,CAAC,CACF,CAAC;SACH;QAED,2BAA2B;QAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEvB,oBAAoB;QACpB,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,KAAK,gBAAgB,EAAE;YAC1C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SAClB;QAED,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,IAA0D;QACnE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtC,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACtC;QACD,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC3C;QACD,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;QAE9B,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,IAInB;QACC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;YACpC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;SAC/B;IACH,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAgC;QAChD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACtB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED;;;;OAIG;IACH,UAAU,CAAC,IAAyB;QAClC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAEtC,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;SACxB;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;IAClC,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CACd,IAAqE;QAErE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACtC,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC/B,CAAC;IAED;;;OAGG;IACH,aAAa,CACX,IAA+D;QAE/D,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,EAAE,cAAc,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC;QAElE,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;QACjC,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACjB;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAElB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,IAA4B;QACxC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAExB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,cAAc,CAAC,IAA6B;QAC1C,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAE/B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAExB,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAC3C,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,IAAgC;QAChD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACvC,MAAM,EAAE,EAAE,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAExD,0DAA0D;QAC1D,IAAI,EAAE,EAAE;YACN,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC;YAC7C,MAAM,IAAI,GAAG,QAAQ,IAAI,QAAQ,CAAC,IAAI,CAAC;YACvC,MAAM,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC,CAAC;YAClE,IAAI,CAAC,OAAO,EAAE;gBACZ,UAAU,CAAC,QAAQ,CACjB,EAAE,EACF,IAAI,kCAAa,CAAC,UAAU,CAC1B,cAAc,EACd,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CACF,CAAC;aACH;SACF;QAED,2BAA2B;QAC3B,YAAY,CAAC,wBAAwB,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAEvC,8BAA8B;QAC9B,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAE3B,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;YACtC,IAAI,CAAC,YAAY,CACf,MAAM,CAAC,CAAC,CAAC,EACT,EAAE,qBAAqB,EAAE,IAAI,EAAE,EAC/B,CAAC,OAAO,EAAE,IAAI,EAAE,EAAE;gBAChB,UAAU,CAAC,QAAQ,CACjB,OAAO,EACP,IAAI,kCAAa,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CACnE,CAAC;gBAEF,sEAAsE;gBACtE,MAAM,QAAQ,GAAG,UAAU,CAAC,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAClD,IAAI,QAAQ,EAAE;oBACZ,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;iBAC5B;gBACD,IAAI,CAAC,uBAAuB,CAAC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YACtE,CAAC,CACF,CAAC;SACH;QAED,2BAA2B;QAC3B,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEvB,4BAA4B;QAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED;;;OAGG;IACH,6BAA6B,CAC3B,IAA4C;QAE5C,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAEpD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,sBAAsB,CAAC,IAAqC;QAC1D,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,iBAAiB,CAAC,IAAgC;QAChD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;;OAIG;IACH,gBAAgB,CAAC,IAA+B;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,IAA8B;QAC5C,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;QAED,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,IAA6B;QAC1C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAE5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACjC;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAChC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;IACH,CAAC;IAED;;;OAGG;IACH,gBAAgB,CAAC,IAA+B;QAC9C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,0BAA0B,CAAC,IAAyC;QAClE,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,WAAW,CAAC,IAA0B;QACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAA8B;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAA0B;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,eAAe,CAAC,IAA8B;QAC5C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAA4B;QACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAA4B;QACxC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,kBAAkB,CAAC,IAAiC;QAClD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,iBAAiB,CAAC,IAAgC;QAChD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,IAAkC;QACpD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,YAAY,CAAC,IAA2B;QACtC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,IAA6B;QAC1C,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,IAAkC;QACpD,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,UAAU,CAAC,IAAyB;QAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,IAA0B;QACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,IAA8B;QAC5C,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,IAAkC;QACpD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QAE5D,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACjB;QACD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;QAExB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;IAChC,CAAC;IAED;;;OAGG;IACH,iBAAiB,CAAC,IAAgC;QAChD,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,EAAE,cAAc,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,IAAI,CAAC;QAEnE,IAAI,QAAQ,EAAE;YACZ,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;YACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;SACjB;QACD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,CAAC;QAC3B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QACjC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;QAEvB,IAAI,CAAC,QAAQ,GAAG,aAAa,CAAC;IAChC,CAAC;IAED;;;;;;;;;;;;;;;;OAgBG;IACH,iBAAiB,CAAC,IAAgC;QAChD,MAAM,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC;QAC7B,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAElC,IAAI,EAAE,EAAE;YACN,KAAK,CAAC,QAAQ,CAAC,EAAE,EAAE,IAAI,kCAAa,CAAC,UAAU,CAAC,UAAU,EAAE,EAAE,EAAE,IAAI,CAAC,CAAC,CAAC;SACxE;QAED,YAAY,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;QACnC,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;SACpB;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED;;;;;OAKG;IACH,YAAY,CAAC,IAA2B;QACtC,MAAM,EAAE,EAAE,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC;QACjC,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAElC,KAAK,CAAC,QAAQ,CACZ,EAAE,EACF,IAAI,kCAAa,CAAC,UAAU,CAAC,gBAAgB,EAAE,EAAE,EAAE,IAAI,CAAC,CACzD,CAAC;QACF,IAAI,WAAW,EAAE;YACf,KAAK,CAAC,aAAa,CACjB,EAAE,EACF,kCAAa,CAAC,SAAS,CAAC,KAAK,EAC7B,WAAW,EACX,IAAI,EACJ,KAAK,EACL,IAAI,CACL,CAAC;YACF,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;SACzB;IACH,CAAC;IAED;;;OAGG;IACH,mBAAmB,CAAC,IAAkC;QACpD,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QAClC,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC;QAE1B,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACnC,OAAO;SACR;QAED,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,EAAE;YAClC,KAAK,CAAC,QAAQ,CACZ,EAAE,EACF,IAAI,kCAAa,CAAC,UAAU,CAC1B,eAAe,EACf,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CACF,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED,sBAAsB,CAAC,IAAqC;QAC1D,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxB,CAAC;IAED;;;OAGG;IACH,aAAa,CAAC,IAA4B;QACxC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IACnB,CAAC;IAED,uBAAuB,CAAC,IAAsC;QAC5D,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;IACD,0BAA0B,CAAC,IAAyC;QAClE,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;;OAGG;IACH,yBAAyB,CAAC,IAAwC;QAChE,MAAM,EAAE,EAAE,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;QACrC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,KAAK,YAAY,EAAE;YAClC,IAAI,CAAC,YAAY,EAAE,CAAC,QAAQ,CAC1B,EAAE,EACF,IAAI,kCAAa,CAAC,UAAU,CAC1B,eAAe,EACf,EAAE,EACF,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL,CACF,CAAC;SACH;QACD,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACH,uBAAuB,CAAC,IAAkC;QACxD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;QACvC,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;QACzC,MAAM,WAAW,GAAG,YAAY,CAAC,WAAW,CAAC;QAC7C,MAAM,cAAc,GAAG,WAAW,CAAC,QAAQ,CAAC;QAE5C,WAAW,CAAC,QAAQ,GAAG,cAAc,CAAC,cAAc,CAAC,CAAC;QACtD,YAAY,CAAC,cAAc,GAAG,WAAW,CAAC;QAE1C,0DAA0D;QAC1D,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,eAAe,EAAE;YACnD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SAC1C;QAED,YAAY,CAAC,cAAc,GAAG,YAAY,CAAC;QAC3C,WAAW,CAAC,QAAQ,GAAG,cAAc,CAAC;IACxC,CAAC;IAED;;;OAGG;IACH,eAAe,CAAC,UAAiC;QAC/C,IAAI,UAAU,EAAE;YACd,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;SACtC;IACH,CAAC;IAED;;;OAGG;IACH,cAAc,CAAC,IAAmB;QAChC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;YACzB,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;SACvB;IACH,CAAC;CACF;AAED,SAAgB,YAAY,CAAC,GAAQ,EAAE,aAA4B;IACjE,MAAM,OAAO,GAAG;QACd,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE,KAAK;QACjB,SAAS,EAAE,KAAK;QAChB,WAAW,EACT,aAAa,CAAC,UAAU,KAAK,QAAQ;YACrC,CAAC,aAAa,CAAC,YAAY;gBACzB,aAAa,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,IAAI;QACrD,aAAa,EAAE,KAAK;QACpB,UAAU,EAAE,aAAa,CAAC,UAAU;QACpC,WAAW,EAAE,aAAa,CAAC,WAAW,IAAI,IAAI;QAC9C,gBAAgB,EAAhB,0BAAgB;QAChB,QAAQ,EAAR,6BAAQ;KACT,CAAC;IAEF,MAAM,YAAY,GAAG,IAAI,4BAAY,CAAC,OAAO,CAAC,CAAC;IAC/C,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;IAEzD,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAEtB,OAAO,YAAY,CAAC;AACtB,CAAC;AAtBD,oCAsBC"}