.mixin { border: 1px solid black; }
.mixout { border-color: orange; }
.borders { border-style: dashed; }
.mixin > * { border: do not match me; }

#namespace {
  .borders {
    border-style: dotted;
  }
  .biohazard {
    content: "death";
    .man {
      color: transparent;
    }
  }
}
#theme {
  > .mixin {
    background-color: grey;
  }
}
#container {
  color: black;
  .mixin;
  .mixout;
  #theme > .mixin;
}

#header {
  .milk {
    color: inherit;
    .mixin;
    #theme > .mixin;
  }
  #cookie {
    .chips {
      #namespace .borders;
      .calories {
        #container;
      }
    }
    .borders;
  }
}
.secure-zone { #namespace .biohazard .man; }
.direct {
  #namespace > .borders;
}

.bo, .bar {
    width: 100%;
}
.bo {
    border: 1px;
}
.ar.bo.ca {
    color: black;
}
.jo.ki {
    background: none;
}
.amp {
    &.support {
        color: orange;
        .higher {
            top: 0px;
        }
        &.deeper {
            height: auto;
        }
    }
}
.extended {
    .bo;
    .jo.ki;
    .amp.support;
    .amp.support.higher;
    .amp.support.deeper;
}
.do .re .mi .fa {
    .sol .la {
        .si {
            color: cyan;
        }
    }
}
.mutli-selector-parents {
    .do.re.mi.fa.sol.la.si;
}
.foo .bar {
  .bar;
}
.has_parents() {
  & .underParents {
    color: red;
  }
}
.has_parents();
.parent {
  .has_parents();
}
.margin_between(@above, @below) {
     * + & { margin-top: @above; }
     legend + & { margin-top: 0; }
     & + * { margin-top: @below; }
}
h1 { .margin_between(25px, 10px); }
h2 { .margin_between(20px, 8px); }
h3 { .margin_between(15px, 5px); }

.mixin_def(@url, @position){
    background-image: @url;
    background-position: @position;
}
.error{
  @s: "/";
  .mixin_def( "@{s}a.png", center center);
}
.recursion() {
  color: black;
}
.test-rule-rec {
  .recursion {
    .recursion();
  }
}
.paddingFloat(@padding) { padding-left: @padding; }

.button {
    .paddingFloat(((10px + 12) * 2));

    &.large { .paddingFloat(((10em * 2) * 2)); }
}
.clearfix() {
  // ...
}
.clearfix {
  .clearfix();
}
.clearfix {
  .clearfix();
}
.foo {
  .clearfix();
}