{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Login.vue", "mtime": 1598764894000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKCW5hbWU6ICJMb2dpbiIsCglkYXRhKCkgewoJCXJldHVybiB7CgkJCXVzZXJuYW1lOiAiIiwKCQkJcGFzc3dvcmQ6ICIiLAoJCQlzdWJtaXR0ZWQ6IGZhbHNlCgkJfTsKCX0sCgljb21wdXRlZDogewoJfSwKCWNyZWF0ZWQoKSB7CgoJfSwKCW1ldGhvZHM6IHsKCQlzdWJtaXQoKSB7CgkJCXRoaXMuJGF4aW9zKHsKCQkJCW1ldGhvZDogJ3Bvc3QnLAoJCQkJdXJsOicvbG9naW4nLAoJCQkJZGF0YTogSlNPTi5zdHJpbmdpZnkoewoJCQkJCXVzZXJuYW1lOiB0aGlzLnVzZXJuYW1lLAoJCQkJCXBhc3N3b3JkOiB0aGlzLnBhc3N3b3JkCgkJCQl9KQoJCQl9KS50aGVuKChyZXMpPT57CgkJCQljb25zb2xlLmxvZyhyZXMuZGF0YSkKCQkJCWlmIChyZXMuY29kZSA9PSAxMDAwKSB7CiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgibG9naW5SZXN1bHQiLCBKU09OLnN0cmluZ2lmeShyZXMuZGF0YSkpOwogICAgICAgICAgdGhpcy4kc3RvcmUuY29tbWl0KCJsb2dpbiIsIHJlcy5kYXRhKTsKICAgICAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHtwYXRoOiB0aGlzLnJlZGlyZWN0IHx8ICcvJyB9KQoJCQkJfSBlbHNlIHsKCQkJCQljb25zb2xlLmxvZyhyZXMubXNnKQoJCQkJfQoJCQl9KS5jYXRjaCgoZXJyb3IpPT57CgkJCQljb25zb2xlLmxvZyhlcnJvcikKCQkJfSkKCQl9Cgl9Cn07Cg=="}, {"version": 3, "sources": ["Login.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"container\">\n      <h2 class=\"form-title\">登录</h2>\n      <div class=\"form-group\">\n        <label for=\"name\">用户名</label>\n        <input type=\"text\" class=\"form-control\" v-model=\"username\" name=\"name\" id=\"name\" placeholder=\"用户名\" />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"pass\">密码</label>\n        <input type=\"password\" class=\"form-control\" v-model=\"password\"  name=\"pass\" id=\"pass\" placeholder=\"密码\" />\n      </div>\n      <div class=\"form-btn\">\n        <button type=\"button\" class=\"btn btn-info\" @click=\"submit\">提交</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n\tname: \"Login\",\n\tdata() {\n\t\treturn {\n\t\t\tusername: \"\",\n\t\t\tpassword: \"\",\n\t\t\tsubmitted: false\n\t\t};\n\t},\n\tcomputed: {\n\t},\n\tcreated() {\n\n\t},\n\tmethods: {\n\t\tsubmit() {\n\t\t\tthis.$axios({\n\t\t\t\tmethod: 'post',\n\t\t\t\turl:'/login',\n\t\t\t\tdata: JSON.stringify({\n\t\t\t\t\tusername: this.username,\n\t\t\t\t\tpassword: this.password\n\t\t\t\t})\n\t\t\t}).then((res)=>{\n\t\t\t\tconsole.log(res.data)\n\t\t\t\tif (res.code == 1000) {\n          localStorage.setItem(\"loginResult\", JSON.stringify(res.data));\n          this.$store.commit(\"login\", res.data);\n          this.$router.push({path: this.redirect || '/' })\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(res.msg)\n\t\t\t\t}\n\t\t\t}).catch((error)=>{\n\t\t\t\tconsole.log(error)\n\t\t\t})\n\t\t}\n\t}\n};\n</script>\n<style lang=\"less\" scoped>\n.main {\n  background: #f8f8f8;\n  padding: 150px 0;\n  .container {\n    width: 600px;\n    background: #fff;\n    margin: 0 auto;\n    max-width: 1200px;\n    padding: 20px;\n    .form-title {\n      margin-bottom: 33px;\n      text-align: center;\n    }\n    .form-group {\n      margin: 15px;\n      label {\n        display: inline-block;\n        max-width: 100%;\n        margin-bottom: 5px;\n        font-weight: 700;\n      }\n      .form-control {\n        display: block;\n        width: 100%;\n        height: 34px;\n        padding: 6px 12px;\n        font-size: 14px;\n        line-height: 1.42857143;\n        color: #555;\n        background-color: #fff;\n        background-image: none;\n        border: 1px solid #ccc;\n        border-radius: 4px;\n      }\n    }\n    .form-btn {\n      display: flex;\n      justify-content: center;\n      .btn {\n        padding: 6px 20px;\n        font-size: 18px;\n        line-height: 1.3333333;\n        border-radius: 6px;\n        display: inline-block;\n        margin-bottom: 0;\n        font-weight: 400;\n        text-align: center;\n        white-space: nowrap;\n        vertical-align: middle;\n        -ms-touch-action: manipulation;\n        touch-action: manipulation;\n        cursor: pointer;\n        border: 1px solid transparent;\n      }\n      .btn-info {\n        color: #fff;\n        background-color: #5bc0de;\n        border-color: #46b8da;\n      }\n    }\n  }\n}\n</style>"]}]}