---
section: cli-commands 
title: npm-star
description: Mark your favorite packages
---

# npm-star(1)

## Mark your favorite packages

### Synopsis

```bash
npm star [<pkg>...]
npm unstar [<pkg>...]
```

### Description

"Starring" a package means that you have some interest in it.  It's
a vaguely positive way to show that you care.

"Unstarring" is the same thing, but in reverse.

It's a boolean thing.  Starring repeatedly has no additional effect.

### See Also

* [npm view](/cli-commands/view)
* [npm whoami](/cli-commands/whoami)
* [npm adduser](/cli-commands/adduser)
