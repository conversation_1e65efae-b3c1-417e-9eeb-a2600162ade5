{"version": 3, "file": "class-name-casing.js", "sourceRoot": "", "sources": ["../../src/rules/class-name-casing.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,OAAO;SACrB;QACD,QAAQ,EAAE;YACR,cAAc,EAAE,kDAAkD;SACnE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAY;YAChC,OAAO,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAC1C,CAAC;QAED;;;;WAIG;QACH,SAAS,MAAM,CAAC,IAAmB,EAAE,EAAuB;YAC1D,IAAI,YAAY,CAAC;YAEjB,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,mCAAc,CAAC,gBAAgB,CAAC;gBACrC,KAAK,mCAAc,CAAC,eAAe;oBACjC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,OAAO,CAAC;oBAC1D,MAAM;gBACR,KAAK,mCAAc,CAAC,sBAAsB;oBACxC,YAAY,GAAG,WAAW,CAAC;oBAC3B,MAAM;gBACR;oBACE,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC;aAC5B;YAED,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,EAAE;gBACR,SAAS,EAAE,gBAAgB;gBAC3B,IAAI,EAAE;oBACJ,YAAY;oBACZ,IAAI,EAAE,EAAE,CAAC,IAAI;iBACd;aACF,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,2DAA2D,CACzD,IAG4B;gBAE5B,0DAA0D;gBAC1D,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;oBAC1C,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;iBACvB;YACH,CAAC;YACD,iDAAiD,CAC/C,IAAiC;gBAEjC,IACE,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,mCAAc,CAAC,YAAY;oBAC5C,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa,EAC7C;oBACA,+CAA+C;oBAC/C;;;;;sBAKE;iBACH;qBAAM;oBACL,MAAM,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;oBACnB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAgC,CAAC;oBAEvD,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;wBAChD,MAAM,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;qBACtB;iBACF;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}