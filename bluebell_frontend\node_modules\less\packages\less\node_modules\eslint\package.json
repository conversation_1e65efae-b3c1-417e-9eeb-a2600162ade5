{"name": "eslint", "version": "5.16.0", "author": "<PERSON> <<EMAIL>>", "description": "An AST-based pattern checker for JavaScript.", "bin": {"eslint": "./bin/eslint.js"}, "main": "./lib/api.js", "scripts": {"test": "node Makefile.js test", "lint": "node Makefile.js lint", "fuzz": "node Makefile.js fuzz", "generate-release": "node Makefile.js generateRelease", "generate-alpharelease": "node Makefile.js generatePrerelease -- alpha", "generate-betarelease": "node Makefile.js generatePrerelease -- beta", "generate-rcrelease": "node Makefile.js generatePrerelease -- rc", "publish-release": "node Makefile.js publishRelease", "docs": "node Makefile.js docs", "gensite": "node Makefile.js gensite", "webpack": "node Makefile.js webpack", "perf": "node Makefile.js perf", "profile": "beefy tests/bench/bench.js --open -- -t brfs -t ./tests/bench/xform-rules.js -r espree", "coveralls": "cat ./coverage/lcov.info | coveralls"}, "files": ["LICENSE", "README.md", "bin", "conf", "lib", "messages"], "repository": "eslint/eslint", "homepage": "https://eslint.org", "bugs": "https://github.com/eslint/eslint/issues/", "dependencies": {"@babel/code-frame": "^7.0.0", "ajv": "^6.9.1", "chalk": "^2.1.0", "cross-spawn": "^6.0.5", "debug": "^4.0.1", "doctrine": "^3.0.0", "eslint-scope": "^4.0.3", "eslint-utils": "^1.3.1", "eslint-visitor-keys": "^1.0.0", "espree": "^5.0.1", "esquery": "^1.0.1", "esutils": "^2.0.2", "file-entry-cache": "^5.0.1", "functional-red-black-tree": "^1.0.1", "glob": "^7.1.2", "globals": "^11.7.0", "ignore": "^4.0.6", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "inquirer": "^6.2.2", "js-yaml": "^3.13.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.3.0", "lodash": "^4.17.11", "minimatch": "^3.0.4", "mkdirp": "^0.5.1", "natural-compare": "^1.4.0", "optionator": "^0.8.2", "path-is-inside": "^1.0.2", "progress": "^2.0.0", "regexpp": "^2.0.1", "semver": "^5.5.1", "strip-ansi": "^4.0.0", "strip-json-comments": "^2.0.1", "table": "^5.2.3", "text-table": "^0.2.0"}, "devDependencies": {"@babel/core": "^7.2.2", "@babel/polyfill": "^7.2.5", "@babel/preset-env": "^7.3.1", "babel-loader": "^8.0.5", "beefy": "^2.1.8", "brfs": "^2.0.0", "chai": "^4.0.1", "cheerio": "^0.22.0", "common-tags": "^1.8.0", "coveralls": "^3.0.1", "dateformat": "^3.0.3", "ejs": "^2.6.1", "eslint-config-eslint": "file:packages/eslint-config-eslint", "eslint-plugin-eslint-plugin": "^2.0.1", "eslint-plugin-internal-rules": "file:tools/internal-rules", "eslint-plugin-node": "^8.0.0", "eslint-release": "^1.2.0", "eslump": "^2.0.0", "esprima": "^4.0.1", "jsdoc": "^3.5.5", "karma": "^3.1.4", "karma-chrome-launcher": "^2.2.0", "karma-mocha": "^1.3.0", "karma-mocha-reporter": "^2.2.3", "karma-webpack": "^4.0.0-rc.6", "leche": "^2.2.3", "load-perf": "^0.2.0", "markdownlint": "^0.12.0", "mocha": "^5.0.5", "mock-fs": "^4.8.0", "npm-license": "^0.3.3", "nyc": "^13.3.0", "proxyquire": "^2.0.1", "puppeteer": "^1.12.2", "shelljs": "^0.8.2", "sinon": "^3.3.0", "temp": "^0.9.0", "through": "^2.3.8", "webpack": "^4.29.3", "webpack-cli": "^3.2.3"}, "keywords": ["ast", "lint", "javascript", "ecmascript", "espree"], "license": "MIT", "engines": {"node": "^6.14.0 || ^8.10.0 || >=9.10.0"}}