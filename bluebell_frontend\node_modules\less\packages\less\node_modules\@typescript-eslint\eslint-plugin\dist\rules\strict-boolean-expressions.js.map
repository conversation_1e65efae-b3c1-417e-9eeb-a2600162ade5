{"version": 3, "file": "strict-boolean-expressions.js", "sourceRoot": "", "sources": ["../../src/rules/strict-boolean-expressions.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,8EAG+C;AAC/C,4DAA4B;AAC5B,iDAAmC;AACnC,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,4BAA4B;IAClC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,oDAAoD;YACjE,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,uBAAuB,EAAE,wCAAwC;SAClE;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,OAAO,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QAChD,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAEjD;;WAEG;QACH,SAAS,aAAa,CAAC,IAAmB;YACxC,MAAM,MAAM,GAAG,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAC9C,IAAI,CACL,CAAC;YACF,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAChE,OAAO,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,oBAAE,CAAC,SAAS,CAAC,WAAW,CAAC,CAAC;QAC/D,CAAC;QAED;;;WAGG;QACH,SAAS,mCAAmC,CAC1C,IAAwB;YAExB,IACE,IAAI,CAAC,IAAI,KAAK,IAAI;gBAClB,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,iBAAiB;gBACnD,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACzB;gBACA,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aACvB;QACH,CAAC;QAED;;WAEG;QACH,SAAS,oCAAoC,CAC3C,IAAgC;YAEhC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE;gBAC3D,UAAU,CAAC,IAAI,CAAC,CAAC;aAClB;QACH,CAAC;QAED;;WAEG;QACH,SAAS,oCAAoC,CAC3C,IAA8B;YAE9B,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;gBACjC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aAC3B;QACH,CAAC;QAED;;WAEG;QACH,SAAS,UAAU,CAAC,IAAmB;YACrC,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,yBAAyB,EAAE,CAAC,CAAC;QACjE,CAAC;QAED,OAAO;YACL,qBAAqB,EAAE,mCAAmC;YAC1D,gBAAgB,EAAE,mCAAmC;YACrD,YAAY,EAAE,mCAAmC;YACjD,WAAW,EAAE,mCAAmC;YAChD,cAAc,EAAE,mCAAmC;YACnD,iBAAiB,EAAE,oCAAoC;YACvD,+BAA+B,EAAE,oCAAoC;SACtE,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}