{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\App.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\App.vue", "mtime": 1596371580000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmltcG9ydCBIZWFkQmFyIGZyb20gIkAvY29tcG9uZW50cy9IZWFkQmFyLnZ1ZSI7CmV4cG9ydCBkZWZhdWx0IHsKICBjb21wb25lbnRzOiB7CiAgICBIZWFkQmFyCiAgfQp9Owo="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAWA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"page\">\n      <HeadBar />\n      <router-view />\n    </div>\n  </div>\n</template>\n\n\n<script>\nimport HeadBar from \"@/components/HeadBar.vue\";\nexport default {\n  components: {\n    HeadBar\n  }\n};\n</script>\n\n<style lang=\"less\">\n@import url(\"./assets/css/iconfont.css\");\nhtml, body{\n    width: 100%;\n    height: 100%;\n    font-family: IBMPlexSans, Arial, sans-serif;\n    background: #eeeeee;\n}\nhtml, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, button, cite, code, del, dfn, em, img, input, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    outline: 0;\n  .page {\n    width: 100%;\n    height: auto;\n  }\n}\n\n</style>\n"]}]}