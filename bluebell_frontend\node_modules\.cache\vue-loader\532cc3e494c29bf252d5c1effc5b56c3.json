{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\SignUp.vue?vue&type=style&index=0&id=6f83b81c&lang=less&scoped=true", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\SignUp.vue", "mtime": 1598770390000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5tYWluIHsKICBiYWNrZ3JvdW5kOiAjZjhmOGY4OwogIHBhZGRpbmc6IDE1MHB4IDA7CiAgLmNvbnRhaW5lciB7CiAgICB3aWR0aDogNjAwcHg7CiAgICBiYWNrZ3JvdW5kOiAjZmZmOwogICAgbWFyZ2luOiAwIGF1dG87CiAgICBtYXgtd2lkdGg6IDEyMDBweDsKICAgIHBhZGRpbmc6IDIwcHg7CiAgICAuZm9ybS10aXRsZSB7CiAgICAgIG1hcmdpbi1ib3R0b206IDMzcHg7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgIH0KICAgIC5mb3JtLWdyb3VwIHsKICAgICAgbWFyZ2luOiAxNXB4OwogICAgICBsYWJlbCB7CiAgICAgICAgZGlzcGxheTogaW5saW5lLWJsb2NrOwogICAgICAgIG1heC13aWR0aDogMTAwJTsKICAgICAgICBtYXJnaW4tYm90dG9tOiA1cHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IDcwMDsKICAgICAgfQogICAgICAuZm9ybS1jb250cm9sIHsKICAgICAgICBkaXNwbGF5OiBibG9jazsKICAgICAgICB3aWR0aDogMTAwJTsKICAgICAgICBoZWlnaHQ6IDM0cHg7CiAgICAgICAgcGFkZGluZzogNnB4IDEycHg7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGxpbmUtaGVpZ2h0OiAxLjQyODU3MTQzOwogICAgICAgIGNvbG9yOiAjNTU1OwogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmZmY7CiAgICAgICAgYmFja2dyb3VuZC1pbWFnZTogbm9uZTsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCAjY2NjOwogICAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgfQogICAgfQogICAgLmZvcm0tYnRuIHsKICAgICAgZGlzcGxheTogZmxleDsKICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgICAgIC5idG4gewogICAgICAgIHBhZGRpbmc6IDZweCAyMHB4OwogICAgICAgIGZvbnQtc2l6ZTogMThweDsKICAgICAgICBsaW5lLWhlaWdodDogMS4zMzMzMzMzOwogICAgICAgIGJvcmRlci1yYWRpdXM6IDZweDsKICAgICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogMDsKICAgICAgICBmb250LXdlaWdodDogNDAwOwogICAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgICB3aGl0ZS1zcGFjZTogbm93cmFwOwogICAgICAgIHZlcnRpY2FsLWFsaWduOiBtaWRkbGU7CiAgICAgICAgLW1zLXRvdWNoLWFjdGlvbjogbWFuaXB1bGF0aW9uOwogICAgICAgIHRvdWNoLWFjdGlvbjogbWFuaXB1bGF0aW9uOwogICAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudDsKICAgICAgfQogICAgICAuYnRuLWluZm8gewogICAgICAgIGNvbG9yOiAjZmZmOwogICAgICAgIGJhY2tncm91bmQtY29sb3I6ICM1YmMwZGU7CiAgICAgICAgYm9yZGVyLWNvbG9yOiAjNDZiOGRhOwogICAgICB9CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["SignUp.vue"], "names": [], "mappings": ";AAiEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "SignUp.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"container\">\n      <h2 class=\"form-title\">注册</h2>\n      <div class=\"form-group\">\n        <label for=\"name\">用户名</label>\n        <input type=\"text\" class=\"form-control\" name=\"name\" id=\"name\" placeholder=\"用户名\" v-model=\"username\"/>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"pass\">密码</label>\n        <input type=\"password\" class=\"form-control\" name=\"pass\" id=\"pass\" placeholder=\"密码\" v-model=\"password\"/>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"re_pass\">确认密码</label>\n        <input type=\"password\" class=\"form-control\" name=\"re_pass\" id=\"re_pass\" placeholder=\"确认密码\"  v-model=\"confirm_password\"/>\n      </div>\n      <div class=\"form-btn\">\n        <button type=\"button\" class=\"btn btn-info\" @click=\"submit\">提交</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n\tname: \"SignUp\",\n\tdata() {\n\t\treturn {\n\t\t\tusername: \"\",\n\t\t\tpassword: \"\",\n\t\t\tconfirm_password: \"\",\n\t\t\tsubmitted: false\n\t\t};\n\t},\n\tcomputed: {\n\t},\n\tcreated() {\n\n\t},\n\tmethods: {\n\t\tsubmit() {\n\t\t\tthis.$axios({\n\t\t\t\tmethod: 'post',\n\t\t\t\turl:'/signup',\n\t\t\t\tdata: JSON.stringify({\n\t\t\t\t\tusername: this.username,\n\t\t\t\t\tpassword: this.password,\n\t\t\t\t\tconfirm_password: this.confirm_password\n\t\t\t\t})\n\t\t\t}).then((res)=>{\n\t\t\t\tconsole.log(res.data);\n\t\t\t\tif (res.code == 1000) {\n          console.log('signup success');\n          this.$router.push({ name: \"Login\" });\n\t\t\t\t}else{\n          console.log(res.msg);\n        }\n\t\t\t}).catch((error)=>{\n\t\t\t\tconsole.log(error)\n\t\t\t})\n\t\t}\n\t}\n};\n</script>\n<style lang=\"less\" scoped>\n.main {\n  background: #f8f8f8;\n  padding: 150px 0;\n  .container {\n    width: 600px;\n    background: #fff;\n    margin: 0 auto;\n    max-width: 1200px;\n    padding: 20px;\n    .form-title {\n      margin-bottom: 33px;\n      text-align: center;\n    }\n    .form-group {\n      margin: 15px;\n      label {\n        display: inline-block;\n        max-width: 100%;\n        margin-bottom: 5px;\n        font-weight: 700;\n      }\n      .form-control {\n        display: block;\n        width: 100%;\n        height: 34px;\n        padding: 6px 12px;\n        font-size: 14px;\n        line-height: 1.42857143;\n        color: #555;\n        background-color: #fff;\n        background-image: none;\n        border: 1px solid #ccc;\n        border-radius: 4px;\n      }\n    }\n    .form-btn {\n      display: flex;\n      justify-content: center;\n      .btn {\n        padding: 6px 20px;\n        font-size: 18px;\n        line-height: 1.3333333;\n        border-radius: 6px;\n        display: inline-block;\n        margin-bottom: 0;\n        font-weight: 400;\n        text-align: center;\n        white-space: nowrap;\n        vertical-align: middle;\n        -ms-touch-action: manipulation;\n        touch-action: manipulation;\n        cursor: pointer;\n        border: 1px solid transparent;\n      }\n      .btn-info {\n        color: #fff;\n        background-color: #5bc0de;\n        border-color: #46b8da;\n      }\n    }\n  }\n}\n</style>"]}]}