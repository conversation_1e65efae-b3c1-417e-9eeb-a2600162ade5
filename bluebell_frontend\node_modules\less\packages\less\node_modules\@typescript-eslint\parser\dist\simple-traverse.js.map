{"version": 3, "file": "simple-traverse.js", "sourceRoot": "", "sources": ["../src/simple-traverse.ts"], "names": [], "mappings": ";;AACA,iDAA6C;AAE7C,SAAS,WAAW,CAAC,CAAM;IACzB,OAAO,CAAC,KAAK,IAAI,IAAI,OAAO,CAAC,KAAK,QAAQ,IAAI,OAAO,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC;AAC3E,CAAC;AAED,SAAS,qBAAqB,CAC5B,cAAkC,EAClC,IAAmB;IAEnB,MAAM,IAAI,GAAG,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,OAAO,IAAI,IAAI,EAAE,CAAC;AACpB,CAAC;AAMD,MAAM,eAAe;IAInB,YAAY,EAAE,KAAK,EAAyB;QAHpC,mBAAc,GAAG,0BAAW,CAAC;QAInC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,QAAQ,CAAC,IAAa,EAAE,MAAiC;QACvD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE;YACtB,OAAO;SACR;QACD,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEzB,MAAM,IAAI,GAAG,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;YACnB,OAAO;SACR;QAED,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE;YACtB,MAAM,eAAe,GAAG,IAAI,CAAC,GAAwB,CAAC,CAAC;YAEvD,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE;gBAClC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE;oBACnC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;iBAC5B;aACF;iBAAM;gBACL,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;aACtC;SACF;IACH,CAAC;CACF;AAED,SAAgB,cAAc,CAC5B,YAA2B,EAC3B,OAA8B;IAE9B,IAAI,eAAe,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;AACjE,CAAC;AALD,wCAKC"}