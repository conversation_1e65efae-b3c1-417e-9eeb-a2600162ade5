{"version": 3, "file": "no-extra-parens.js", "sourceRoot": "", "sources": ["../../src/rules/no-extra-parens.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,8EAI+C;AAC/C,uFAAwD;AACxD,8CAAgC;AAKhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE;YACJ,WAAW,EAAE,kCAAkC;YAC/C,QAAQ,EAAE,iBAAiB;YAC3B,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,MAAM,EAAE,yBAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,yBAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,KAAK,CAAC;IACvB,MAAM,CAAC,OAAO;QACZ,MAAM,KAAK,GAAG,yBAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC,SAAS,SAAS,CAChB,IAA4D;YAE5D,MAAM,IAAI,GAAG,KAAK,CAAC,gBAA4C,CAAC;YAEhE,wDAAwD;YACxD,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;gBACpD,OAAO,IAAI,mBACN,IAAI,IACP,IAAI,oBACC,IAAI,CAAC,IAAI,IACZ,IAAI,EAAE,mCAAc,CAAC,gBAAuB,OAE9C,CAAC;aACJ;YACD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;gBACrD,OAAO,IAAI,mBACN,IAAI,IACP,KAAK,oBACA,IAAI,CAAC,KAAK,IACb,IAAI,EAAE,mCAAc,CAAC,gBAAuB,OAE9C,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,OAAO,CAAC,IAAsD;YACrE,MAAM,IAAI,GAAG,KAAK,CAAC,cAA0C,CAAC;YAE9D,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;gBACtD,+EAA+E;gBAC/E,OAAO,IAAI,mBACN,IAAI,IACP,MAAM,oBACD,IAAI,CAAC,MAAM,IACd,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QACD,SAAS,qBAAqB,CAC5B,IAA0D;YAE1D,MAAM,IAAI,GAAG,KAAK,CAAC,eAA2C,CAAC;YAE/D,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;gBACxD,+EAA+E;gBAC/E,OAAO,IAAI,mBACN,IAAI,IACP,QAAQ,oBACH,IAAI,CAAC,QAAQ,IAChB,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;aACJ;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,CAAC;QACpB,CAAC;QAED,MAAM,SAAS,GAA0B;YACvC,kBAAkB;YAClB,uBAAuB,CAAC,IAAI;gBAC1B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACpD,OAAO,KAAK,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;iBAC5C;YACH,CAAC;YACD,uBAAuB;YACvB,kBAAkB;YAClB,gBAAgB,EAAE,SAAS;YAC3B,cAAc,EAAE,OAAO;YACvB,mBAAmB;YACnB,kBAAkB;YAClB,qBAAqB,CAAC,IAAI;gBACxB,+EAA+E;gBAC/E,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACpD,OAAO,KAAK,CAAC,qBAAqB,mBAC7B,IAAI,IACP,IAAI,oBACC,IAAI,CAAC,IAAI,IACZ,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBAC1D,OAAO,KAAK,CAAC,qBAAqB,mBAC7B,IAAI,IACP,UAAU,oBACL,IAAI,CAAC,UAAU,IAClB,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACzD,8EAA8E;oBAC9E,OAAO,KAAK,CAAC,qBAAqB,mBAC7B,IAAI,IACP,SAAS,oBACJ,IAAI,CAAC,SAAS,IACjB,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBACD,OAAO,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC3C,CAAC;YACD,mBAAmB;YACnB,gCAAgC,CAC9B,IAAuD;gBAEvD,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACrD,4CAA4C;oBAC5C,OAAO,KAAK,CAAC,gCAAgC,CAAC,mBACzC,IAAI,IACP,IAAI,EAAE,mCAAc,CAAC,cAAqB,EAC1C,KAAK,oBACA,IAAI,CAAC,KAAK,IACb,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,gCAAgC,CAAC,CAAC,IAAI,CAAC,CAAC;YACvD,CAAC;YACD,YAAY,CAAC,IAAI;gBACf,uDAAuD;gBACvD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACjE,OAAO,KAAK,CAAC,YAAY,mBACpB,IAAI,IACP,IAAI,EAAE,IAAI,IACV,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACjE,OAAO,KAAK,CAAC,YAAY,mBACpB,IAAI,IACP,IAAI,EAAE,IAAI,IACV,CAAC;iBACJ;gBACD,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACrE,OAAO,KAAK,CAAC,YAAY,mBACpB,IAAI,IACP,MAAM,EAAE,IAAI,IACZ,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;YAClC,CAAC;YACD,cAAc;YACd,iBAAiB,EAAE,SAAS;YAC5B,gBAAgB,CAAC,IAAI;gBACnB,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACtD,+EAA+E;oBAC/E,OAAO,KAAK,CAAC,gBAAgB,mBACxB,IAAI,IACP,MAAM,oBACD,IAAI,CAAC,MAAM,IACd,IAAI,EAAE,mCAAc,CAAC,kBAAyB,OAEhD,CAAC;iBACJ;gBAED,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;YACtC,CAAC;YACD,aAAa,EAAE,OAAO;YACtB,mBAAmB;YACnB,kBAAkB;YAClB,qBAAqB;YACrB,aAAa,CAAC,IAAI;gBAChB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACxD,OAAO,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;iBAClC;YACH,CAAC;YACD,UAAU,CAAC,IAAI;gBACb,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EAAE;oBACjE,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;iBAC/B;YACH,CAAC;YACD,kBAAkB;YAClB,cAAc,CAAC,IAAI;gBACjB,IACE,IAAI,CAAC,QAAQ;oBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EACpD;oBACA,OAAO,KAAK,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;iBACnC;YACH,CAAC;YACD,eAAe,EAAE,qBAAqB;YACtC,gBAAgB,EAAE,qBAAqB;YACvC,qBAAqB;YACrB,iBAAiB;YACjB,iGAAiG;YACjG,eAAe,CAAC,IAAI;gBAClB,IACE,IAAI,CAAC,QAAQ;oBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,EACpD;oBACA,OAAO,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;iBACpC;YACH,CAAC;SACF,CAAC;QACF,OAAO,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,KAAK,EAAE,SAAS,CAAC,CAAC;IAC7C,CAAC;CACF,CAAC,CAAC"}