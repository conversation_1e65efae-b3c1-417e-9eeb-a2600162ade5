# Installation
> `npm install --save @types/jasmine`

# Summary
This package contains type definitions for <PERSON> (http://jasmine.github.io).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jasmine

Additional Details
 * Last updated: Fri, 01 Nov 2019 16:26:22 GMT
 * Dependencies: none
 * Global values: afterAll, afterEach, beforeAll, beforeEach, describe, expect, expectAsync, fail, fdescribe, fit, it, jasmine, pending, runs, spyOn, spyOnAllFunctions, spyOnProperty, waits, waitsFor, xdescribe, xit

# Credits
These definitions were written by <PERSON> <https://github.com/b<PERSON><PERSON><PERSON>>, <PERSON> <https://github.com/theodorejb>, <PERSON> <https://github.com/davidparsson>, <PERSON> <https://github.com/gmoothart>, <PERSON><PERSON> <https://github.com/lukas-zech-software>, <PERSON> <https://github.com/Engineer2B>, <PERSON> <https://github.com/c<PERSON><PERSON>>, <PERSON> <https://github.com/Roaders>, <PERSON><PERSON><PERSON> <https://github.com/devoto13>, Domas Trijonis <https://github.com/fdim>, Moshe Kolodny <https://github.com/kolodny>, Stephen Farrar <https://github.com/stephenfarrar>, Mochamad Arfin <https://github.com/ndunks>, and Alex Povar <https://github.com/zvirja>.
