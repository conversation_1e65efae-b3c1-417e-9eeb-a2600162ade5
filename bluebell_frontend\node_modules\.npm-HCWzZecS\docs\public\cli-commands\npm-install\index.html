<!DOCTYPE html><html><head><script>"use strict";!function(){var i=(window.location.pathname.match(/^(\/(?:ipfs|ipns)\/[^/]+)/)||[])[1]||"";window.__GATSBY_IPFS_PATH_PREFIX__=i}();</script><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><style data-href="../../styles.e93b5499b63484750fba.css">code[class*=language-],pre[class*=language-]{color:#ccc;background:none;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:#2d2d2d}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal}.token.block-comment,.token.cdata,.token.comment,.token.doctype,.token.prolog{color:#999}.token.punctuation{color:#ccc}.token.attr-name,.token.deleted,.token.namespace,.token.tag{color:#e2777a}.token.function-name{color:#6196cc}.token.boolean,.token.function,.token.number{color:#f08d49}.token.class-name,.token.constant,.token.property,.token.symbol{color:#f8c555}.token.atrule,.token.builtin,.token.important,.token.keyword,.token.selector{color:#cc99cd}.token.attr-value,.token.char,.token.regex,.token.string,.token.variable{color:#7ec699}.token.entity,.token.operator,.token.url{color:#67cdcc}.token.bold,.token.important{font-weight:700}.token.italic{font-style:italic}.token.entity{cursor:help}.token.inserted{color:green}a,abbr,acronym,address,applet,article,aside,audio,b,big,blockquote,body,canvas,caption,center,cite,code,dd,del,details,dfn,div,dl,dt,em,embed,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,ins,kbd,label,legend,li,mark,menu,nav,object,ol,output,p,pre,q,ruby,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,u,ul,var,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:after,blockquote:before,q:after,q:before{content:"";content:none}table{border-collapse:collapse;border-spacing:0}[hidden]{display:none}html{font-family:Poppins,sans-serif}*{box-sizing:border-box}li,p{font-size:15px;line-height:1.7;font-weight:300}p,ul{padding:10px 0}strong{font-weight:700;color:#c3f}li{list-style-type:disc;list-style-position:inside;padding:8px 0}.documentation h1{font-size:42px;font-weight:600;padding:30px 0 10px}.documentation h2{font-size:22px;font-weight:300}.documentation h3{color:#c3f;font-size:22px;padding:30px 0 5px;font-weight:500}.documentation h4{font-weight:600;padding:20px 0 5px}.documentation p{display:inline-block}:not(pre)>code[class*=language-],pre[class*=language-]{border-radius:4px;background-color:#413844;font-size:13px}:not(pre)>code[class*=language-text]{background-color:rgba(204,139,216,.1);color:#413844;padding:2px 6px;border-radius:0;font-size:14px;font-weight:700;border-radius:1px;display:inline-block}.documentation a,a>code[class*=language-text]{color:#fb3b49;font-weight:600}p>code[class*=language-text]{display:inline-block}.documentation h1:before{content:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 27 26'%3E%3Cdefs%3E%3ClinearGradient id='a' x1='18.13' x2='25.6' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='.37' stop-color='%23fb8719'/%3E%3Cstop offset='.51' stop-color='%23fa8420'/%3E%3Cstop offset='.61' stop-color='%23f9802c'/%3E%3Cstop offset='.69' stop-color='%23f7793d'/%3E%3Cstop offset='.76' stop-color='%23f47053'/%3E%3Cstop offset='.82' stop-color='%23f1656e'/%3E%3Cstop offset='.87' stop-color='%23ed578f'/%3E%3Cstop offset='.92' stop-color='%23e948b5'/%3E%3Cstop offset='.97' stop-color='%23e437de'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='b' x1='17.89' x2='25.84' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='1' x2='18.69' y1='17.84' y2='17.84' xlink:href='%23a'/%3E%3ClinearGradient id='d' x1='.76' x2='18.93' y1='17.84' y2='17.84' xlink:href='%23b'/%3E%3ClinearGradient id='e' x1='1' x2='20.48' y1='7.33' y2='7.33' xlink:href='%23a'/%3E%3ClinearGradient id='f' x1='.76' x2='20.72' y1='7.33' y2='7.33' xlink:href='%23b'/%3E%3C/defs%3E%3Cpath fill='url(%23a)' stroke='url(%23b)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.34-.41L25 14.06l-5-11a.28.28 0 11.5-.23L25.58 14a.28.28 0 010 .28l-6.91 9.9a.28.28 0 01-.14.06z'/%3E%3Cpath fill='url(%23c)' stroke='url(%23d)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.14 0l-12-1.15a.28.28 0 01-.23-.09L1 11.81a.28.28 0 11.5-.23l5.07 11L18 23.68 13 13a.28.28 0 11.5-.23l5.12 11.12a.28.28 0 01-.09.35z'/%3E%3Cpath fill='url(%23e)' stroke='url(%23f)' stroke-miterlimit='10' stroke-width='.48' d='M13.4 13.12a.25.25 0 01-.14 0L1.25 12a.28.28 0 01-.2-.44L8 1.64a.28.28 0 01.25-.12l12 1.18a.28.28 0 01.2.44L13.51 13a.25.25 0 01-.11.12z'/%3E%3C/svg%3E");position:relative;display:inline-block;padding-right:8px;top:3px;width:28px}.active-sidebar-link{background-color:#ffebff}.active-navbar-link{border-bottom:3px solid #c3f}.header-link-class{margin-left:-24px}.disabled-body{overflow:hidden}</style><meta name="generator" content="Gatsby 2.18.18"/><title data-react-helmet="true"></title><style data-styled="UihHA jAtLxz bCnUTx bAGJfc hJcdbU kOyZtC eCQAUi fsnHHg bXQeSB dsecBh iPgskl bNiGAM gJQTGP fMOzaj" data-styled-version="4.4.1">
/* sc-component-id: links__NavLink-sc-19vgq0o-1 */
.kOyZtC{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .kOyZtC:hover{opacity:.5;}
/* sc-component-id: links__BasicNavLink-sc-19vgq0o-2 */
.eCQAUi{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .eCQAUi:hover{opacity:.5;}
/* sc-component-id: links__SidebarLink-sc-19vgq0o-3 */
.iPgskl{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#fb3b49;padding:10px;-webkit-transition:background-color .3s;transition:background-color .3s;} .iPgskl:hover{background-color:#ffebff;}
/* sc-component-id: Accordion__SectionButton-i8yhwx-0 */
.dsecBh{outline:none;background-color:transparent;cursor:pointer;color:red;border:none;font-size:18px;font-weight:bold;padding:5px 0;-webkit-transition:opacity .5s;transition:opacity .5s;} .dsecBh:after{background:center / contain no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNi41IDEwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZiM2I0OTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnVwLWNhcnJvdDwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNOC4yNS44NWExLjE1LDEuMTUsMCwwLDAtLjgxLjM0bC02LDZBMS4xNSwxLjE1LDAsMCwwLDMuMDYsOC44MUw4LjI1LDMuNjNsNS4xOSw1LjE5YTEuMTUsMS4xNSwwLDAsMCwxLjYzLTEuNjNsLTYtNkExLjE1LDEuMTUsMCwwLDAsOC4yNS44NVoiLz48L3N2Zz4=);content:'';height:11px;width:28px;display:inline-block;} .dsecBh:hover{opacity:.6;}
/* sc-component-id: DocLinks__LinkDesc-sc-1vrw6od-0 */
.bNiGAM{font-size:11px;line-height:1.5;text-transform:lowercase;display:block;font-weight:400;color:#767676;}
/* sc-component-id: Sidebar__Container-gs0c67-0 */
.bXQeSB{border-right:1px solid #86838333;padding:30px;height:100vh;display:none;width:380px;position:-webkit-sticky;position:sticky;overflow:scroll;padding-bottom:200px;top:54px;background-color:#ffffff;} @media screen and (min-width:48em){.bXQeSB{display:block;}}
/* sc-component-id: navbar__Container-kjuegf-0 */
.UihHA{width:100%;border-bottom:1px solid #86838333;position:-webkit-sticky;position:sticky;top:0;background-color:#ffffff;z-index:1;}
/* sc-component-id: navbar__Inner-kjuegf-1 */
.jAtLxz{border-top:3px solid;border-image:linear-gradient(139deg,#fb8817,#ff4b01,#c12127,#e02aff) 3;margin:auto;height:53px;padding:0 30px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}
/* sc-component-id: navbar__Logo-kjuegf-2 */
.bAGJfc{width:120px;padding:0px 5px;height:18px;vertical-align:middle;display:inline-block;-webkit-transition:opacity .5s;transition:opacity .5s;} .bAGJfc:hover{opacity:.8;}
/* sc-component-id: navbar__Links-kjuegf-3 */
.hJcdbU{display:none;} @media screen and (min-width:48em){.hJcdbU{display:block;margin-left:auto;}}
/* sc-component-id: navbar__Heart-kjuegf-4 */
.bCnUTx{font-size:15px;display:inline-block;}
/* sc-component-id: navbar__Hamburger-kjuegf-5 */
.fsnHHg{border:none;background:center no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMzUgMjMiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudCk7fS5jbHMtMntmaWxsOnVybCgjbGluZWFyLWdyYWRpZW50LTIpO30uY2xzLTN7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudC0zKTt9PC9zdHlsZT48bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhci1ncmFkaWVudCIgeTE9IjIiIHgyPSIzNSIgeTI9IjIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBvZmZzZXQ9IjAiIHN0b3AtY29sb3I9IiNmYjg4MTciLz48c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNlMDJhZmYiLz48L2xpbmVhckdyYWRpZW50PjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTIiIHkxPSIxMS41IiB5Mj0iMTEuNSIgeGxpbms6aHJlZj0iI2xpbmVhci1ncmFkaWVudCIvPjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTMiIHkxPSIyMSIgeTI9IjIxIiB4bGluazpocmVmPSIjbGluZWFyLWdyYWRpZW50Ii8+PC9kZWZzPjx0aXRsZT5oYW1idXJnZXI8L3RpdGxlPjxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjxyZWN0IGNsYXNzPSJjbHMtMiIgeT0iOS41IiB3aWR0aD0iMzUiIGhlaWdodD0iNCIgcng9IjIiIHJ5PSIyIi8+PHJlY3QgY2xhc3M9ImNscy0zIiB5PSIxOSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjwvc3ZnPg==);height:30px;width:30px;display:block;margin-left:auto;-webkit-transition:opacity .5s;transition:opacity .5s;cursor:pointer;} .fsnHHg:hover{opacity:.6;} @media screen and (min-width:48em){.fsnHHg{display:none;}}
/* sc-component-id: FoundTypo__Container-sc-1e373sc-0 */
.fMOzaj{margin:80px 0;border-top:1px solid black;padding:20px 0;}
/* sc-component-id: Page__Content-sc-4b62ym-0 */
.gJQTGP{max-width:760px;margin:auto;padding:0 30px 120px;}</style><link rel="icon" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="manifest" href="../../manifest.webmanifest"/><meta name="theme-color" content="#663399"/><link rel="apple-touch-icon" sizes="48x48" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="72x72" href="../../icons/icon-72x72.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="96x96" href="../../icons/icon-96x96.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="144x144" href="../../icons/icon-144x144.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="192x192" href="../../icons/icon-192x192.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="256x256" href="../../icons/icon-256x256.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="384x384" href="../../icons/icon-384x384.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="512x512" href="../../icons/icon-512x512.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2"/><style type="text/css">@font-face{font-family:Poppins;font-style:normal;font-weight:300;src:local('Poppins Light'),local('Poppins-Light'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:400;src:local('Poppins Regular'),local('Poppins-Regular'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:500;src:local('Poppins Medium'),local('Poppins-Medium'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:400;src:local('Inconsolata Regular'),local('Inconsolata-Regular'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5q4.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:700;src:local('Inconsolata Bold'),local('Inconsolata-Bold'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_o.woff) format('woff');font-display: swap;}</style><style type="text/css">
    .header-link-class.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }
    .header-link-class.after {
      display: inline-block;
      padding-left: 4px;
    }
    h1 .header-link-class svg,
    h2 .header-link-class svg,
    h3 .header-link-class svg,
    h4 .header-link-class svg,
    h5 .header-link-class svg,
    h6 .header-link-class svg {
      visibility: hidden;
    }
    h1:hover .header-link-class svg,
    h2:hover .header-link-class svg,
    h3:hover .header-link-class svg,
    h4:hover .header-link-class svg,
    h5:hover .header-link-class svg,
    h6:hover .header-link-class svg,
    h1 .header-link-class:focus svg,
    h2 .header-link-class:focus svg,
    h3 .header-link-class:focus svg,
    h4 .header-link-class:focus svg,
    h5 .header-link-class:focus svg,
    h6 .header-link-class:focus svg {
      visibility: visible;
    }
  </style><script>
    document.addEventListener("DOMContentLoaded", function(event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var offset = element.offsetTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function() {
            window.scrollTo(0, offset - 100)
          }), 0)
        }
      }
    })
  </script><link as="script" rel="preload" href="../../webpack-runtime-b622568e0ef6e093f777.js"/><link as="script" rel="preload" href="../../styles-de5e304580bcba768a01.js"/><link as="script" rel="preload" href="../../commons-4df35f6dbd2fdc25d817.js"/><link as="script" rel="preload" href="../../app-041f7e4f56e7debd8d98.js"/><link as="script" rel="preload" href="../../component---src-templates-page-js-7faf8ceb01991e80d244.js"/><link as="fetch" rel="preload" href="../../page-data/cli-commands/npm-install/page-data.json" crossorigin="anonymous"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" role="group" id="gatsby-focus-wrapper"><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="navbar__Container-kjuegf-0 UihHA css-4cffwv"><div class="navbar__Inner-kjuegf-1 jAtLxz css-4cffwv"><a href="../../"><style data-emotion-css="26z63x">.css-26z63x{box-sizing:border-box;margin:0;min-width:0;margin-left:4px;margin-right:24px;}</style><div class="navbar__Heart-kjuegf-4 bCnUTx css-26z63x">❤</div><style data-emotion-css="9taffg">.css-9taffg{box-sizing:border-box;margin:0;min-width:0;max-width:100%;height:auto;}</style><img src="data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDcgMTciPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMjMxZjIwO30uY2xzLTJ7ZmlsbDpub25lO308L3N0eWxlPjwvZGVmcz48dGl0bGU+Y2xpLWxvZ288L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTS41NCwxMy40aDYuNFYzLjY3aDMuMlYxMy40aDMuMlYuNDJILjU0Wk0zMS4yNi40MnYxM2g2LjRWMy42N2gzLjJWMTMuNGgzLjJWMy42N2gzLjE5VjEzLjRoMy4yVi40MlptLTksMy4yNWgzLjJ2Ni40OUgyMi4zWm0tNi40LDEzaDYuNFYxMy40aDYuNFYuNDJIMTUuOVoiLz48cmVjdCBjbGFzcz0iY2xzLTIiIHg9IjAuNTQiIHk9IjAuNDIiIHdpZHRoPSI0OS45MSIgaGVpZ2h0PSIxNi4yMiIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI2NS41OCAzLjU2IDY1LjU4IDkuODYgNzEuNjYgOS44NiA3MS42NiAxMy4wMiA2NS40NCAxMy4wMiA1OS4yIDEzLjA0IDU5LjIyIDAuNDEgNzEuNjYgMC40MSA3MS42NiAzLjU0IDY1LjU4IDMuNTYiLz48cG9seWdvbiBjbGFzcz0iY2xzLTEiIHBvaW50cz0iODAuNjIgMTAuMjMgODAuNjIgMC4zNiA3NC4yMyAwLjM2IDc0LjIzIDEzLjMgNzYuOTIgMTMuMyA4MC42MiAxMy4zIDg2LjQ3IDEzLjMgODYuNDcgMTAuMjMgODAuNjIgMTAuMjMiLz48cmVjdCBjbGFzcz0iY2xzLTEiIHg9IjEwMS4zMiIgeT0iOC4zNyIgd2lkdGg9IjEuOTkiIGhlaWdodD0iOC4yOSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTE0LjgzIC04OS43OSkgcm90YXRlKDkwKSIvPjxyZWN0IGNsYXNzPSJjbHMtMSIgeD0iODguMzMiIHk9IjAuMzYiIHdpZHRoPSI2LjM5IiBoZWlnaHQ9IjEyLjk0Ii8+PC9zdmc+" class="navbar__Logo-kjuegf-2 bAGJfc css-9taffg"/></a><ul class="navbar__Links-kjuegf-3 hJcdbU"><a class="links__NavLink-sc-19vgq0o-1 kOyZtC" href="../../cli-commands/npm/index.html">docs</a><a href="https://www.npmjs.com/" class="links__BasicNavLink-sc-19vgq0o-2 eCQAUi">npmjs.org</a></ul><button class="navbar__Hamburger-kjuegf-5 fsnHHg"></button></div></div><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-4cffwv"><nav class="Sidebar__Container-gs0c67-0 bXQeSB sidebar"><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">cli-commands</button><div><style data-emotion-css="l3rx45">.css-l3rx45{box-sizing:border-box;margin:0;min-width:0;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm">npm<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">javascript package manager</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-access">npm access<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Set access level on published packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-audit">npm audit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run a security audit</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bin">npm bin<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm bin folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bugs">npm bugs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bugs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-build">npm build<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Build a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bundle">npm bundle<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">REMOVED</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-cache">npm cache<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manipulates packages cache</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ci">npm ci<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-completion">npm completion<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Tab Completion for npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-config">npm config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage the npm configuration files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-dedupe">npm dedupe<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Reduce duplication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-deprecate">npm deprecate<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Deprecate a version of a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-docs">npm docs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Docs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-doctor">npm doctor<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check your environments</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-edit">npm edit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Edit an installed package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-fund">npm fund<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Retrieve funding information</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help">npm help<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Get help on npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help-search">npm help-search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search npm help documentation</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-hook">npm hook<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage registry hooks</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-init">npm init<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">create a package.json file</span></a></div><div class="css-l3rx45"><a aria-current="page" class="links__SidebarLink-sc-19vgq0o-3 iPgskl active-sidebar-link" href="../../cli-commands/npm-install">npm install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-ci-test">npm install-ci-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-test">npm install-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install package(s) and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-link">npm link<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Symlink a package folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-logout">npm logout<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Log out of the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ls">npm ls<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">List installed packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-org">npm org<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-outdated">npm outdated<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check for outdated packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-owner">npm owner<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage package owners</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-pack">npm pack<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Create a tarball from a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ping">npm ping<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Ping npm registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prefix">npm prefix<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display prefix</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-profile">npm profile<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Change settings on your registry profile</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prune">npm prune<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove extraneous packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-publish">npm publish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Publish a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-rebuild">npm rebuild<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Rebuild a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-repo">npm repo<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Open package repository page in the browser</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-restart">npm restart<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Restart a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-root">npm root<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm root</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-run-script">npm run-script<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run arbitrary package scripts</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-search">npm search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search for packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-shrinkwrap">npm shrinkwrap<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Lock down dependency versions for publication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-star">npm star<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Mark your favorite packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stars">npm stars<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View packages marked as favorites</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-start">npm start<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Start a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stop">npm stop<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Stop a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-team">npm team<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage organization teams and team memberships</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-test">npm test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Test a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-token">npm token<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage your authentication tokens</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-uninstall">npm uninstall<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-unpublish">npm unpublish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package from the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-update">npm update<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Update a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-version">npm version<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bump a package version</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-view">npm view<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View registry info</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-whoami">npm whoami<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm username</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">configuring-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/folders">folders<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Folder Structures Used by npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/install">install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Download and install node and npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/npmrc">npmrc<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The npm config files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-lock-json">package-lock.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A manifestation of the manifest</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-locks">package-locks<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">An explanation of npm lockfiles</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-json">package.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Specifics of npm&#x27;s package.json handling</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/shrinkwrap-json">shrinkwrap.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A publishable lockfile</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">using-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/config">config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">More than you probably want to know about npm configuration</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/developers">developers<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Developer Guide</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/disputes">disputes<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Handling Module Name Disputes</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/orgs">orgs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Working with Teams &amp; Orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/registry">registry<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The JavaScript Package Registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/removal">removal<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Cleaning the Slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scope">scope<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Scoped packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scripts">scripts<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">How npm handles the &quot;scripts&quot; field</span></a></div></div></div></nav><style data-emotion-css="16vu25q">.css-16vu25q{box-sizing:border-box;margin:0;min-width:0;width:100%;}</style><div class="css-16vu25q"><div class="Page__Content-sc-4b62ym-0 gJQTGP documentation"><div><h1 id="npm-install1" style="position:relative;"><a href="#npm-install1" aria-label="npm install1 permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>npm install</h1>
<h2 id="install-a-package" style="position:relative;"><a href="#install-a-package" aria-label="install a package permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Install a package</h2>
<h3 id="synopsis" style="position:relative;"><a href="#synopsis" aria-label="synopsis permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Synopsis</h3>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> <span class="token punctuation">(</span>with no args, <span class="token keyword">in</span> package <span class="token function">dir</span><span class="token punctuation">)</span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token punctuation">[</span><span class="token operator">&lt;</span>@scope<span class="token operator">></span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>name<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token punctuation">[</span><span class="token operator">&lt;</span>@scope<span class="token operator">></span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>name<span class="token operator">></span>@<span class="token operator">&lt;</span>tag<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token punctuation">[</span><span class="token operator">&lt;</span>@scope<span class="token operator">></span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>name<span class="token operator">></span>@<span class="token operator">&lt;</span>version<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token punctuation">[</span><span class="token operator">&lt;</span>@scope<span class="token operator">></span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>name<span class="token operator">></span>@<span class="token operator">&lt;</span>version range<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>alias<span class="token operator">></span>@npm:<span class="token operator">&lt;</span>name<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>git-host<span class="token operator">></span>:<span class="token operator">&lt;</span>git-user<span class="token operator">></span>/<span class="token operator">&lt;</span>repo-name<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>git repo url<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>tarball file<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>tarball url<span class="token operator">></span>
<span class="token function">npm</span> <span class="token function">install</span> <span class="token operator">&lt;</span>folder<span class="token operator">></span>

aliases: <span class="token function">npm</span> i, <span class="token function">npm</span> <span class="token function">add</span>
common options: <span class="token punctuation">[</span>-P<span class="token operator">|</span>--save-prod<span class="token operator">|</span>-D<span class="token operator">|</span>--save-dev<span class="token operator">|</span>-O<span class="token operator">|</span>--save-optional<span class="token punctuation">]</span> <span class="token punctuation">[</span>-E<span class="token operator">|</span>--save-exact<span class="token punctuation">]</span> <span class="token punctuation">[</span>-B<span class="token operator">|</span>--save-bundle<span class="token punctuation">]</span> <span class="token punctuation">[</span>--no-save<span class="token punctuation">]</span> <span class="token punctuation">[</span>--dry-run<span class="token punctuation">]</span></code></pre></div>
<h3 id="description" style="position:relative;"><a href="#description" aria-label="description permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Description</h3>
<p>This command installs a package, and any packages that it depends on. If the
package has a package-lock or shrinkwrap file, the installation of dependencies
will be driven by that, with an <code class="language-text">npm-shrinkwrap.json</code> taking precedence if both
files exist. See <a href="../../configuring-npm/package-lock-json">package-lock.json</a> and <a href="../../cli-commands/shrinkwrap"><code class="language-text">npm shrinkwrap</code></a>.</p>
<p>A <code class="language-text">package</code> is:</p>
<ul>
<li>a) a folder containing a program described by a <a href="../../configuring-npm/package-json"><code class="language-text">package.json</code></a> file</li>
<li>b) a gzipped tarball containing (a)</li>
<li>c) a url that resolves to (b)</li>
<li>d) a <code class="language-text">&lt;name&gt;@&lt;version&gt;</code> that is published on the registry (see <a href="../../using-npm/registry"><code class="language-text">registry</code></a>) with (c)</li>
<li>e) a <code class="language-text">&lt;name&gt;@&lt;tag&gt;</code> (see <a href="../../cli-commands/dist-tag"><code class="language-text">npm dist-tag</code></a>) that points to (d)</li>
<li>f) a <code class="language-text">&lt;name&gt;</code> that has a "latest" tag satisfying (e)</li>
<li>g) a <code class="language-text">&lt;git remote url&gt;</code> that resolves to (a)</li>
</ul>
<p>Even if you never publish your package, you can still get a lot of
benefits of using npm if you just want to write a node program (a), and
perhaps if you also want to be able to easily install it elsewhere
after packing it up into a tarball (b).</p>
<ul>
<li>
<p><code class="language-text">npm install</code> (in package directory, no arguments):</p>
<p>Install the dependencies in the local node_modules folder.</p>
<p>In global mode (ie, with <code class="language-text">-g</code> or <code class="language-text">--global</code> appended to the command),
it installs the current package context (ie, the current working
directory) as a global package.</p>
<p>By default, <code class="language-text">npm install</code> will install all modules listed as dependencies
in <a href="../../configuring-npm/package-json"><code class="language-text">package.json</code></a>.</p>
<p>With the <code class="language-text">--production</code> flag (or when the <code class="language-text">NODE_ENV</code> environment variable
is set to <code class="language-text">production</code>), npm will not install modules listed in
<code class="language-text">devDependencies</code>. To install all modules listed in both <code class="language-text">dependencies</code>
and <code class="language-text">devDependencies</code> when <code class="language-text">NODE_ENV</code> environment variable is set to <code class="language-text">production</code>,
you can use <code class="language-text">--production=false</code>.</p>
<blockquote>
<p>NOTE: The <code class="language-text">--production</code> flag has no particular meaning when adding a
dependency to a project.</p>
</blockquote>
</li>
<li>
<p><code class="language-text">npm install &lt;folder&gt;</code>:</p>
<p>Install the package in the directory as a symlink in the current project.
Its dependencies will be installed before it's linked. If <code class="language-text">&lt;folder&gt;</code> sits
inside the root of your project, its dependencies may be hoisted to the
toplevel <code class="language-text">node_modules</code> as they would for other types of dependencies.</p>
</li>
<li>
<p><code class="language-text">npm install &lt;tarball file&gt;</code>:</p>
<p>Install a package that is sitting on the filesystem.  Note: if you just want
to link a dev directory into your npm root, you can do this more easily by
using <code class="language-text">npm link</code>.</p>
<p>Tarball requirements:</p>
<ul>
<li>The filename <em>must</em> use <code class="language-text">.tar</code>, <code class="language-text">.tar.gz</code>, or <code class="language-text">.tgz</code> as
the extension.</li>
<li>The package contents should reside in a subfolder inside the tarball (usually it is called <code class="language-text">package/</code>). npm strips one directory layer when installing the package (an equivalent of <code class="language-text">tar x --strip-components=1</code> is run).</li>
<li>The package must contain a <code class="language-text">package.json</code> file with <code class="language-text">name</code> and <code class="language-text">version</code> properties.</li>
</ul>
<p>Example:</p>
<div class="gatsby-highlight" data-language="text"><pre class="language-text"><code class="language-text">  npm install ./package.tgz</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install &lt;tarball url&gt;</code>:</p>
<p>Fetch the tarball url, and then install it.  In order to distinguish between
this and other options, the argument must start with "http://" or "https://"</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="text"><pre class="language-text"><code class="language-text">  npm install https://github.com/indexzero/forever/tarball/v0.5.6</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install [&lt;@scope&gt;/]&lt;name&gt;</code>:</p>
<p>Do a <code class="language-text">&lt;name&gt;@&lt;tag&gt;</code> install, where <code class="language-text">&lt;tag&gt;</code> is the "tag" config. (See
<a href="../../using-npm/config"><code class="language-text">config</code></a>. The config's default value is <code class="language-text">latest</code>.)</p>
<p>In most cases, this will install the version of the modules tagged as
<code class="language-text">latest</code> on the npm registry.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="text"><pre class="language-text"><code class="language-text">  npm install sax</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install &lt;alias&gt;@npm:&lt;name&gt;</code>:</p>
<p>Install a package under a custom alias. Allows multiple versions of
a same-name package side-by-side, more convenient import names for
packages with otherwise long ones and using git forks replacements
or forked npm packages as replacements. Aliasing works only on your
project and does not rename packages in transitive dependencies.
Aliases should follow the naming conventions stated in
<a href="https://www.npmjs.com/package/validate-npm-package-name#naming-rules"><code class="language-text">validate-npm-package-name</code></a>.</p>
<p>Examples:</p>
<div class="gatsby-highlight" data-language="text"><pre class="language-text"><code class="language-text">  npm install my-react@npm:react
  npm install jquery2@npm:jquery@2
  npm install jquery3@npm:jquery@3
  npm install npa@npm:npm-package-arg</code></pre></div>
<p><code class="language-text">npm install</code> saves any specified packages into <code class="language-text">dependencies</code> by default.
Additionally, you can control where and how they get saved with some
additional flags:</p>
<ul>
<li><code class="language-text">-P, --save-prod</code>: Package will appear in your <code class="language-text">dependencies</code>. This is the
default unless <code class="language-text">-D</code> or <code class="language-text">-O</code> are present.</li>
<li><code class="language-text">-D, --save-dev</code>: Package will appear in your <code class="language-text">devDependencies</code>.</li>
<li><code class="language-text">-O, --save-optional</code>: Package will appear in your <code class="language-text">optionalDependencies</code>.</li>
<li><code class="language-text">--no-save</code>: Prevents saving to <code class="language-text">dependencies</code>.</li>
</ul>
<p>When using any of the above options to save dependencies to your
package.json, there are two additional, optional flags:</p>
<ul>
<li><code class="language-text">-E, --save-exact</code>: Saved dependencies will be configured with an
exact version rather than using npm's default semver range
operator.</li>
<li><code class="language-text">-B, --save-bundle</code>: Saved dependencies will also be added to your <code class="language-text">bundleDependencies</code> list.</li>
</ul>
<p>Further, if you have an <code class="language-text">npm-shrinkwrap.json</code> or <code class="language-text">package-lock.json</code> then it
will be updated as well.</p>
<p><code class="language-text">&lt;scope&gt;</code> is optional. The package will be downloaded from the registry
associated with the specified scope. If no registry is associated with
the given scope the default registry is assumed. See <a href="../../using-npm/scope"><code class="language-text">scope</code></a>.</p>
<p>Note: if you do not include the @-symbol on your scope name, npm will
interpret this as a GitHub repository instead, see below. Scopes names
must also be followed by a slash.</p>
<p>Examples:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax
<span class="token function">npm</span> <span class="token function">install</span> githubname/reponame
<span class="token function">npm</span> <span class="token function">install</span> @myorg/privatepackage
<span class="token function">npm</span> <span class="token function">install</span> node-tap --save-dev
<span class="token function">npm</span> <span class="token function">install</span> dtrace-provider --save-optional
<span class="token function">npm</span> <span class="token function">install</span> readable-stream --save-exact
<span class="token function">npm</span> <span class="token function">install</span> ansi-regex --save-bundle</code></pre></div>
<p><strong>Note</strong>: If there is a file or folder named <code class="language-text">&lt;name&gt;</code> in the current
working directory, then it will try to install that, and only try to
fetch the package by name if it is not valid.</p>
</li>
<li>
<p><code class="language-text">npm install [&lt;@scope&gt;/]&lt;name&gt;@&lt;tag&gt;</code>:</p>
<p>Install the version of the package that is referenced by the specified tag.
If the tag does not exist in the registry data for that package, then this
will fail.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax@latest
<span class="token function">npm</span> <span class="token function">install</span> @myorg/mypackage@latest</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install [&lt;@scope&gt;/]&lt;name&gt;@&lt;version&gt;</code>:</p>
<p>Install the specified version of the package.  This will fail if the
version has not been published to the registry.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax@0.1.1
<span class="token function">npm</span> <span class="token function">install</span> @myorg/privatepackage@1.5.0</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install [&lt;@scope&gt;/]&lt;name&gt;@&lt;version range&gt;</code>:</p>
<p>Install a version of the package matching the specified version range.  This
will follow the same rules for resolving dependencies described in <a href="../../configuring-npm/package-json"><code class="language-text">package.json</code></a>.</p>
<p>Note that most version ranges must be put in quotes so that your shell will
treat it as a single argument.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax@<span class="token string">">=0.1.0 &lt;0.2.0"</span>
<span class="token function">npm</span> <span class="token function">install</span> @myorg/privatepackage@<span class="token string">">=0.1.0 &lt;0.2.0"</span></code></pre></div>
</li>
<li>
<p><code class="language-text">npm install &lt;git remote url&gt;</code>:</p>
<p>Installs the package from the hosted git provider, cloning it with <code class="language-text">git</code>.
For a full git remote url, only that URL will be attempted.</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">  <span class="token operator">&lt;</span>protocol<span class="token operator">></span>://<span class="token punctuation">[</span><span class="token operator">&lt;</span>user<span class="token operator">></span><span class="token punctuation">[</span>:<span class="token operator">&lt;</span>password<span class="token operator">></span><span class="token punctuation">]</span>@<span class="token punctuation">]</span><span class="token operator">&lt;</span>hostname<span class="token operator">></span><span class="token punctuation">[</span>:<span class="token operator">&lt;</span>port<span class="token operator">></span><span class="token punctuation">]</span><span class="token punctuation">[</span>:<span class="token punctuation">]</span><span class="token punctuation">[</span>/<span class="token punctuation">]</span><span class="token operator">&lt;</span>path<span class="token operator">></span><span class="token punctuation">[</span><span class="token comment">#&lt;commit-ish> | #semver:&lt;semver>]</span></code></pre></div>
<p><code class="language-text">&lt;protocol&gt;</code> is one of <code class="language-text">git</code>, <code class="language-text">git+ssh</code>, <code class="language-text">git+http</code>, <code class="language-text">git+https</code>, or
<code class="language-text">git+file</code>.</p>
<p>If <code class="language-text">#&lt;commit-ish&gt;</code> is provided, it will be used to clone exactly that
commit. If the commit-ish has the format <code class="language-text">#semver:&lt;semver&gt;</code>, <code class="language-text">&lt;semver&gt;</code> can
be any valid semver range or exact version, and npm will look for any tags
or refs matching that range in the remote repository, much as it would for a
registry dependency. If neither <code class="language-text">#&lt;commit-ish&gt;</code> or <code class="language-text">#semver:&lt;semver&gt;</code> is
specified, then the default branch of the repository is used.</p>
<p>If the repository makes use of submodules, those submodules will be cloned
as well.</p>
<p>If the package being installed contains a <code class="language-text">prepare</code> script, its
<code class="language-text">dependencies</code> and <code class="language-text">devDependencies</code> will be installed, and the prepare
script will be run, before the package is packaged and installed.</p>
<p>The following git environment variables are recognized by npm and will be
added to the environment when running git:</p>
<ul>
<li><code class="language-text">GIT_ASKPASS</code></li>
<li><code class="language-text">GIT_EXEC_PATH</code></li>
<li><code class="language-text">GIT_PROXY_COMMAND</code></li>
<li><code class="language-text">GIT_SSH</code></li>
<li><code class="language-text">GIT_SSH_COMMAND</code></li>
<li><code class="language-text">GIT_SSL_CAINFO</code></li>
<li><code class="language-text">GIT_SSL_NO_VERIFY</code></li>
</ul>
<p>See the git man page for details.</p>
<p>Examples:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> git+ssh://**************:npm/cli.git<span class="token comment">#v1.0.27</span>
<span class="token function">npm</span> <span class="token function">install</span> git+ssh://**************:npm/cli<span class="token comment">#semver:^5.0</span>
<span class="token function">npm</span> <span class="token function">install</span> git+https://<EMAIL>/npm/cli.git
<span class="token function">npm</span> <span class="token function">install</span> git://github.com/npm/cli.git<span class="token comment">#v1.0.27</span>
<span class="token assign-left variable">GIT_SSH_COMMAND</span><span class="token operator">=</span><span class="token string">'ssh -i ~/.ssh/custom_ident'</span> <span class="token function">npm</span> <span class="token function">install</span> git+ssh://**************:npm/cli.git</code></pre></div>
</li>
<li><code class="language-text">npm install &lt;githubname&gt;/&lt;githubrepo&gt;[#&lt;commit-ish&gt;]</code>:</li>
<li>
<p><code class="language-text">npm install github:&lt;githubname&gt;/&lt;githubrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>Install the package at <code class="language-text">https://github.com/githubname/githubrepo</code> by
attempting to clone it using <code class="language-text">git</code>.</p>
<p>If <code class="language-text">#&lt;commit-ish&gt;</code> is provided, it will be used to clone exactly that
commit. If the commit-ish has the format <code class="language-text">#semver:&lt;semver&gt;</code>, <code class="language-text">&lt;semver&gt;</code> can
be any valid semver range or exact version, and npm will look for any tags
or refs matching that range in the remote repository, much as it would for a
registry dependency. If neither <code class="language-text">#&lt;commit-ish&gt;</code> or <code class="language-text">#semver:&lt;semver&gt;</code> is
specified, then <code class="language-text">master</code> is used.</p>
<p>As with regular git dependencies, <code class="language-text">dependencies</code> and <code class="language-text">devDependencies</code> will
be installed if the package has a <code class="language-text">prepare</code> script, before the package is
done installing.</p>
<p>Examples:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> mygithubuser/myproject
<span class="token function">npm</span> <span class="token function">install</span> github:mygithubuser/myproject</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install gist:[&lt;githubname&gt;/]&lt;gistID&gt;[#&lt;commit-ish&gt;|#semver:&lt;semver&gt;]</code>:</p>
<p>Install the package at <code class="language-text">https://gist.github.com/gistID</code> by attempting to
clone it using <code class="language-text">git</code>. The GitHub username associated with the gist is
optional and will not be saved in <code class="language-text">package.json</code>.</p>
<p>As with regular git dependencies, <code class="language-text">dependencies</code> and <code class="language-text">devDependencies</code> will
be installed if the package has a <code class="language-text">prepare</code> script, before the package is
done installing.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> gist:101a11beef</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install bitbucket:&lt;bitbucketname&gt;/&lt;bitbucketrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>Install the package at <code class="language-text">https://bitbucket.org/bitbucketname/bitbucketrepo</code>
by attempting to clone it using <code class="language-text">git</code>.</p>
<p>If <code class="language-text">#&lt;commit-ish&gt;</code> is provided, it will be used to clone exactly that
commit. If the commit-ish has the format <code class="language-text">#semver:&lt;semver&gt;</code>, <code class="language-text">&lt;semver&gt;</code> can
be any valid semver range or exact version, and npm will look for any tags
or refs matching that range in the remote repository, much as it would for a
registry dependency. If neither <code class="language-text">#&lt;commit-ish&gt;</code> or <code class="language-text">#semver:&lt;semver&gt;</code> is
specified, then <code class="language-text">master</code> is used.</p>
<p>As with regular git dependencies, <code class="language-text">dependencies</code> and <code class="language-text">devDependencies</code> will
be installed if the package has a <code class="language-text">prepare</code> script, before the package is
done installing.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> bitbucket:mybitbucketuser/myproject</code></pre></div>
</li>
<li>
<p><code class="language-text">npm install gitlab:&lt;gitlabname&gt;/&lt;gitlabrepo&gt;[#&lt;commit-ish&gt;]</code>:</p>
<p>Install the package at <code class="language-text">https://gitlab.com/gitlabname/gitlabrepo</code>
by attempting to clone it using <code class="language-text">git</code>.</p>
<p>If <code class="language-text">#&lt;commit-ish&gt;</code> is provided, it will be used to clone exactly that
commit. If the commit-ish has the format <code class="language-text">#semver:&lt;semver&gt;</code>, <code class="language-text">&lt;semver&gt;</code> can
be any valid semver range or exact version, and npm will look for any tags
or refs matching that range in the remote repository, much as it would for a
registry dependency. If neither <code class="language-text">#&lt;commit-ish&gt;</code> or <code class="language-text">#semver:&lt;semver&gt;</code> is
specified, then <code class="language-text">master</code> is used.</p>
<p>As with regular git dependencies, <code class="language-text">dependencies</code> and <code class="language-text">devDependencies</code> will
be installed if the package has a <code class="language-text">prepare</code> script, before the package is
done installing.</p>
<p>Example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> gitlab:mygitlabuser/myproject
<span class="token function">npm</span> <span class="token function">install</span> gitlab:myusr/myproj<span class="token comment">#semver:^5.0</span></code></pre></div>
</li>
</ul>
<p>You may combine multiple arguments, and even multiple types of arguments.
For example:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax@<span class="token string">">=0.1.0 &lt;0.2.0"</span> bench supervisor</code></pre></div>
<p>The <code class="language-text">--tag</code> argument will apply to all of the specified install targets. If a
tag with the given name exists, the tagged version is preferred over newer
versions.</p>
<p>The <code class="language-text">--dry-run</code> argument will report in the usual way what the install would
have done without actually installing anything.</p>
<p>The <code class="language-text">--package-lock-only</code> argument will only update the <code class="language-text">package-lock.json</code>,
instead of checking <code class="language-text">node_modules</code> and downloading dependencies.</p>
<p>The <code class="language-text">-f</code> or <code class="language-text">--force</code> argument will force npm to fetch remote resources even if a
local copy exists on disk.</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> <span class="token function">install</span> sax --force</code></pre></div>
<p>The <code class="language-text">--no-fund</code> argument will hide the message displayed at the end of each
install that acknowledges the number of dependencies looking for funding.
See <code class="language-text">npm-fund(1)</code></p>
<p>The <code class="language-text">-g</code> or <code class="language-text">--global</code> argument will cause npm to install the package globally
rather than locally.  See <a href="../../configuring-npm/folders">folders</a>.</p>
<p>The <code class="language-text">--global-style</code> argument will cause npm to install the package into
your local <code class="language-text">node_modules</code> folder with the same layout it uses with the
global <code class="language-text">node_modules</code> folder. Only your direct dependencies will show in
<code class="language-text">node_modules</code> and everything they depend on will be flattened in their
<code class="language-text">node_modules</code> folders. This obviously will eliminate some deduping.</p>
<p>The <code class="language-text">--ignore-scripts</code> argument will cause npm to not execute any
scripts defined in the package.json. See <a href="../../using-npm/scripts"><code class="language-text">scripts</code></a>.</p>
<p>The <code class="language-text">--legacy-bundling</code> argument will cause npm to install the package such
that versions of npm prior to 1.4, such as the one included with node 0.8,
can install the package. This eliminates all automatic deduping.</p>
<p>The <code class="language-text">--link</code> argument will cause npm to link global installs into the
local space in some cases.</p>
<p>The <code class="language-text">--no-bin-links</code> argument will prevent npm from creating symlinks for
any binaries the package might contain.</p>
<p>The <code class="language-text">--no-optional</code> argument will prevent optional dependencies from
being installed.</p>
<p>The <code class="language-text">--no-shrinkwrap</code> argument, which will ignore an available
package lock or shrinkwrap file and use the package.json instead.</p>
<p>The <code class="language-text">--no-package-lock</code> argument will prevent npm from creating a
<code class="language-text">package-lock.json</code> file.  When running with package-lock's disabled npm
will not automatically prune your node modules when installing.</p>
<p>The <code class="language-text">--nodedir=/path/to/node/source</code> argument will allow npm to find the
node source code so that npm can compile native modules.</p>
<p>The <code class="language-text">--only={prod[uction]|dev[elopment]}</code> argument will cause either only
<code class="language-text">devDependencies</code> or only non-<code class="language-text">devDependencies</code> to be installed regardless of the <code class="language-text">NODE_ENV</code>.</p>
<p>The <code class="language-text">--no-audit</code> argument can be used to disable sending of audit reports to
the configured registries.  See <a href="npm-audit"><code class="language-text">npm-audit</code></a> for details on what is sent.</p>
<p>See <a href="../../using-npm/config"><code class="language-text">config</code></a>.  Many of the configuration params have some
effect on installation, since that's most of what npm does.</p>
<h4 id="algorithm" style="position:relative;"><a href="#algorithm" aria-label="algorithm permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Algorithm</h4>
<p>To install a package, npm uses the following algorithm:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">load the existing node_modules tree from disk
clone the tree
fetch the package.json and assorted metadata and <span class="token function">add</span> it to the clone
walk the clone and <span class="token function">add</span> any missing dependencies
  dependencies will be added as close to the <span class="token function">top</span> as is possible
  without breaking any other modules
compare the original tree with the cloned tree and <span class="token function">make</span> a list of
actions to take to convert one to the other
execute all of the actions, deepest first
  kinds of actions are install, update, remove and move</code></pre></div>
<p>For this <code class="language-text">package{dep}</code> structure: <code class="language-text">A{B,C}, B{C}, C{D}</code>,
this algorithm produces:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">A
+-- B
+-- C
+-- D</code></pre></div>
<p>That is, the dependency from B to C is satisfied by the fact that A
already caused C to be installed at a higher level. D is still installed
at the top level because nothing conflicts with it.</p>
<p>For <code class="language-text">A{B,C}, B{C,D@1}, C{D@2}</code>, this algorithm produces:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">A
+-- B
+-- C
   `-- D@2
+-- D@1</code></pre></div>
<p>Because B's D@1 will be installed in the top level, C now has to install D@2
privately for itself. This algorithm is deterministic, but different trees may
be produced if two dependencies are requested for installation in a different
order.</p>
<p>See <a href="../../configuring-npm/folders">folders</a> for a more detailed description of the specific folder structures that npm creates.</p>
<h3 id="limitations-of-npms-install-algorithm" style="position:relative;"><a href="#limitations-of-npms-install-algorithm" aria-label="limitations of npms install algorithm permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Limitations of npm's Install Algorithm</h3>
<p>npm will refuse to install any package with an identical name to the
current package. This can be overridden with the <code class="language-text">--force</code> flag, but in
most cases can simply be addressed by changing the local package name.</p>
<p>There are some very rare and pathological edge-cases where a cycle can
cause npm to try to install a never-ending tree of packages.  Here is
the simplest case:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">A -<span class="token operator">></span> B -<span class="token operator">></span> A<span class="token string">' -> B'</span> -<span class="token operator">></span> A -<span class="token operator">></span> B -<span class="token operator">></span> A<span class="token string">' -> B'</span> -<span class="token operator">></span> A -<span class="token operator">></span> <span class="token punctuation">..</span>.</code></pre></div>
<p>where <code class="language-text">A</code> is some version of a package, and <code class="language-text">A&#39;</code> is a different version
of the same package.  Because <code class="language-text">B</code> depends on a different version of <code class="language-text">A</code>
than the one that is already in the tree, it must install a separate
copy.  The same is true of <code class="language-text">A&#39;</code>, which must install <code class="language-text">B&#39;</code>.  Because <code class="language-text">B&#39;</code>
depends on the original version of <code class="language-text">A</code>, which has been overridden, the
cycle falls into infinite regress.</p>
<p>To avoid this situation, npm flat-out refuses to install any
<code class="language-text">name@version</code> that is already present anywhere in the tree of package
folder ancestors.  A more correct, but more complex, solution would be
to symlink the existing version into the new location.  If this ever
affects a real use-case, it will be investigated.</p>
<h3 id="see-also" style="position:relative;"><a href="#see-also" aria-label="see also permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>See Also</h3>
<ul>
<li><a href="../../configuring-npm/folders">npm folders</a></li>
<li><a href="../../cli-commands/update">npm update</a></li>
<li><a href="../../cli-commands/audit">npm audit</a></li>
<li><a href="../../cli-commands/fund">npm fund</a></li>
<li><a href="../../cli-commands/link">npm link</a></li>
<li><a href="../../cli-commands/rebuild">npm rebuild</a></li>
<li><a href="../../using-npm/scripts">npm scripts</a></li>
<li><a href="../../cli-commands/build">npm build</a></li>
<li><a href="../../cli-commands/config">npm config</a></li>
<li><a href="../../configuring-npm/npmrc">npmrc</a></li>
<li><a href="../../using-npm/registry">npm registry</a></li>
<li><a href="../../cli-commands/dist-tag">npm dist-tag</a></li>
<li><a href="../../cli-commands/uninstall">npm uninstall</a></li>
<li><a href="../../cli-commands/shrinkwrap">npm shrinkwrap</a></li>
<li><a href="../../configuring-npm/package-json">package.json</a></li>
</ul></div><div class="FoundTypo__Container-sc-1e373sc-0 fMOzaj"><p><span role="img" aria-label="eyes-emoji">👀</span> Found a typo? <a href="https://github.com/npm/cli/">Let us know!</a></p><p>The current stable version of npm is <a href="https://github.com/npm/cli/">here</a>. To upgrade, run: <code class="language-text">npm install npm@latest -g</code></p><p>To report bugs or submit feature requests for the docs, please post <a href="https://npm.community/c/support/docs-needed">here</a>. Submit npm issues <a href="https://npm.community/c/bugs">here.</a></p></div><script>
          var anchors = document.querySelectorAll(".sidebar a, .documentation a")
          Array.prototype.slice.call(anchors).map(function(el) {
            if (el.href.match(/file:\/\//)) {
              el.href = el.href + "/index.html"
            }
          })
          </script></div></div></div></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/cli-commands/npm-install";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-041f7e4f56e7debd8d98.js"],"component---src-templates-page-js":["/component---src-templates-page-js-7faf8ceb01991e80d244.js"],"component---src-pages-404-js":["/component---src-pages-404-js-6c8c4e2e908a7101a231.js"],"component---src-pages-index-js":["/component---src-pages-index-js-6b93f80c513be8d7330c.js"]};/*]]>*/</script><script src="../../component---src-templates-page-js-7faf8ceb01991e80d244.js" async=""></script><script src="../../app-041f7e4f56e7debd8d98.js" async=""></script><script src="../../commons-4df35f6dbd2fdc25d817.js" async=""></script><script src="../../styles-de5e304580bcba768a01.js" async=""></script><script src="../../webpack-runtime-b622568e0ef6e093f777.js" async=""></script></body></html>