{"version": 3, "file": "no-floating-promises.js", "sourceRoot": "", "sources": ["../../src/rules/no-floating-promises.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,iDAAmC;AACnC,+CAAiC;AAEjC,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;YACxE,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,QAAQ,EAAE,wCAAwC;SACnD;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE,EAAE;IAElB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,MAAM,EAAE,UAAU,EAAE,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAC7D,IAAI,CACqB,CAAC;gBAE5B,IAAI,kBAAkB,CAAC,OAAO,EAAE,UAAU,CAAC,EAAE;oBAC3C,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,UAAU;wBACrB,IAAI;qBACL,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,kBAAkB,CAAC,OAAuB,EAAE,IAAa;IAChE,yEAAyE;IACzE,IACE,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;QAC3B,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU,EACpD;QACA,uEAAuE;QACvE,yEAAyE;QACzE,yBAAyB;QACzB,OAAO,CACL,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC;YACtC,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CACxC,CAAC;KACH;SAAM,IAAI,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;QACpC,yEAAyE;QACzE,4EAA4E;QAC5E,OAAO,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,CAAC,CAAC;KACrD;IAED,4EAA4E;IAC5E,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE;QACjC,OAAO,KAAK,CAAC;KACd;IAED,IAAI,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,EAAE;QAC7B,sEAAsE;QACtE,uCAAuC;QACvC,OAAO,CACL,CAAC,6BAA6B,CAAC,IAAI,CAAC;YACpC,CAAC,qCAAqC,CAAC,IAAI,CAAC,CAC7C,CAAC;KACH;SAAM,IAAI,EAAE,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;QAC3C,4EAA4E;QAC5E,gCAAgC;QAChC,OAAO,CACL,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC;YAC3C,kBAAkB,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,CAAC,CAC3C,CAAC;KACH;SAAM,IACL,EAAE,CAAC,0BAA0B,CAAC,IAAI,CAAC;QACnC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC;QACrB,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,EACxB;QACA,2EAA2E;QAC3E,2EAA2E;QAC3E,qDAAqD;QACrD,OAAO,IAAI,CAAC;KACb;IAED,4EAA4E;IAC5E,8EAA8E;IAC9E,sBAAsB;IACtB,OAAO,KAAK,CAAC;AACf,CAAC;AAED,6EAA6E;AAC7E,0EAA0E;AAC1E,EAAE;AACF,0GAA0G;AAC1G,SAAS,aAAa,CAAC,OAAuB,EAAE,IAAa;IAC3D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAC7C,KAAK,MAAM,EAAE,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE;QACtE,MAAM,IAAI,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,SAAS,EAAE;YACtB,SAAS;SACV;QAED,MAAM,QAAQ,GAAG,OAAO,CAAC,yBAAyB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QAC/D,IACE,oBAAoB,CAClB,QAAQ,EACR,SAAS,CAAC,EAAE,CACV,SAAS,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC;YAChC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC;YACvD,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAC1D,EACD;YACA,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,oBAAoB,CAC3B,IAAa,EACb,OAA6C;IAE7C,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,eAAe,CACtB,OAAuB,EACvB,KAAgB,EAChB,IAAa;IAEb,MAAM,IAAI,GAAwB,OAAO,CAAC,eAAe,CACvD,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAC/C,CAAC;IACF,KAAK,MAAM,CAAC,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,6BAA6B,CAAC,UAA6B;IAClE,OAAO,CACL,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,UAAU,CAAC;QACzD,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,OAAO;QAC3C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC;AAED,SAAS,qCAAqC,CAC5C,UAA6B;IAE7B,OAAO,CACL,OAAO,CAAC,0BAA0B,CAAC,UAAU,CAAC,UAAU,CAAC;QACzD,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM;QAC1C,UAAU,CAAC,SAAS,CAAC,MAAM,IAAI,CAAC,CACjC,CAAC;AACJ,CAAC"}