<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1600.3 426" style="enable-background:new 0 0 1600.3 426;" xml:space="preserve">
<style type="text/css">
	.st0{opacity:0.3;fill:url(#SVGID_1_);}
	.st1{opacity:0.3;fill:url(#SVGID_2_);}
	.st2{opacity:0.1;fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st3{opacity:0.3;fill:#E8D9D9;}
	.st4{opacity:0.5;fill:url(#SVGID_3_);}
	.st5{opacity:0.3;fill:url(#SVGID_4_);}
	.st6{opacity:0.3;fill:url(#SVGID_5_);}
	.st7{fill:#F6D2C9;}
	.st8{fill:#FFFFFF;}
	.st9{fill:#FF2EDD;}
	.st10{fill:none;stroke:url(#SVGID_6_);stroke-width:3;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#B3B3B3;stroke-width:0.75;stroke-miterlimit:10;}
	.st12{fill:none;stroke:url(#SVGID_7_);stroke-miterlimit:10;}
	.st13{fill:none;stroke:url(#SVGID_8_);stroke-width:3;stroke-miterlimit:10;}
	.st14{fill:#FB3B49;}
	.st15{fill:url(#SVGID_9_);}
	.st16{opacity:0.7;}
	.st17{fill:url(#SVGID_10_);}
	.st18{fill:#333333;}
	.st19{opacity:0.2;fill:#FB3B49;}
	.st20{opacity:0.3;fill:url(#SVGID_11_);}
	.st21{fill:none;stroke:url(#SVGID_12_);stroke-width:3;stroke-miterlimit:10;}
	.st22{fill:url(#SVGID_13_);}
	.st23{fill:url(#SVGID_14_);}
	.st24{fill:none;stroke:url(#SVGID_15_);stroke-width:10.069;stroke-miterlimit:10;}
	.st25{fill:none;stroke:url(#SVGID_16_);stroke-width:10.069;stroke-miterlimit:10;}
	.st26{fill:none;stroke:url(#SVGID_17_);stroke-width:3;stroke-miterlimit:10;}
	.st27{clip-path:url(#XMLID_6_);}
	.st28{opacity:0.3;fill:url(#SVGID_18_);}
	.st29{fill:none;stroke:url(#SVGID_19_);stroke-width:3;stroke-miterlimit:10;}
	.st30{fill:url(#SVGID_20_);}
	.st31{fill:url(#SVGID_21_);}
	.st32{fill:none;stroke:url(#SVGID_22_);stroke-width:3;stroke-miterlimit:10;}
	.st33{opacity:0.8;}
	.st34{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st35{fill:#7C2EDD;}
	.st36{fill:none;stroke:url(#SVGID_23_);stroke-width:3;stroke-miterlimit:10;}
	.st37{fill:none;stroke:url(#SVGID_24_);stroke-width:3;stroke-miterlimit:10;}
	.st38{fill:none;stroke:#B3B3B3;stroke-miterlimit:10;}
	.st39{fill:none;stroke:#B3B3B3;stroke-width:1.1228;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#B3B3B3;stroke-width:1.2168;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#333333;stroke-miterlimit:10;}
	.st42{fill:url(#SVGID_25_);}
	.st43{fill:url(#SVGID_26_);}
	.st44{fill:url(#SVGID_27_);}
	.st45{fill:url(#SVGID_28_);}
	.st46{fill:#231F20;}
	.st47{fill:none;}
	.st48{opacity:0.6;fill:url(#SVGID_29_);}
	.st49{fill:none;stroke:url(#SVGID_30_);stroke-miterlimit:10;}
	.st50{fill:none;stroke:#B3B3B3;stroke-width:0.7877;stroke-miterlimit:10;}
	.st51{opacity:0.9;}
	.st52{opacity:0.1;}
	.st53{fill:none;stroke:#808080;stroke-miterlimit:10;}
	.st54{opacity:5.000000e-02;}
	.st55{fill:none;stroke:#FF00FF;stroke-miterlimit:10;}
	.st56{fill:url(#SVGID_31_);}
	.st57{fill:url(#SVGID_32_);}
	.st58{opacity:0.19;fill:url(#SVGID_33_);}
	.st59{fill:none;stroke:url(#SVGID_34_);stroke-width:3;stroke-miterlimit:10;}
	.st60{opacity:0.19;fill:url(#SVGID_35_);}
	.st61{opacity:0.5;fill:#FFFFFF;}
	.st62{fill:none;stroke:#333333;stroke-width:2;stroke-miterlimit:10;}
	.st63{opacity:0.19;fill:url(#SVGID_36_);}
	.st64{fill:#333333;stroke:#333333;stroke-miterlimit:10;}
	.st65{opacity:0.19;fill:url(#SVGID_37_);}
	.st66{fill:none;stroke:#333333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st67{fill:none;stroke:url(#SVGID_38_);stroke-width:3;stroke-miterlimit:10;}
	.st68{opacity:0.6;fill:url(#SVGID_39_);}
	.st69{opacity:0.4;fill:url(#SVGID_40_);}
	.st70{opacity:0.4;fill:url(#SVGID_41_);}
	.st71{opacity:0.4;fill:url(#SVGID_42_);}
	.st72{fill:#F2F2F2;}
	.st73{opacity:0.4;fill:url(#SVGID_43_);}
	.st74{fill:#413844;stroke:#223839;stroke-miterlimit:10;}
	
		.st75{fill:#FFFFFF;fill-opacity:0.5;stroke:#223839;stroke-width:1.802;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st76{fill:url(#SVGID_44_);}
	.st77{fill:url(#SVGID_45_);}
	.st78{fill:url(#SVGID_46_);}
	.st79{fill:url(#SVGID_47_);}
	.st80{fill:url(#SVGID_48_);}
	.st81{fill:none;stroke:#223839;stroke-width:2;stroke-miterlimit:10;}
	.st82{fill:url(#SVGID_49_);}
	.st83{fill:url(#SVGID_50_);}
	.st84{fill:url(#SVGID_51_);}
	.st85{fill:url(#SVGID_52_);}
	.st86{fill:url(#SVGID_53_);}
	.st87{fill:url(#SVGID_54_);}
	.st88{fill:url(#SVGID_55_);}
	.st89{fill:url(#SVGID_56_);}
	.st90{fill:url(#SVGID_57_);}
	.st91{fill:url(#SVGID_58_);}
	.st92{fill:#FF00FF;}
	.st93{fill:#7457D9;}
	.st94{opacity:0.3;fill:url(#SVGID_59_);}
	.st95{fill:none;stroke:url(#SVGID_60_);stroke-width:3;stroke-miterlimit:10;}
	.st96{fill:#333333;stroke:#333333;stroke-width:1.0718;stroke-miterlimit:10;}
	.st97{fill:none;stroke:url(#SVGID_61_);stroke-miterlimit:10;}
	.st98{fill:#413844;}
	.st99{fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st100{opacity:0.6;fill:url(#SVGID_62_);}
	.st101{opacity:0.4;fill:url(#SVGID_63_);}
	.st102{opacity:0.4;fill:url(#SVGID_64_);}
	.st103{opacity:0.4;fill:url(#SVGID_65_);}
	.st104{opacity:0.4;fill:url(#SVGID_66_);}
	.st105{fill:url(#SVGID_67_);}
	.st106{fill:url(#SVGID_68_);}
	.st107{fill:url(#SVGID_69_);}
	.st108{fill:url(#SVGID_70_);}
	.st109{fill:url(#SVGID_71_);}
	.st110{fill:url(#SVGID_72_);}
	.st111{fill:url(#SVGID_73_);}
	.st112{fill:url(#SVGID_74_);}
	.st113{fill:url(#SVGID_75_);}
	.st114{fill:url(#SVGID_76_);}
	.st115{fill:url(#SVGID_77_);}
	.st116{fill:url(#SVGID_78_);}
	.st117{fill:url(#SVGID_79_);}
	.st118{fill:url(#SVGID_80_);}
	.st119{fill:url(#SVGID_81_);}
	.st120{fill:none;stroke:#FF00FF;stroke-miterlimit:10;stroke-dasharray:40,2;}
	.st121{fill:url(#SVGID_82_);stroke:url(#SVGID_83_);stroke-width:0.742;stroke-miterlimit:10;}
	.st122{fill:url(#SVGID_84_);stroke:url(#SVGID_85_);stroke-width:0.742;stroke-miterlimit:10;}
	.st123{fill:url(#SVGID_86_);stroke:url(#SVGID_87_);stroke-width:0.742;stroke-miterlimit:10;}
	.st124{fill:url(#SVGID_88_);}
	.st125{fill:url(#SVGID_89_);}
	.st126{fill:url(#SVGID_90_);}
	.st127{opacity:0.9;fill:url(#SVGID_91_);}
	.st128{fill:none;stroke:url(#SVGID_92_);stroke-width:3;stroke-miterlimit:10;}
	.st129{fill:none;stroke:url(#SVGID_93_);stroke-width:3;stroke-miterlimit:10;}
	.st130{opacity:0.1;fill:none;stroke:#4D4D4D;stroke-miterlimit:10;}
	.st131{fill:#ED1C24;}
	.st132{fill:#666666;}
	.st133{opacity:0.2;fill:#D4BEB8;}
	.st134{fill:none;stroke:#FB3B49;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st135{opacity:8.000000e-02;fill:#CC33FF;}
	.st136{fill:#CC33FF;}
	.st137{fill:#AF2AF7;}
	.st138{opacity:0.3;fill:url(#SVGID_94_);}
	.st139{fill:none;stroke:#F2F2F2;stroke-miterlimit:10;}
	.st140{fill:url(#SVGID_95_);stroke:url(#SVGID_96_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st141{fill:url(#SVGID_97_);stroke:url(#SVGID_98_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st142{fill:url(#SVGID_99_);stroke:url(#SVGID_100_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st143{fill:none;stroke:#FB3B49;stroke-miterlimit:10;}
	.st144{fill:url(#SVGID_101_);stroke:url(#SVGID_102_);stroke-width:0.742;stroke-miterlimit:10;}
	.st145{fill:url(#SVGID_103_);}
	.st146{fill:url(#SVGID_104_);}
	.st147{fill:none;stroke:url(#SVGID_105_);stroke-miterlimit:10;}
	.st148{fill:url(#SVGID_106_);stroke:url(#SVGID_107_);stroke-width:0.742;stroke-miterlimit:10;}
	.st149{fill:url(#SVGID_108_);stroke:url(#SVGID_109_);stroke-width:0.742;stroke-miterlimit:10;}
	.st150{fill:url(#SVGID_110_);stroke:url(#SVGID_111_);stroke-width:0.742;stroke-miterlimit:10;}
	.st151{fill:none;stroke:#FF00FF;stroke-width:0.6009;stroke-miterlimit:10;stroke-dasharray:24.0344,1.2017;}
	.st152{fill:none;stroke:#FB3B49;stroke-width:0.6009;stroke-miterlimit:10;}
	.st153{fill:url(#SVGID_112_);stroke:url(#SVGID_113_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st154{fill:url(#SVGID_114_);}
	.st155{fill:url(#SVGID_115_);}
	.st156{fill:url(#SVGID_116_);}
	.st157{fill:url(#SVGID_117_);}
	.st158{opacity:0.9;fill:url(#SVGID_118_);}
	.st159{fill:url(#SVGID_119_);stroke:url(#SVGID_120_);stroke-width:0.742;stroke-miterlimit:10;}
	.st160{fill:url(#SVGID_121_);stroke:url(#SVGID_122_);stroke-width:0.742;stroke-miterlimit:10;}
	.st161{fill:url(#SVGID_123_);stroke:url(#SVGID_124_);stroke-width:0.742;stroke-miterlimit:10;}
	.st162{fill:url(#SVGID_125_);stroke:url(#SVGID_126_);stroke-width:0.742;stroke-miterlimit:10;}
	.st163{fill:url(#SVGID_127_);}
	.st164{fill:url(#SVGID_128_);}
	.st165{opacity:0.9;fill:url(#SVGID_129_);}
	.st166{fill:url(#SVGID_130_);}
	.st167{opacity:0.9;fill:url(#SVGID_131_);}
	.st168{fill:url(#SVGID_132_);stroke:url(#SVGID_133_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st169{fill:url(#SVGID_134_);}
	.st170{fill:url(#SVGID_135_);}
	.st171{opacity:0.9;fill:url(#SVGID_136_);}
	.st172{fill:url(#SVGID_137_);}
	.st173{opacity:0.9;fill:url(#SVGID_138_);}
	.st174{fill:url(#SVGID_139_);}
	.st175{opacity:0.9;fill:url(#SVGID_140_);}
</style>
<g id="Layer_1">
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="4447.9849" y1="-1992.9341" x2="3672.0149" y2="-1068.1691">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.5"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="3261" y="-1648.1" class="st0" width="1598" height="235"/>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="1337.01" y1="-3594.5083" x2="304.99" y2="-2364.5947">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="22" y="-3357.1" class="st1" width="1598" height="755"/>
	<path class="st2" d="M4549.4-1824.1l-790.9,0c-1.4,0-2.6-1.2-2.6-2.6v-239.8c0-1.4,1.2-2.6,2.6-2.6l790.9,0c1.4,0,2.6,1.2,2.6,2.6
		v239.8C4552-1825.2,4550.8-1824.1,4549.4-1824.1z"/>
	<rect x="-1601" y="-2611.1" class="st3" width="1598" height="1797"/>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-3219" y1="-2989.0515" x2="-1621" y2="-2989.0515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st4" points="-1621,-2613.1 -1621,-3365.1 -3219,-3365.1 -3219,-2614.6 	"/>
	<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="-4839" y1="-3087.1721" x2="-3241" y2="-3087.1721">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<polygon class="st5" points="-3241,-2813.8 -3241,-3366.1 -4839,-3366.1 -4839,-2808.3 	"/>
	<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="-4880.6743" y1="-5406.9058" x2="-3201.3259" y2="-4988.1973">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<rect x="-4839" y="-5574.1" class="st6" width="1596" height="753"/>
	<g>
		<g>
			<rect x="-5828.6" y="-2814.7" class="st7" width="318" height="1481"/>
		</g>
		<g>
			<rect x="-5836" y="-2822.1" class="st8" width="318" height="1481"/>
		</g>
	</g>
	<rect x="-4794" y="-5613.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -4788 -5598.8521)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -3828.0918 -5597.7505)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-4840" y1="-5632.0518" x2="-3241" y2="-5632.0518">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st10" x1="-4840" y1="-5632.1" x2="-3241" y2="-5632.1"/>
	<line class="st11" x1="-3240.5" y1="-5576.1" x2="-4840" y2="-5576.1"/>
	<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="-2844.8535" y1="-3189.3015" x2="-2843.6465" y2="-3189.3015">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st12" x1="-2844" y1="-3189.1" x2="-2844.5" y2="-3189.6"/>
	<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="-3464" y1="-5591.0518" x2="-3429.5" y2="-5591.0518">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st13" x1="-3464" y1="-5591.1" x2="-3429.5" y2="-5591.1"/>
	<rect x="-4143" y="-5113.1" class="st14" width="276" height="71"/>
	<g>
		<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="-4266.5444" y1="-5425.7017" x2="-4239.4526" y2="-5425.7017">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st15" points="-4261.4,-5407 -4266.5,-5413.1 -4251.8,-5425.7 -4266.5,-5438.3 -4261.3,-5444.4 -4239.5,-5425.6 		
			"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-3776.0264" y1="-5397.5586" x2="-3745.5" y2="-5397.5586">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-3776" y="-5401.6" class="st17" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="1618" height="1337" xlink:href="7C982DBF67AE2D7E.png"  transform="matrix(1 0 0 1 -4850 -2806.0515)">
		</image>
		<g>
			<rect x="-4841" y="-2792.1" class="st18" width="1600" height="1319"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="7C982DBF67AE2D82.png"  transform="matrix(1 0 0 1 -4219 -5471.0518)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-4167.1-5394.7h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-5394.7z"/>
				<path class="st18" d="M-4151.5-5463.1h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-5463.1z M-4139-5452.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-4139z"/>
				<path class="st18" d="M-4092.2-5463.1h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-5463.1
					z"/>
				<path class="st18" d="M-3930.2-5443.8c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-3930.2-5443.8z"/>
				<path class="st18" d="M-3860.7-5405v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-3860.7z"/>
				<path class="st18" d="M-3840.9-5463.1h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-5463.1z"/>
			</g>
		</g>
	</g>
	<rect x="-4373.6" y="-5308.6" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -4373.584 -5286.4229)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">The</tspan><tspan x="61" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="81.8" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">intelligent</tspan><tspan x="255.3" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="276.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">package</tspan><tspan x="424.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="444.9" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">manager</tspan><tspan x="600.4" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="621.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">for</tspan><tspan x="668.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="689" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">the </tspan><tspan x="0" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Node</tspan><tspan x="87.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="101.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Javascript</tspan><tspan x="282.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="296.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Platform.</tspan><tspan x="452.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="466.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Install</tspan><tspan x="572.3" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="586.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">stuff</tspan><tspan x="664.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="678.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">and </tspan><tspan x="275.1" y="86" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">get coding!</tspan></text>
	<rect x="-4136" y="-5106.1" class="st19" width="276" height="71"/>
	
		<text transform="matrix(1 0 0 1 -4093.0112 -5068.6401)" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Read Docs</text>
	<path class="st18" d="M-3236-4885.6c18.3,18.3-25.9-40-51.8-40c-25.9,0-25.9,40-51.8,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		s-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40s-25.9-40-51.7-40
		s-25.9,40-51.7,40s-25.9-40-51.7-40v1283.5h1603.5C-3236.5-3642.1-3238.4-4887.9-3236-4885.6z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="7C982DBF67AE2D85.png"  transform="matrix(1 0 0 1 -4600 -4776.0518)">
		</image>
		<g>
			<circle class="st8" cx="-4427" cy="-4603.1" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="7C982DBF67AE2D86.png"  transform="matrix(1 0 0 1 -4607 -4077.0515)">
		</image>
		<g>
			<circle class="st8" cx="-4434" cy="-3904.1" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="7C982DBF67AE2D84.png"  transform="matrix(1 0 0 1 -3784 -4352.0518)">
		</image>
		<g>
			<circle class="st8" cx="-3611" cy="-4179.1" r="128"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -4018.6538 -3114.9739)"><tspan x="0" y="0" style="font-family:'MyriadPro-Regular'; font-size:30px; letter-spacing:1;">❤</tspan><tspan x="16.8" y="0" style="font-family:'MonotypeSorts'; font-size:30px; letter-spacing:1;">,</tspan></text>
	<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="-3219" y1="-5262.4517" x2="-1623" y2="-5262.4517">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st20" d="M-1623-5578.1v630.9c-21-2.9-22.7-23.8-46.8-23.8c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2s-25.9-24.2-51.8-24.2s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-24.5,0-25.8-21.6-47.8-24v-607.2H-1623z"/>
	<g>
		
			<image style="overflow:visible;" width="1608" height="1247" xlink:href="7C982DBF67AE2D83.png"  transform="matrix(1 0 0 1 -3227 -4957.0518)">
		</image>
		<g>
			<path class="st18" d="M-1623-4925.2v1211.1h-1596v-1234.8c22,2.4,23.3,24,47.8,24c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				s25.9,24.2,51.8,24.2s25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2
				c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2C-1645.7-4949-1644-4928.1-1623-4925.2z"/>
		</g>
	</g>
	<rect x="-3174" y="-5611.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -3168 -5596.8521)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -2208.0918 -5601.7505)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-1620.5" y1="-5578.1" x2="-3220" y2="-5578.1"/>
	<linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="-1844" y1="-5595.0518" x2="-1809.5" y2="-5595.0518">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st21" x1="-1844" y1="-5595.1" x2="-1809.5" y2="-5595.1"/>
	<rect x="-2550.8" y="-5190.1" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="-2688.5444" y1="-5394.7017" x2="-2661.4526" y2="-5394.7017">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st22" points="-2683.4,-5376 -2688.5,-5382.1 -2673.8,-5394.7 -2688.5,-5407.3 -2683.3,-5413.4 -2661.5,-5394.6 		
			"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="-2198.0264" y1="-5366.5586" x2="-2167.5" y2="-5366.5586">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-2198" y="-5370.6" class="st23" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="7C982DBF67AE2D8D.png"  transform="matrix(1 0 0 1 -2641 -5440.0518)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-2589.1-5363.7h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-5363.7z"/>
				<path class="st18" d="M-2573.5-5432.1h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-5432.1z M-2561-5421.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-2561z"/>
				<path class="st18" d="M-2514.2-5432.1h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-5432.1
					z"/>
				<path class="st18" d="M-2352.2-5412.8c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-2352.2-5412.8z"/>
				<path class="st18" d="M-2282.7-5374v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-2282.7z"/>
				<path class="st18" d="M-2262.9-5432.1h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-5432.1z"/>
			</g>
		</g>
	</g>
	<rect x="-2795.6" y="-5299.6" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -2648.5601 -5284.8228)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-2545" y="-5184.2" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -2494.0112 -5151.6401)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="7C982DBF67AE2D8E.png"  transform="matrix(1 0 0 1 -3045.6223 -4851.6738)">
		</image>
		<g>
			<ellipse class="st8" cx="-2891.2" cy="-4699.6" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="-1623.7075" y1="-4902.9917" x2="-1618" y2="-4902.9917">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st24" d="M-1618-4902.9c-1.8,0-3.4-0.1-5-0.3"/>
	<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="-2413" y1="-2367.9319" x2="-2408.4441" y2="-2367.9319">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st25" d="M-2409-2367.8c-1.3-0.1-2.6-0.2-4-0.2"/>
	<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="-3219" y1="-5631.5518" x2="-1620" y2="-5631.5518">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st26" x1="-3219" y1="-5631.6" x2="-1620" y2="-5631.6"/>
	
		<text transform="matrix(1 0 0 1 -2698.1777 -4733.3311)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g>
		
			<text transform="matrix(1 0 0 1 -2990.1777 -4391.3311)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -2685.1777 -3986.3308)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.7;" width="309" height="304" xlink:href="7C982DBF67AE2D8C.png"  transform="matrix(1 0 0 1 -3055.6223 -4123.6738)">
		</image>
		<g>
			<ellipse class="st8" cx="-2901.6" cy="-3971.7" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -2699.5654 -4685.6016)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -2991.5654 -4343.6016)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed accumsan vehicula diam vel auctor. Suspendisse</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;"> id interdum lectus. Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">vestibulum vitae eget lacus. </tspan></text>
	<g>
		<defs>
			<text id="XMLID_1_" transform="matrix(1 0 0 1 -2689.5654 -3935.6013)"><tspan x="0" y="0" style="font-family:'Poppins-SemiBold'; font-size:25px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
		</defs>
		<clipPath id="XMLID_6_">
			<use xlink:href="#XMLID_1_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.4;" width="247" height="242" xlink:href="1FE9CA9FC2C9381.png"  transform="matrix(1 0 0 1 -2266.0918 -4275.0894)">
			</image>
			<g>
				<ellipse class="st8" cx="-2149.5" cy="-4156.7" rx="113.9" ry="111.5"/>
			</g>
		</g>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="1FE9CA9FC2C9387.png"  transform="matrix(1 0 0 1 -2157.9446 -4441.7388)">
			</image>
			<g>
				<ellipse class="st8" cx="-2003.5" cy="-4289.7" rx="113.9" ry="111.5"/>
			</g>
		</g>
	</g>
	<linearGradient id="SVGID_18_" gradientUnits="userSpaceOnUse" x1="-2102" y1="-4333.5518" x2="-1816" y2="-4333.5518">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st28" cx="-1959" cy="-4333.6" r="143"/>
	<circle class="st8" cx="-1959" cy="-4333.6" r="134"/>
	<rect x="-4794" y="-3399.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -4788 -3384.8518)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -3828.0918 -3389.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-3240.5" y1="-3366.1" x2="-4840" y2="-3366.1"/>
	<linearGradient id="SVGID_19_" gradientUnits="userSpaceOnUse" x1="-3464" y1="-3383.0515" x2="-3429.5" y2="-3383.0515">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st29" x1="-3464" y1="-3383.1" x2="-3429.5" y2="-3383.1"/>
	<rect x="-4170.8" y="-2978.1" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_20_" gradientUnits="userSpaceOnUse" x1="-4308.5444" y1="-3182.7014" x2="-4281.4526" y2="-3182.7014">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st30" points="-4303.4,-3164 -4308.5,-3170.1 -4293.8,-3182.7 -4308.5,-3195.3 -4303.3,-3201.4 -4281.5,-3182.6 		
			"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_21_" gradientUnits="userSpaceOnUse" x1="-3818.0264" y1="-3154.5583" x2="-3787.5" y2="-3154.5583">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-3818" y="-3158.6" class="st31" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="1FE9CA9FC2C9380.png"  transform="matrix(1 0 0 1 -4261 -3228.0515)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-4209.1-3151.7h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-3151.7z"/>
				<path class="st18" d="M-4193.5-3220.1h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-3220.1z M-4181-3209.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-4181z"/>
				<path class="st18" d="M-4134.2-3220.1h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-3220.1
					z"/>
				<path class="st18" d="M-3972.2-3200.8c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-3972.2-3200.8z"/>
				<path class="st18" d="M-3902.7-3162v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-3902.7z"/>
				<path class="st18" d="M-3882.9-3220.1h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-3220.1z"/>
			</g>
		</g>
	</g>
	<rect x="-4415.6" y="-3087.6" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -4268.5601 -3072.8225)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-4165" y="-2972.2" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -4114.0112 -2939.6399)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="1FE9CA9FC2C9383.png"  transform="matrix(1 0 0 1 -4654.6226 -2628.6741)">
		</image>
		<g>
			<ellipse class="st8" cx="-4500.2" cy="-2476.6" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_22_" gradientUnits="userSpaceOnUse" x1="-4839" y1="-3419.5515" x2="-3240" y2="-3419.5515">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st32" x1="-4839" y1="-3419.6" x2="-3240" y2="-3419.6"/>
	
		<text transform="matrix(1 0 0 1 -4307.1777 -2523.3308)" style="opacity:0.8;fill:#FFFFFF; font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -4599.1777 -2168.3308)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -4294.1777 -1763.3309)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<text transform="matrix(1 0 0 1 -4308.5654 -2475.6013)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -4600.5654 -2120.6013)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">interdum lectus.  Phasellus sed tortor sed dui rutrum vestibulum vitae </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">eget lacus. </tspan></text>
	<text id="XMLID_2_" transform="matrix(1 0 0 1 -4298.5654 -1712.6014)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	<circle class="st18" cx="-2885" cy="-2355.1" r="143"/>
	<g class="st33">
		<path class="st8" d="M-3508.8-2056.1H-3630v-112.5h121.2V-2056.1z M-3517.5-2133.9h-103.8v69.2h103.8V-2133.9z M-3517.5-2142.6
			v-17.3h-103.8v17.3H-3517.5z"/>
		<circle class="st34" cx="-3613.4" cy="-2151.2" r="3.6"/>
		<circle class="st34" cx="-3601.9" cy="-2151.2" r="3.6"/>
		<circle class="st34" cx="-3590.3" cy="-2151.2" r="3.6"/>
		<path class="st8" d="M-3574.3-2099.5l-20.8,21.9l-6.3-6l15.2-16l-15.2-16.3l6.3-5.9C-3595.1-2121.7-3574.3-2099.5-3574.3-2099.5z"
			/>
		<path class="st8" d="M-3569.4-2086.3h30.3v8.7h-30.3V-2086.3z"/>
	</g>
	
		<text transform="matrix(1 0 0 1 -4296.1777 -2681.3308)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use NPM CLI?</text>
	<rect x="-3172" y="-3400.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -3166 -3385.8518)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -2206.0918 -3386.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_23_" gradientUnits="userSpaceOnUse" x1="-3220" y1="-3419.5515" x2="-1620" y2="-3419.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st36" x1="-3220" y1="-3419.6" x2="-1620" y2="-3419.6"/>
	<linearGradient id="SVGID_24_" gradientUnits="userSpaceOnUse" x1="-1842" y1="-3380.0515" x2="-1807.5" y2="-3380.0515">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st37" x1="-1842" y1="-3380.1" x2="-1807.5" y2="-3380.1"/>
	<line class="st11" x1="-1618.5" y1="-3364.1" x2="-3218" y2="-3364.1"/>
	<circle class="st8" cx="-2885" cy="-2355.1" r="125"/>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="1FE9CA9FC2C939F.png"  transform="matrix(1 0 0 1 -3731.8604 -2263.6924)">
		</image>
		<g>
			<ellipse class="st8" cx="-3577.4" cy="-2111.7" rx="113.9" ry="111.5"/>
		</g>
	</g>
	
		<text transform="matrix(1 0 0 1 -2573.1777 -2432.3308)" class="st18" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use this?</text>
	<line class="st38" x1="-2313.5" y1="-2300.6" x2="-2315.6" y2="-2300.6"/>
	<line class="st38" x1="-2908.3" y1="-2300.6" x2="-2910.5" y2="-2300.6"/>
	<line class="st39" x1="-1886" y1="-2020.1" x2="-1888.4" y2="-2020.1"/>
	<line class="st40" x1="-2851.6" y1="-3276.4" x2="-2854.4" y2="-3276.4"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="1FE9CA9FC2C9382.png"  transform="matrix(1 0 0 1 -2904.3645 -3334.416)">
		</image>
		<g>
			<path class="st18" d="M-2011.8-3293.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-2013.1-3296.7-2011.8-3295.3-2011.8-3293.7z"/>
			<path class="st41" d="M-2011.8-3293.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-2013.1-3296.7-2011.8-3295.3-2011.8-3293.7z"/>
		</g>
	</g>
	<line class="st40" x1="-2002" y1="-3280.4" x2="-2004.5" y2="-3280.4"/>
	<line class="st40" x1="-2798.1" y1="-3201.3" x2="-2800.8" y2="-3201.3"/>
	<line class="st40" x1="-2829.8" y1="-3229" x2="-2832.5" y2="-3229"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="1FE9CA9FC2C9385.png"  transform="matrix(1 0 0 1 -2872.3645 -3279.416)">
		</image>
		<g>
			<path class="st18" d="M-1979.1-3239.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-1980.4-3242.4-1979.1-3241-1979.1-3239.4z"/>
			<path class="st41" d="M-1979.1-3239.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-1980.4-3242.4-1979.1-3241-1979.1-3239.4z"/>
		</g>
	</g>
	<g>
		<path class="st8" d="M-1979.1-3239.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-1980.4-3242.1-1979.1-3240.7-1979.1-3239.1z"/>
		<path class="st41" d="M-1979.1-3239.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-1980.4-3242.1-1979.1-3240.7-1979.1-3239.1z"/>
	</g>
	<line class="st40" x1="-1969.1" y1="-3221.9" x2="-1971.7" y2="-3221.9"/>
	<line class="st40" x1="-2633.2" y1="-3142.9" x2="-2635.9" y2="-3142.9"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="1FE9CA9FC2C9386.png"  transform="matrix(1 0 0 1 -2838.3645 -3220.416)">
		</image>
		<g>
			<path class="st18" d="M-1945.4-3180.3v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-1946.8-3183.3-1945.4-3181.9-1945.4-3180.3z"/>
			<path class="st41" d="M-1945.4-3180.3v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-1946.8-3183.3-1945.4-3181.9-1945.4-3180.3z"/>
		</g>
	</g>
	<line class="st40" x1="-1925.3" y1="-3164.8" x2="-1927.9" y2="-3164.8"/>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_25_" gradientUnits="userSpaceOnUse" x1="-2155.0264" y1="-3028.5583" x2="-2124.5" y2="-3028.5583">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-2155" y="-3032.6" class="st42" width="30.5" height="8"/>
		</g>
		<g>
			
				<linearGradient id="SVGID_26_" gradientUnits="userSpaceOnUse" x1="-2645.5444" y1="-3056.7014" x2="-2618.4526" y2="-3056.7014">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st43" points="-2640.4,-3038 -2645.5,-3044.1 -2630.8,-3056.7 -2645.5,-3069.3 -2640.3,-3075.4 -2618.5,-3056.6 
							"/>
		</g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="1FE9CA9FC2C9384.png"  transform="matrix(1 0 0 1 -2595 -3099.0515)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-2546.1-3025.7h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-3025.7z"/>
					<path class="st8" d="M-2530.5-3094.1h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						h-11.2v27.7h-12.5V-3094.1z M-2518-3083.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-2518z"/>
					<path class="st8" d="M-2471.2-3094.1h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-3094.1
						z"/>
					<path class="st8" d="M-2309.2-3074.8c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-2309.2-3074.8z"/>
					<path class="st8" d="M-2239.7-3036v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-2239.7z"/>
					<path class="st8" d="M-2219.9-3094.1h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-3094.1z"/>
				</g>
			</g>
		</g>
		<rect x="-2623.7" y="-2959.6" class="st47" width="489.6" height="304.6"/>
		<text transform="matrix(1 0 0 1 -2623.7363 -2944.8225)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="54" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="167.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="181.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="279.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="293.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="396.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="409.9" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="440.6" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="454.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the </tspan><tspan x="0" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node</tspan><tspan x="57.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="67.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript</tspan><tspan x="186.4" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="196.3" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Platform.</tspan><tspan x="298" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="307.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Install</tspan><tspan x="376.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="386.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">stuff</tspan><tspan x="437.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="447.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">and </tspan><tspan x="181.2" y="62" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">get coding!</tspan></text>
		<g>
			<rect x="-2492.7" y="-2827.1" class="st14" width="230" height="59.2"/>
			<rect x="-2486.8" y="-2821.2" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -2435.8589 -2788.6399)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g>
		<path class="st8" d="M-2012.1-3294.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-2013.4-3297.1-2012.1-3295.7-2012.1-3294.1z"/>
		<path class="st41" d="M-2012.1-3294.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-2013.4-3297.1-2012.1-3295.7-2012.1-3294.1z"/>
	</g>
	<g>
		<path class="st8" d="M-1945.1-3180.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-1946.4-3183.1-1945.1-3181.7-1945.1-3180.1z"/>
		<path class="st41" d="M-1945.1-3180.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-1946.4-3183.1-1945.1-3181.7-1945.1-3180.1z"/>
	</g>
	<linearGradient id="SVGID_27_" gradientUnits="userSpaceOnUse" x1="-2171" y1="-2120.0515" x2="-1885" y2="-2120.0515">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st44" cx="-2028" cy="-2120.1" r="143"/>
	<circle class="st8" cx="-2028" cy="-2120.1" r="125"/>
	<linearGradient id="SVGID_28_" gradientUnits="userSpaceOnUse" x1="-2886" y1="-1783.0516" x2="-2600" y2="-1783.0516">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st45" cx="-2743" cy="-1783.1" r="143"/>
	<circle class="st8" cx="-2743" cy="-1783.1" r="125"/>
	<g>
		<g>
			<path class="st46" d="M-3136.9-3386h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-3386z M-3106.2-3399v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H-3106.2L-3106.2-3399z M-3115.2-3395.7h3.2v6.5h-3.2V-3395.7z M-3121.6-3382.8h6.4v-3.2h6.4v-13h-12.8V-3382.8z"/>
			<rect x="-3136.9" y="-3399" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-3071.9,-3395.8 -3071.9,-3389.5 -3065.8,-3389.5 -3065.8,-3386.4 -3072,-3386.4 -3078.3,-3386.4 
			-3078.2,-3399 -3065.8,-3399 -3065.8,-3395.9 		"/>
		<rect x="-3063.2" y="-3399" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-3057.3" y="-3392.4" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -6443.4009 -331.8764)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-3036.1" y="-3391" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -6422.0322 -351.7385)" class="st46" width="2" height="8.3"/>
		<rect x="-3049.1" y="-3399.1" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_29_" gradientUnits="userSpaceOnUse" x1="-1599" y1="-2985.5515" x2="-1" y2="-2985.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st48" points="-1,-2606.1 -1,-3365.1 -1599,-3365.1 -1599,-2607.6 	"/>
	<linearGradient id="SVGID_30_" gradientUnits="userSpaceOnUse" x1="-1202.8535" y1="-3183.3015" x2="-1201.6465" y2="-3183.3015">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st49" x1="-1202" y1="-3183.1" x2="-1202.5" y2="-3183.6"/>
	<rect x="-1552" y="-3401.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -1546 -3386.8518)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -393.0918 -3385.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<line class="st11" x1="1.5" y1="-3365.1" x2="-1598" y2="-3365.1"/>
	<line class="st50" x1="-872.2" y1="-2383.7" x2="-873.9" y2="-2383.7"/>
	<line class="st38" x1="-1288.3" y1="-2360.6" x2="-1290.5" y2="-2360.6"/>
	<line class="st39" x1="-266" y1="-2080.1" x2="-268.4" y2="-2080.1"/>
	<line class="st40" x1="-1209.6" y1="-3270.4" x2="-1212.4" y2="-3270.4"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="1FE9CA9FC2C938C.png"  transform="matrix(1 0 0 1 -1350.3645 -3348.416)">
		</image>
		<g>
			<path class="st18" d="M-457.8-3307.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-459.1-3310.7-457.8-3309.3-457.8-3307.7z"/>
			<path class="st41" d="M-457.8-3307.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-459.1-3310.7-457.8-3309.3-457.8-3307.7z"/>
		</g>
	</g>
	<line class="st40" x1="-360" y1="-3274.4" x2="-362.5" y2="-3274.4"/>
	<line class="st40" x1="-1156.1" y1="-3195.3" x2="-1158.8" y2="-3195.3"/>
	<line class="st40" x1="-1187.8" y1="-3223" x2="-1190.5" y2="-3223"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="1FE9CA9FC2C93A4.png"  transform="matrix(1 0 0 1 -1296.3645 -3273.416)">
		</image>
		<g>
			<path class="st18" d="M-403.1-3233.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-404.4-3236.4-403.1-3235-403.1-3233.4z"/>
			<path class="st41" d="M-403.1-3233.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-404.4-3236.4-403.1-3235-403.1-3233.4z"/>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-403.1-3233.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-404.4-3236.1-403.1-3234.7-403.1-3233.1z"/>
		<path class="st41" d="M-403.1-3233.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-404.4-3236.1-403.1-3234.7-403.1-3233.1z"/>
	</g>
	<line class="st40" x1="-327.1" y1="-3215.9" x2="-329.7" y2="-3215.9"/>
	<line class="st40" x1="-991.2" y1="-3136.9" x2="-993.9" y2="-3136.9"/>
	<g class="st52">
		<line class="st53" x1="-1599.5" y1="-3364.8" x2="1.5" y2="-3364.8"/>
		<line class="st53" x1="-1599.4" y1="-3174.7" x2="1.6" y2="-3174.7"/>
		<line class="st53" x1="-1599.2" y1="-2984.7" x2="1.8" y2="-2984.7"/>
		<line class="st53" x1="-1599.1" y1="-2794.6" x2="1.9" y2="-2794.6"/>
		<line class="st53" x1="-1599" y1="-2604.6" x2="2" y2="-2604.6"/>
		<line class="st53" x1="-1598.8" y1="-2414.5" x2="2.2" y2="-2414.5"/>
		<line class="st53" x1="-1598.7" y1="-2224.5" x2="2.3" y2="-2224.5"/>
		<line class="st53" x1="-1598.5" y1="-2034.4" x2="2.5" y2="-2034.4"/>
		<line class="st53" x1="-1598.4" y1="-1844.4" x2="2.6" y2="-1844.4"/>
		<line class="st53" x1="-1598.3" y1="-1654.3" x2="2.7" y2="-1654.3"/>
		<line class="st53" x1="-1598.1" y1="-1464.3" x2="2.9" y2="-1464.3"/>
		<line class="st53" x1="-1598" y1="-1274.3" x2="3" y2="-1274.3"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="1FE9CA9FC2C93A7.png"  transform="matrix(1 0 0 1 -1227.3645 -3181.416)">
		</image>
		<g>
			<path class="st18" d="M-334.4-3141.3v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-335.8-3144.3-334.4-3142.9-334.4-3141.3z"/>
			<path class="st41" d="M-334.4-3141.3v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-335.8-3144.3-334.4-3142.9-334.4-3141.3z"/>
		</g>
	</g>
	<line class="st40" x1="-283.3" y1="-3158.8" x2="-285.9" y2="-3158.8"/>
	<g class="st54">
		<line class="st55" x1="-1598" y1="-3359.1" x2="-1598" y2="-1224.1"/>
		<line class="st55" x1="-1398.4" y1="-3359.6" x2="-1398.4" y2="-1224.6"/>
		<line class="st55" x1="-1198.9" y1="-3360.1" x2="-1198.9" y2="-1225.1"/>
		<line class="st55" x1="-999.3" y1="-3360.6" x2="-999.3" y2="-1225.6"/>
		<line class="st55" x1="-799.8" y1="-3361.1" x2="-799.8" y2="-1226.1"/>
		<line class="st55" x1="-600.2" y1="-3361.6" x2="-600.2" y2="-1226.6"/>
		<line class="st55" x1="-400.6" y1="-3362.1" x2="-400.6" y2="-1227.1"/>
		<line class="st55" x1="-201.1" y1="-3362.6" x2="-201.1" y2="-1227.6"/>
		<line class="st55" x1="-1.5" y1="-3363.1" x2="-1.5" y2="-1228.1"/>
	</g>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_31_" gradientUnits="userSpaceOnUse" x1="-576.0264" y1="-2978.5583" x2="-545.5" y2="-2978.5583">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-576" y="-2982.6" class="st56" width="30.5" height="8"/>
		</g>
		<g>
			
				<linearGradient id="SVGID_32_" gradientUnits="userSpaceOnUse" x1="-1066.5444" y1="-3006.7014" x2="-1039.4526" y2="-3006.7014">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st57" points="-1061.4,-2988 -1066.5,-2994.1 -1051.8,-3006.7 -1066.5,-3019.3 -1061.3,-3025.4 -1039.5,-3006.6 
							"/>
		</g>
		<g class="st51">
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="1FE9CA9FC2C93A5.png"  transform="matrix(1 0 0 1 -1016 -3049.0515)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-967.1-2975.7h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-2975.7z"/>
					<path class="st8" d="M-951.5-3044.1h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						H-939v27.7h-12.5V-3044.1z M-939-3033.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-939z"/>
					<path class="st8" d="M-892.2-3044.1h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-3044.1z
						"/>
					<path class="st8" d="M-730.2-3024.8c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-730.2-3024.8z"/>
					<path class="st8" d="M-660.7-2986v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-660.7z"/>
					<path class="st8" d="M-640.9-3044.1h38.2v10.2H-616v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-3044.1z"/>
				</g>
			</g>
		</g>
		<rect x="-1067.7" y="-2909.6" class="st47" width="551.7" height="304.6"/>
		<text transform="matrix(1 0 0 1 -1067.7363 -2894.8225)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
		<g>
			<rect x="-1065.7" y="-2810.1" class="st14" width="230" height="59.2"/>
			<rect x="-1059.8" y="-2804.2" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -1008.8589 -2771.6399)" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-458.1-3308.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-459.4-3311.1-458.1-3309.7-458.1-3308.1z"/>
		<path class="st41" d="M-458.1-3308.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-459.4-3311.1-458.1-3309.7-458.1-3308.1z"/>
	</g>
	<g class="st51">
		<path class="st8" d="M-334.1-3141.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-335.4-3144.1-334.1-3142.7-334.1-3141.1z"/>
		<path class="st41" d="M-334.1-3141.1v21.7c0,1.7-1.4,3-3,3h-849.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-335.4-3144.1-334.1-3142.7-334.1-3141.1z"/>
	</g>
	<g>
		<g>
			<path class="st46" d="M-1516.9-3387h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-3387z M-1486.2-3400v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H-1486.2L-1486.2-3400z M-1495.2-3396.7h3.2v6.5h-3.2V-3396.7z M-1501.6-3383.8h6.4v-3.2h6.4v-13h-12.8V-3383.8z"/>
			<rect x="-1516.9" y="-3400" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-1451.9,-3396.8 -1451.9,-3390.5 -1445.8,-3390.5 -1445.8,-3387.4 -1452,-3387.4 -1458.3,-3387.4 
			-1458.2,-3400 -1445.8,-3400 -1445.8,-3396.9 		"/>
		<rect x="-1443.2" y="-3400" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-1437.3" y="-3393.4" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -4824.4009 -1952.8765)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-1416.1" y="-3392" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -4803.0322 -1972.7385)" class="st46" width="2" height="8.3"/>
		<rect x="-1429.1" y="-3400.1" class="st46" width="6.4" height="12.9"/>
	</g>
	
		<linearGradient id="SVGID_33_" gradientUnits="userSpaceOnUse" x1="-1378.5829" y1="-2008.6067" x2="-998.5828" y2="-2008.6067" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 1552.7886 -2231.2971)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st58" points="-425.6,-1372.6 -452.9,-993.6 -646.4,-1007.5 -619.1,-1386.5 	"/>
	<linearGradient id="SVGID_34_" gradientUnits="userSpaceOnUse" x1="-1600" y1="-3419.5515" x2="0" y2="-3419.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st59" x1="-1600" y1="-3419.6" x2="0" y2="-3419.6"/>
	
		<linearGradient id="SVGID_35_" gradientUnits="userSpaceOnUse" x1="-475.3538" y1="-1962.2018" x2="-271.3535" y2="-1962.2018" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 290.6579 -98.123)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st60" points="-229.5,-1716.6 -431.2,-1686 -516.5,-2247.5 -314.8,-2278.1 	"/>
	<line class="st50" x1="-142.2" y1="-2319.7" x2="-143.9" y2="-2319.7"/>
	<g>
		
			<image style="overflow:visible;" width="827" height="400" xlink:href="1FE9CA9FC2C93AB.png"  transform="matrix(1 0 0 1 -1217 -1603.0516)">
		</image>
		<g>
			<path class="st61" d="M-399.5-1209.1l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-396.9-1210.2-398.1-1209.1-399.5-1209.1z"/>
			<path class="st62" d="M-399.5-1209.1l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-396.9-1210.2-398.1-1209.1-399.5-1209.1z"/>
		</g>
	</g>
	<rect x="-1160" y="-1139.1" class="st8" width="21" height="38"/>
	
		<linearGradient id="SVGID_36_" gradientUnits="userSpaceOnUse" x1="-1363.8662" y1="-2368.8884" x2="-983.8663" y2="-2368.8884" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 1308.3594 -3181.1709)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st63" points="-1061.8,-2465.6 -1105.5,-2088.1 -1298.2,-2110.5 -1254.5,-2488 	"/>
	<g>
		
			<image style="overflow:visible;" width="828" height="375" xlink:href="1FE9CA9FC2C93AC.png"  transform="matrix(1 0 0 1 -1217 -2550.0515)">
		</image>
		<g>
			<path class="st61" d="M-397.6-2181.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-395-2182.7-396.2-2181.6-397.6-2181.6z"/>
			<path class="st62" d="M-397.6-2181.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-395-2182.7-396.2-2181.6-397.6-2181.6z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -914.0342 -2335.4666)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g id="XMLID_3_">
		<text transform="matrix(0.9755 0 0 1 -990.544 -1356.6014)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	</g>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -4609.1777 -2169.3308)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="1FE9CA9FC2C93AF.png"  transform="matrix(1 0 0 1 -915 -2418.0515)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -911.9512 -2386.3308)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="1FE9CA9FC2C93AD.png"  transform="matrix(1 0 0 1 -984 -1439.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -980.5255 -1407.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;" width="827" height="401" xlink:href="1FE9CA9FC2C93AA.png"  transform="matrix(1 0 0 1 -1217 -2070.0515)">
		</image>
		<g>
			<path class="st61" d="M-399.1-1675.6l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6l0-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-396.6-1676.7-397.7-1675.6-399.1-1675.6z"/>
			<path class="st62" d="M-399.1-1675.6l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6l0-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-396.6-1676.7-397.7-1675.6-399.1-1675.6z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -1004.5447 -1847.6014)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">interdum lectus.  Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">vestibulum vitae eget lacus. </tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="237" height="59" xlink:href="1FE9CA9FC2C93A6.png"  transform="matrix(1 0 0 1 -1002 -1932.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -998.5334 -1900.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<polygon class="st64" points="3.6,-935.7 -1603.1,-985.9 -1603.1,-543.6 0.5,-543.6 	"/>
	
		<linearGradient id="SVGID_37_" gradientUnits="userSpaceOnUse" x1="-2335.8354" y1="-2249.9255" x2="-1955.8352" y2="-2249.9255" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 980.2065 948.5847)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st65" points="-1073.7,-1111 -1453.3,-1093.3 -1462.3,-1287.1 -1082.7,-1304.8 	"/>
	<line class="st41" x1="-422.4" y1="-3227.3" x2="-414" y2="-3218.3"/>
	<line class="st41" x1="-422.7" y1="-3218.9" x2="-413.7" y2="-3227.2"/>
	<line class="st41" x1="-477.4" y1="-3303.3" x2="-469" y2="-3294.3"/>
	<line class="st41" x1="-477.7" y1="-3294.9" x2="-468.7" y2="-3303.2"/>
	<line class="st41" x1="-351.4" y1="-3134.3" x2="-343" y2="-3125.3"/>
	<line class="st41" x1="-351.7" y1="-3125.9" x2="-342.7" y2="-3134.2"/>
	<path class="st19" d="M-984.6-2367.6c5.4-5.6,8.6-13.1,8.6-21.5c0-17.1-13.9-31-31-31s-31,13.9-31,31c0,9.8,4.5,18.5,11.6,24.2
		c-2.2,5.6-8,23.3-5.2,51.8h55.6C-976-2313.1-970.7-2344.9-984.6-2367.6z"/>
	<circle class="st18" cx="-1004.5" cy="-2394.4" r="3.5"/>
	<circle class="st18" cx="-1021.5" cy="-2391.6" r="3.5"/>
	<circle class="st62" cx="-1014.5" cy="-2389.6" r="30.5"/>
	<path class="st66" d="M-1004-2381.3c-3.2,3.7-8.8,4.1-12.4,0.9"/>
	<path class="st62" d="M-1028.6-2365.8c0,0-9.4,18.8-6,53.8h55.6c0,0,5.6-33.4-9.7-56.2"/>
	<line class="st62" x1="-1100" y1="-2434.1" x2="-1100" y2="-2297.1"/>
	<line class="st62" x1="-1100" y1="-1931.1" x2="-1100" y2="-1794.1"/>
	<line class="st62" x1="-1100" y1="-1452.1" x2="-1100" y2="-1315.1"/>
	<g id="POueHo_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="POueHo_2_" xlink:href="1FE9CA9FC2C93A4.jpg"  transform="matrix(1 0 0 1 289 -4704.0518)">
		</image>
	</g>
	<g id="FkRr9g_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="FkRr9g_2_" xlink:href="1FE9CA9FC2C93CB.jpg"  transform="matrix(1 0 0 1 -1131 -4653.0518)">
		</image>
	</g>
	<rect x="44" y="-3398.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 50 -3383.8518)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 1224.9082 -3382.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M79.1-3384h6.4v-9.7h3.2v9.7h3.2v-13H79.1V-3384z M109.8-3397v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7h3.2
				v-13H109.8L109.8-3397z M100.8-3393.7h3.2v6.5h-3.2V-3393.7z M94.4-3380.8h6.4v-3.2h6.4v-13H94.4V-3380.8z"/>
			<rect x="79.1" y="-3397" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="144.1,-3393.8 144.1,-3387.5 150.2,-3387.5 150.2,-3384.4 144,-3384.4 137.7,-3384.4 137.8,-3397 
			150.2,-3397 150.2,-3393.9 		"/>
		<rect x="152.8" y="-3397" class="st46" width="6.4" height="12.9"/>
		
			<rect x="158.7" y="-3390.4" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3225.4011 -3545.8765)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="179.9" y="-3389" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3204.0322 -3565.7385)" class="st46" width="2" height="8.3"/>
		<rect x="166.9" y="-3397.1" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_38_" gradientUnits="userSpaceOnUse" x1="18" y1="-3418.5515" x2="1618" y2="-3418.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st67" x1="18" y1="-3418.6" x2="1618" y2="-3418.6"/>
	<linearGradient id="SVGID_39_" gradientUnits="userSpaceOnUse" x1="21" y1="-1667.3009" x2="1619" y2="-1667.3009">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st68" points="1619,-732.1 1619,-2602.6 21,-2602.6 21,-736 	"/>
	<line class="st50" x1="736.8" y1="-2328.7" x2="735.1" y2="-2328.7"/>
	<line class="st38" x1="320.7" y1="-2305.6" x2="318.5" y2="-2305.6"/>
	
		<linearGradient id="SVGID_40_" gradientUnits="userSpaceOnUse" x1="-1317.8749" y1="-399.812" x2="-937.8748" y2="-399.812" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 1552.7886 -2231.2971)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st69" points="1183.4,-1317.6 1156.1,-938.6 962.6,-952.5 989.9,-1331.5 	"/>
	
		<linearGradient id="SVGID_41_" gradientUnits="userSpaceOnUse" x1="1150.6624" y1="-1659.7031" x2="1354.6626" y2="-1659.7031" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 290.6579 -98.123)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st70" points="1423.5,-1661.6 1221.8,-1631 1136.5,-2192.5 1338.2,-2223.1 	"/>
	
		<linearGradient id="SVGID_42_" gradientUnits="userSpaceOnUse" x1="-1233.1201" y1="-764.2665" x2="-853.1201" y2="-764.2665" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 1308.3594 -3181.1709)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st71" points="547.2,-2410.6 503.5,-2033.1 310.8,-2055.5 354.5,-2433 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M1221.4-2116.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1224-2117.7,1222.8-2116.6,1221.4-2116.6z"/>
		</g>
		<g>
			<path class="st72" d="M1211.4-2126.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1214-2127.7,1212.8-2126.6,1211.4-2126.6z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 728.9658 -2284.4666)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="1FE9CA9FC2C93C9.png"  transform="matrix(1 0 0 1 728 -2363.0515)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 731.1631 -2331.3308)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_43_" gradientUnits="userSpaceOnUse" x1="-731.1376" y1="-2120.1138" x2="-351.1375" y2="-2120.1138" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 980.2065 948.5847)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st73" points="535.3,-1056 155.7,-1038.3 146.7,-1232.1 526.3,-1249.8 	"/>
	<path class="st74" d="M655.3-2257.6H535.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C657.3-2258.4,656.4-2257.6,655.3-2257.6z"/>
	<rect x="533.7" y="-2354.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C93F7.png"  transform="matrix(1 0 0 1 537.8558 -2333.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_44_" gradientUnits="userSpaceOnUse" x1="579.1498" y1="-2316.9705" x2="580.8394" y2="-2331.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="543.3" y="-2327.4" class="st76" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C93F9.png"  transform="matrix(1 0 0 1 538.1003 -2315.9514)">
		</image>
		<g>
			<linearGradient id="SVGID_45_" gradientUnits="userSpaceOnUse" x1="566.9532" y1="-2302.2866" x2="568.2815" y2="-2313.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="543.2" y="-2311" class="st77" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C93FB.png"  transform="matrix(1 0 0 1 537.8434 -2301.2083)">
		</image>
		<g>
			<linearGradient id="SVGID_46_" gradientUnits="userSpaceOnUse" x1="571.6628" y1="-2285.8679" x2="573.1657" y2="-2298.8254">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="543.2" y="-2295.5" class="st78" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C93FE.png"  transform="matrix(1 0 0 1 536.9122 -2284.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_47_" gradientUnits="userSpaceOnUse" x1="568.1271" y1="-2269.1079" x2="569.5059" y2="-2280.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="542.8" y="-2278.1" class="st79" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C93FF.png"  transform="matrix(1 0 0 1 594.1003 -2315.9514)">
		</image>
		<g>
			<linearGradient id="SVGID_48_" gradientUnits="userSpaceOnUse" x1="607.0668" y1="-2304.1665" x2="607.9589" y2="-2311.8579">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="599.5" y="-2311" class="st80" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="580" y1="-2347.1" x2="620" y2="-2347.1"/>
	<circle class="st18" cx="541.5" cy="-2347.6" r="1.5"/>
	<circle class="st18" cx="547.5" cy="-2347.6" r="1.5"/>
	<line class="st50" x1="738.8" y1="-1882.7" x2="737.1" y2="-1882.7"/>
	<line class="st38" x1="322.7" y1="-1859.6" x2="320.5" y2="-1859.6"/>
	<g class="st16">
		<g>
			<path class="st14" d="M1223.4-1670.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1226-1671.7,1224.8-1670.6,1223.4-1670.6z"/>
		</g>
		<g>
			<path class="st72" d="M1213.4-1680.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1216-1681.7,1214.8-1680.6,1213.4-1680.6z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="1FE9CA9FC2C93FA.png"  transform="matrix(1 0 0 1 723 -1936.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 726.3887 -1904.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M657.3-1811.6H537.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C659.3-1812.4,658.4-1811.6,657.3-1811.6z"/>
	<rect x="535.7" y="-1908.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C93F5.png"  transform="matrix(1 0 0 1 539.8558 -1887.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_49_" gradientUnits="userSpaceOnUse" x1="581.1498" y1="-1870.9703" x2="582.8394" y2="-1885.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="545.3" y="-1881.4" class="st82" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C93F6.png"  transform="matrix(1 0 0 1 540.1003 -1869.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_50_" gradientUnits="userSpaceOnUse" x1="568.9532" y1="-1856.2865" x2="570.2815" y2="-1867.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="545.2" y="-1865" class="st83" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C93D2.png"  transform="matrix(1 0 0 1 539.8434 -1855.2081)">
		</image>
		<g>
			<linearGradient id="SVGID_51_" gradientUnits="userSpaceOnUse" x1="573.6628" y1="-1839.8679" x2="575.1657" y2="-1852.8253">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="545.2" y="-1849.5" class="st84" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C93D1.png"  transform="matrix(1 0 0 1 538.9122 -1838.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_52_" gradientUnits="userSpaceOnUse" x1="570.1271" y1="-1823.1078" x2="571.5059" y2="-1834.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="544.8" y="-1832.1" class="st85" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C93D6.png"  transform="matrix(1 0 0 1 596.1003 -1869.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_53_" gradientUnits="userSpaceOnUse" x1="609.0668" y1="-1858.1666" x2="609.9589" y2="-1865.858">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="601.5" y="-1865" class="st86" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="582" y1="-1901.1" x2="622" y2="-1901.1"/>
	<circle class="st18" cx="543.5" cy="-1901.6" r="1.5"/>
	<circle class="st18" cx="549.5" cy="-1901.6" r="1.5"/>
	<rect x="723.1" y="-1875.1" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 723.1143 -1861.7743)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="741.8" y1="-1419.7" x2="740.1" y2="-1419.7"/>
	<line class="st38" x1="325.7" y1="-1396.6" x2="323.5" y2="-1396.6"/>
	<g class="st16">
		<g>
			<path class="st14" d="M1226.4-1207.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1229-1208.7,1227.8-1207.6,1226.4-1207.6z"/>
		</g>
		<g>
			<path class="st72" d="M1216.4-1217.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C1219-1218.7,1217.8-1217.6,1216.4-1217.6z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="1FE9CA9FC2C93D0.png"  transform="matrix(1 0 0 1 726 -1473.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 729.3887 -1441.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M660.3-1348.6H540.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C662.3-1349.4,661.4-1348.6,660.3-1348.6z"/>
	<rect x="538.7" y="-1445.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C93D3.png"  transform="matrix(1 0 0 1 542.8558 -1424.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_54_" gradientUnits="userSpaceOnUse" x1="584.1498" y1="-1407.9703" x2="585.8394" y2="-1422.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="548.3" y="-1418.4" class="st87" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C93CF.png"  transform="matrix(1 0 0 1 543.1003 -1406.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_55_" gradientUnits="userSpaceOnUse" x1="571.9532" y1="-1393.2865" x2="573.2815" y2="-1404.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="548.2" y="-1402" class="st88" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C942E.png"  transform="matrix(1 0 0 1 542.8434 -1392.2081)">
		</image>
		<g>
			<linearGradient id="SVGID_56_" gradientUnits="userSpaceOnUse" x1="576.6628" y1="-1376.8679" x2="578.1657" y2="-1389.8253">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="548.2" y="-1386.5" class="st89" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C9431.png"  transform="matrix(1 0 0 1 541.9122 -1375.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_57_" gradientUnits="userSpaceOnUse" x1="573.1271" y1="-1360.1078" x2="574.5059" y2="-1371.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="547.8" y="-1369.1" class="st90" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C9432.png"  transform="matrix(1 0 0 1 599.1003 -1406.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_58_" gradientUnits="userSpaceOnUse" x1="612.0668" y1="-1395.1666" x2="612.9589" y2="-1402.858">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="604.5" y="-1402" class="st91" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="585" y1="-1438.1" x2="625" y2="-1438.1"/>
	<circle class="st18" cx="546.5" cy="-1438.6" r="1.5"/>
	<circle class="st18" cx="552.5" cy="-1438.6" r="1.5"/>
	<rect x="726.1" y="-1412.1" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 726.1143 -1398.7743)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<path class="st92" d="M1376.9-3190.4c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19
		C1377.1-3190.5,1377-3190.4,1376.9-3190.4z"/>
	<path class="st92" d="M1383.1-3162.7c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8s0.7,0.1,0.8,0.5l6.2,27.7
		C1383.7-3163.2,1383.4-3162.8,1383.1-3162.7z"/>
	<path class="st92" d="M1383.1-3162.7c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C1383.3-3162.8,1383.2-3162.8,1383.1-3162.7z"/>
	<path class="st93" d="M1389.1-2812c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C1389.3-2811.9,1389.2-2811.9,1389.1-2812z"
		/>
	<path class="st93" d="M1373.4-2790.8c-0.1-0.1-0.2-0.1-0.2-0.2L1363-2815c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C1374-2790.7,1373.6-2790.6,1373.4-2790.8z"/>
	<path class="st93" d="M1414.8-2809.9c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_59_" gradientUnits="userSpaceOnUse" x1="2956.01" y1="-3597.5083" x2="1923.99" y2="-2367.5947">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.7"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="1641" y="-3360.1" class="st94" width="1598" height="755"/>
	<rect x="1667" y="-3398.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 1673 -3383.8518)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 3044.9082 -3382.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M1713.1-3384h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-3384z M1743.8-3397v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H1743.8L1743.8-3397z M1734.8-3393.7h3.2v6.5h-3.2V-3393.7z M1728.4-3380.8h6.4v-3.2h6.4v-13h-12.8V-3380.8z"/>
			<rect x="1713.1" y="-3397" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="1778.1,-3393.8 1778.1,-3387.5 1784.2,-3387.5 1784.2,-3384.4 1778,-3384.4 1771.7,-3384.4 
			1771.8,-3397 1784.2,-3397 1784.2,-3393.9 		"/>
		<rect x="1786.8" y="-3397" class="st46" width="6.4" height="12.9"/>
		
			<rect x="1792.7" y="-3390.4" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1591.401 -5179.8765)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="1813.9" y="-3389" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1570.0321 -5199.7383)" class="st46" width="2" height="8.3"/>
		<rect x="1800.9" y="-3397.1" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_60_" gradientUnits="userSpaceOnUse" x1="1640" y1="-3419.5515" x2="3240" y2="-3419.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st95" x1="1640" y1="-3419.6" x2="3240" y2="-3419.6"/>
	<rect x="18.5" y="-994.6" class="st96" width="1602" height="510"/>
	<linearGradient id="SVGID_61_" gradientUnits="userSpaceOnUse" x1="2160.1465" y1="-3197.3015" x2="2161.3535" y2="-3197.3015">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st97" x1="2161" y1="-3197.1" x2="2160.5" y2="-3197.6"/>
	<line class="st40" x1="2206.9" y1="-3264.3" x2="2204.2" y2="-3264.3"/>
	<line class="st40" x1="2175.2" y1="-3237" x2="2172.5" y2="-3237"/>
	<line class="st40" x1="2371.8" y1="-3205.9" x2="2369.1" y2="-3205.9"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="1FE9CA9FC2C9430.png"  transform="matrix(1 0 0 1 2050 -3301.0515)">
			</image>
			<g>
				<path class="st98" d="M2821.9-3282.4v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C2820.6-3285.4,2821.9-3284,2821.9-3282.4z"/>
				<path class="st81" d="M2821.9-3282.4v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C2820.6-3285.4,2821.9-3284,2821.9-3282.4z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M2821.9-3282.1v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C2820.6-3285.1,2821.9-3283.7,2821.9-3282.1z"/>
			<path class="st62" d="M2821.9-3282.1v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C2820.6-3285.1,2821.9-3283.7,2821.9-3282.1z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="2207.4" y1="-3189.4" x2="2204.6" y2="-3189.4"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="1FE9CA9FC2C942D.png"  transform="matrix(1 0 0 1 2089 -3245.0515)">
			</image>
			<g>
				<path class="st98" d="M2860.2-3226.7v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C2858.9-3229.7,2860.2-3228.3,2860.2-3226.7z"/>
				<path class="st99" d="M2860.2-3226.7v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C2858.9-3229.7,2860.2-3228.3,2860.2-3226.7z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M2859.9-3227.1v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C2858.6-3230.1,2859.9-3228.7,2859.9-3227.1z"/>
			<path class="st62" d="M2859.9-3227.1v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C2858.6-3230.1,2859.9-3228.7,2859.9-3227.1z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="1FE9CA9FC2C9413.png"  transform="matrix(1 0 0 1 2123 -3185.0515)">
			</image>
			<g>
				<path class="st98" d="M2894.6-3168.3v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C2893.7-3170.3,2894.6-3169.4,2894.6-3168.3z"/>
				<path class="st99" d="M2894.6-3168.3v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C2893.7-3170.3,2894.6-3169.4,2894.6-3168.3z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="2609.7" y="-3019.2" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="2238.4,-3023.4 2234.5,-3028.1 2245.7,-3037.6 2234.5,-3047.3 2238.4,-3051.9 2255.2,-3037.6 				
					"/>
			</g>
			<g class="st51">
				
					<image style="overflow:visible;opacity:0.2;" width="327" height="66" xlink:href="1FE9CA9FC2C9415.png"  transform="matrix(1 0 0 1 2272.4106 -3070.6409)">
				</image>
				<g>
					<g>
						<path class="st8" d="M2310.5-3013.9h-10.4l-17.7-39.6v39.6h-8.5v-52.3h11.5l16.6,37.5v-37.5h9.1c0.1,0.1,0.2,0.3,0.2,0.4
							c0,0.2-0.1,0.5-0.3,0.8c-0.2,0.3-0.4,0.9-0.5,1.7V-3013.9z"/>
						<path class="st8" d="M2322.4-3066.3h17.7c3.1,0,5.8,0.4,8,1.3c2.2,0.8,4,2,5.5,3.4c1.4,1.4,2.5,3.1,3.1,5c0.7,1.9,1,3.9,1,6
							c0,2.1-0.3,4.1-1,6c-0.6,1.9-1.7,3.5-3.1,4.9c-1.4,1.4-3.2,2.5-5.4,3.3c-2.2,0.8-4.8,1.2-7.8,1.2h-8.6v21.2h-9.6V-3066.3z
							 M2332-3057.9v14.9h7.9c1.5,0,2.7-0.2,3.7-0.5c1-0.4,1.9-0.9,2.6-1.5c0.7-0.6,1.2-1.4,1.5-2.3c0.3-0.9,0.5-1.8,0.5-2.9
							c0-1.1-0.2-2.1-0.5-3.1c-0.3-0.9-0.8-1.7-1.5-2.4c-0.7-0.7-1.5-1.2-2.5-1.6c-1-0.4-2.2-0.6-3.6-0.6H2332z"/>
						<path class="st8" d="M2367.8-3066.3h9.9l8.8,24.6l8.7-24.7h10v52.4h-8.5v-38l-7.3,19.7h-6.2l-7.1-19.7v38h-8.4V-3066.3z"/>
						<path class="st8" d="M2491.7-3051.5c-0.2-0.1-0.4-0.2-0.5-0.3c-0.1-0.2-0.1-0.4-0.2-0.6c0-0.2-0.1-0.5-0.1-0.8
							c0-0.3-0.1-0.6-0.2-1c-0.8-1.8-1.8-3.2-3.2-4.2c-1.3-1-3.1-1.6-5.1-1.6c-1.7,0-3.2,0.5-4.5,1.4c-1.3,1-2.5,2.3-3.4,4
							c-1,1.7-1.7,3.8-2.2,6.2c-0.5,2.4-0.8,5.1-0.8,8.1c0,2.9,0.3,5.5,0.8,7.9c0.6,2.4,1.3,4.5,2.4,6.3c1,1.8,2.2,3.1,3.7,4.2
							c1.4,1,3,1.5,4.7,1.5c2,0,3.8-0.6,5.3-1.9c1.5-1.3,2.9-3,4.2-5.1l7.1,4.6c-2,3.4-4.4,6-7.2,7.7c-2.8,1.7-5.9,2.6-9.2,2.6
							c-3.1,0-5.9-0.5-8.6-1.6c-2.6-1.1-4.9-2.8-6.8-5.1c-1.9-2.3-3.4-5.2-4.5-8.6c-1.1-3.4-1.6-7.4-1.6-12.1c0-3.4,0.3-6.5,0.9-9.2
							c0.6-2.7,1.4-5.1,2.4-7.2c1-2.1,2.2-3.8,3.6-5.2c1.4-1.4,2.8-2.6,4.4-3.5c1.5-0.9,3.1-1.6,4.8-2c1.7-0.4,3.3-0.6,4.8-0.6
							c2,0,3.8,0.3,5.6,0.8c1.8,0.6,3.5,1.4,5,2.4c1.5,1.1,2.9,2.3,4.1,3.8c1.2,1.5,2.2,3.1,2.9,4.9L2491.7-3051.5z"/>
						<path class="st8" d="M2544.9-3021.8v7.9h-33.4v-52.3h10.2c0.1,0.1,0.2,0.3,0.2,0.4c0,0.2-0.1,0.5-0.3,0.8
							c-0.2,0.3-0.4,0.9-0.5,1.7v41.5H2544.9z"/>
						<path class="st8" d="M2560-3066.3h29.2v7.8h-10.2v36.8h10.6v7.7h-30.2v-7.8h10.2v-36.7h-9.7V-3066.3z"/>
					</g>
				</g>
			</g>
			<rect x="2233.3" y="-2950.6" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 2233.2637 -2935.8225)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="2235.3" y="-2851.1" class="st14" width="230" height="59.2"/>
				<rect x="2241.2" y="-2845.2" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 2292.1411 -2812.6399)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M2894.9-3167.1v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C2893.6-3170.1,2894.9-3168.7,2894.9-3167.1z"/>
			<path class="st62" d="M2894.9-3167.1v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C2893.6-3170.1,2894.9-3168.7,2894.9-3167.1z"/>
		</g>
		<line class="st66" x1="2154.6" y1="-3160.4" x2="2163" y2="-3151.4"/>
		<line class="st66" x1="2154.3" y1="-3151.7" x2="2163.3" y2="-3160.1"/>
		<line class="st66" x1="2114.6" y1="-3220.4" x2="2123" y2="-3211.4"/>
		<line class="st66" x1="2114.3" y1="-3211.7" x2="2123.3" y2="-3220.1"/>
		<line class="st66" x1="2077.6" y1="-3275.4" x2="2086" y2="-3266.4"/>
		<line class="st66" x1="2077.3" y1="-3266.7" x2="2086.3" y2="-3275.1"/>
	</g>
	<linearGradient id="SVGID_62_" gradientUnits="userSpaceOnUse" x1="1641" y1="-1667.3009" x2="3239" y2="-1667.3009">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st100" points="3239,-732.1 3239,-2602.6 1641,-2602.6 1641,-736 	"/>
	<line class="st50" x1="2356.8" y1="-2328.7" x2="2355.1" y2="-2328.7"/>
	<line class="st38" x1="1940.7" y1="-2305.6" x2="1938.5" y2="-2305.6"/>
	
		<linearGradient id="SVGID_63_" gradientUnits="userSpaceOnUse" x1="-1201.5189" y1="1216.004" x2="-821.5187" y2="1216.004" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 1552.7886 -2231.2971)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st101" points="2803.4,-1317.6 2776.1,-938.6 2582.6,-952.5 2609.9,-1331.5 	"/>
	
		<linearGradient id="SVGID_64_" gradientUnits="userSpaceOnUse" x1="2752.3081" y1="-1416.5347" x2="2956.3083" y2="-1416.5347" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 290.6579 -98.123)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st102" points="3043.5,-1661.6 2841.8,-1631 2756.5,-2192.5 2958.2,-2223.1 	"/>
	
		<linearGradient id="SVGID_65_" gradientUnits="userSpaceOnUse" x1="-1046.4729" y1="844.9454" x2="-666.473" y2="844.9454" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 1308.3594 -3181.1709)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st103" points="2167.2,-2410.6 2123.5,-2033.1 1930.8,-2055.5 1974.5,-2433 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M2841.4-2116.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2844-2117.7,2842.8-2116.6,2841.4-2116.6z"/>
		</g>
		<g>
			<path class="st72" d="M2831.4-2126.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2834-2127.7,2832.8-2126.6,2831.4-2126.6z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 2338.6865 -2284.4666)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-21.5" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="1FE9CA9FC2C9417.png"  transform="matrix(1 0 0 1 2344 -2363.0515)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 2347.1631 -2331.3308)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_66_" gradientUnits="userSpaceOnUse" x1="887.1075" y1="-2044.7302" x2="1267.1077" y2="-2044.7302" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 980.2065 948.5847)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st104" points="2155.3,-1056 1775.7,-1038.3 1766.7,-1232.1 2146.3,-1249.8 	"/>
	<path class="st74" d="M2275.3-2257.6h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C2277.3-2258.4,2276.4-2257.6,2275.3-2257.6z"/>
	<rect x="2153.7" y="-2354.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C9414.png"  transform="matrix(1 0 0 1 2157.8557 -2333.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_67_" gradientUnits="userSpaceOnUse" x1="2199.1499" y1="-2316.9705" x2="2200.8394" y2="-2331.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2163.3" y="-2327.4" class="st105" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C941C.png"  transform="matrix(1 0 0 1 2158.1003 -2315.9514)">
		</image>
		<g>
			<linearGradient id="SVGID_68_" gradientUnits="userSpaceOnUse" x1="2186.9534" y1="-2302.2866" x2="2188.2815" y2="-2313.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2163.2" y="-2311" class="st106" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C941D.png"  transform="matrix(1 0 0 1 2157.8435 -2301.2083)">
		</image>
		<g>
			<linearGradient id="SVGID_69_" gradientUnits="userSpaceOnUse" x1="2191.6628" y1="-2285.8679" x2="2193.1658" y2="-2298.8254">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2163.2" y="-2295.5" class="st107" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C941F.png"  transform="matrix(1 0 0 1 2156.9124 -2284.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_70_" gradientUnits="userSpaceOnUse" x1="2188.1272" y1="-2269.1079" x2="2189.5059" y2="-2280.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2162.8" y="-2278.1" class="st108" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C941B.png"  transform="matrix(1 0 0 1 2214.1003 -2315.9514)">
		</image>
		<g>
			<linearGradient id="SVGID_71_" gradientUnits="userSpaceOnUse" x1="2227.0667" y1="-2304.1665" x2="2227.959" y2="-2311.8579">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2219.5" y="-2311" class="st109" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="2200" y1="-2347.1" x2="2240" y2="-2347.1"/>
	<circle class="st18" cx="2161.5" cy="-2347.6" r="1.5"/>
	<circle class="st18" cx="2167.5" cy="-2347.6" r="1.5"/>
	<line class="st50" x1="2358.8" y1="-1882.7" x2="2357.1" y2="-1882.7"/>
	<line class="st38" x1="1942.7" y1="-1859.6" x2="1940.5" y2="-1859.6"/>
	<g class="st16">
		<g>
			<path class="st14" d="M2843.4-1670.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2846-1671.7,2844.8-1670.6,2843.4-1670.6z"/>
		</g>
		<g>
			<path class="st72" d="M2833.4-1680.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2836-1681.7,2834.8-1680.6,2833.4-1680.6z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="1FE9CA9FC2C9416.png"  transform="matrix(1 0 0 1 2343 -1936.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 2346.3887 -1904.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M2277.3-1811.6h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C2279.3-1812.4,2278.4-1811.6,2277.3-1811.6z"/>
	<rect x="2155.7" y="-1908.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C9478.png"  transform="matrix(1 0 0 1 2159.8557 -1887.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_72_" gradientUnits="userSpaceOnUse" x1="2201.1499" y1="-1870.9703" x2="2202.8394" y2="-1885.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2165.3" y="-1881.4" class="st110" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C947B.png"  transform="matrix(1 0 0 1 2160.1003 -1869.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_73_" gradientUnits="userSpaceOnUse" x1="2188.9534" y1="-1856.2865" x2="2190.2815" y2="-1867.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2165.2" y="-1865" class="st111" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C9479.png"  transform="matrix(1 0 0 1 2159.8435 -1855.2081)">
		</image>
		<g>
			<linearGradient id="SVGID_74_" gradientUnits="userSpaceOnUse" x1="2193.6628" y1="-1839.8679" x2="2195.1658" y2="-1852.8253">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2165.2" y="-1849.5" class="st112" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C9407.png"  transform="matrix(1 0 0 1 2158.9124 -1838.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_75_" gradientUnits="userSpaceOnUse" x1="2190.1272" y1="-1823.1078" x2="2191.5059" y2="-1834.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2164.8" y="-1832.1" class="st113" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C9408.png"  transform="matrix(1 0 0 1 2216.1003 -1869.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_76_" gradientUnits="userSpaceOnUse" x1="2229.0667" y1="-1858.1666" x2="2229.959" y2="-1865.858">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2221.5" y="-1865" class="st114" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="2202" y1="-1901.1" x2="2242" y2="-1901.1"/>
	<circle class="st18" cx="2163.5" cy="-1901.6" r="1.5"/>
	<circle class="st18" cx="2169.5" cy="-1901.6" r="1.5"/>
	<rect x="2343.1" y="-1875.1" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 2343.1143 -1861.7743)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="2361.8" y1="-1419.7" x2="2360.1" y2="-1419.7"/>
	<line class="st38" x1="1945.7" y1="-1396.6" x2="1943.5" y2="-1396.6"/>
	<g class="st16">
		<g>
			<path class="st14" d="M2846.4-1207.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2849-1208.7,2847.8-1207.6,2846.4-1207.6z"/>
		</g>
		<g>
			<path class="st72" d="M2836.4-1217.6l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C2839-1218.7,2837.8-1217.6,2836.4-1217.6z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="1FE9CA9FC2C940B.png"  transform="matrix(1 0 0 1 2346 -1473.0516)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 2349.3887 -1441.3309)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M2280.3-1348.6h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C2282.3-1349.4,2281.4-1348.6,2280.3-1348.6z"/>
	<rect x="2158.7" y="-1445.1" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="1FE9CA9FC2C9409.png"  transform="matrix(1 0 0 1 2162.8557 -1424.1958)">
		</image>
		<g>
			<linearGradient id="SVGID_77_" gradientUnits="userSpaceOnUse" x1="2204.1499" y1="-1407.9703" x2="2205.8394" y2="-1422.5374">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2168.3" y="-1418.4" class="st115" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="1FE9CA9FC2C940F.png"  transform="matrix(1 0 0 1 2163.1003 -1406.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_78_" gradientUnits="userSpaceOnUse" x1="2191.9534" y1="-1393.2865" x2="2193.2815" y2="-1404.738">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2168.2" y="-1402" class="st116" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="1FE9CA9FC2C940A.png"  transform="matrix(1 0 0 1 2162.8435 -1392.2081)">
		</image>
		<g>
			<linearGradient id="SVGID_79_" gradientUnits="userSpaceOnUse" x1="2196.6628" y1="-1376.8679" x2="2198.1658" y2="-1389.8253">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2168.2" y="-1386.5" class="st117" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="1FE9CA9FC2C9405.png"  transform="matrix(1 0 0 1 2161.9124 -1375.1394)">
		</image>
		<g>
			<linearGradient id="SVGID_80_" gradientUnits="userSpaceOnUse" x1="2193.1272" y1="-1360.1078" x2="2194.5059" y2="-1371.9954">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2167.8" y="-1369.1" class="st118" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="1FE9CA9FC2C942F.png"  transform="matrix(1 0 0 1 2219.1003 -1406.9513)">
		</image>
		<g>
			<linearGradient id="SVGID_81_" gradientUnits="userSpaceOnUse" x1="2232.0667" y1="-1395.1666" x2="2232.959" y2="-1402.858">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="2224.5" y="-1402" class="st119" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="2205" y1="-1438.1" x2="2245" y2="-1438.1"/>
	<circle class="st18" cx="2166.5" cy="-1438.6" r="1.5"/>
	<circle class="st18" cx="2172.5" cy="-1438.6" r="1.5"/>
	<rect x="2346.1" y="-1412.1" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 2346.1143 -1398.7743)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<rect x="1638.5" y="-994.6" class="st64" width="1602" height="444"/>
	<path class="st120" d="M1747-2951.1"/>
	<path class="st120" d="M1764.5-2933.6"/>
	<path class="st14" d="M1866.6-2753.4c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C1866.8-2753.4,1866.7-2753.4,1866.6-2753.4z"/>
	<path class="st14" d="M1866.2-2754.3c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M1857.4-2726.6c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
		C1857.6-2726.6,1857.5-2726.6,1857.4-2726.6z"/>
	<linearGradient id="SVGID_82_" gradientUnits="userSpaceOnUse" x1="1903.5889" y1="-3027.4766" x2="1933.5912" y2="-3027.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_83_" gradientUnits="userSpaceOnUse" x1="1903.2179" y1="-3027.4766" x2="1933.9623" y2="-3027.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st121" d="M1922.7-3018.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C1922.8-3018.6,1922.8-3018.6,1922.7-3018.6z"/>
	<linearGradient id="SVGID_84_" gradientUnits="userSpaceOnUse" x1="1903.5872" y1="-3011.293" x2="1930.8262" y2="-3011.293">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_85_" gradientUnits="userSpaceOnUse" x1="1903.2162" y1="-3011.293" x2="1931.1973" y2="-3011.293">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st122" d="M1930.6-3001.4c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C1930.9-3001.8,1930.8-3001.5,1930.6-3001.4z"/>
	<linearGradient id="SVGID_86_" gradientUnits="userSpaceOnUse" x1="1929.9719" y1="-3018.0071" x2="1941.4713" y2="-3018.0071">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_87_" gradientUnits="userSpaceOnUse" x1="1929.601" y1="-3018.0071" x2="1941.8423" y2="-3018.0071">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st123" d="M1930.6-3001.4c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C1930.7-3001.5,1930.6-3001.5,1930.6-3001.4z"/>
	<linearGradient id="SVGID_88_" gradientUnits="userSpaceOnUse" x1="3086.7239" y1="-2778.9739" x2="3123.9116" y2="-2778.9739">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st124" d="M3097.1-2768c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C3097.3-2767.9,3097.2-2767.9,3097.1-2768z"
		/>
	<linearGradient id="SVGID_89_" gradientUnits="userSpaceOnUse" x1="3070.9363" y1="-2769.8533" x2="3122.8965" y2="-2769.8533">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st125" d="M3081.4-2746.8c-0.1-0.1-0.2-0.1-0.2-0.2L3071-2771c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C3082-2746.7,3081.6-2746.6,3081.4-2746.8z"/>
	<linearGradient id="SVGID_90_" gradientUnits="userSpaceOnUse" x1="3081.127" y1="-2755.0007" x2="3123.9141" y2="-2755.0007">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st126" d="M3122.8-2765.9c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_91_" gradientUnits="userSpaceOnUse" x1="3008.5015" y1="-3180.9949" x2="3063.0801" y2="-3180.9949">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st127" d="M3056.9-3200.2C3056.9-3200.3,3056.9-3200.3,3056.9-3200.2c-0.1-0.1-0.1-0.2-0.1-0.2c0,0-0.1-0.1-0.1-0.1
		c0,0,0,0-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l-26.6-8.5c-0.2-0.1-0.5,0-0.6,0.1l-20.5,19.1c0,0,0,0,0,0
		c-0.1,0.1-0.1,0.1-0.1,0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1,0,0.2c0,0,0,0.1,0,0.1c0,0,0,0,0,0l6.2,27.7c0.1,0.2,0.2,0.4,0.4,0.5
		l26.6,8.4c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0,0,0,0,0,0l20.5-19.1c0.2-0.2,0.2-0.4,0.2-0.6L3056.9-3200.2z M3015.9-3162.3
		l-5.9-26.3l25.2,8l5.9,26.3L3015.9-3162.3z"/>
	<path class="st92" d="M1948.5-3516.3l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C1948.6-3516,1948.6-3516.2,1948.5-3516.3z M1911.7-3516.2l8.9-15.4l17.6-0.1l-8.9,15.5
		L1911.7-3516.2z"/>
	<rect x="3297" y="-3399.1" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 3303 -3384.8518)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<g>
		<g>
			<path class="st46" d="M3332.1-3385h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-3385z M3362.8-3398v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H3362.8L3362.8-3398z M3353.8-3394.7h3.2v6.5h-3.2V-3394.7z M3347.4-3381.8h6.4v-3.2h6.4v-13h-12.8V-3381.8z"/>
			<rect x="3332.1" y="-3398" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="3397.1,-3394.8 3397.1,-3388.5 3403.2,-3388.5 3403.2,-3385.4 3397,-3385.4 3390.7,-3385.4 
			3390.8,-3398 3403.2,-3398 3403.2,-3394.9 		"/>
		<rect x="3405.8" y="-3398" class="st46" width="6.4" height="12.9"/>
		
			<rect x="3411.7" y="-3391.4" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 26.599 -6799.8765)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="3432.9" y="-3390" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 47.9678 -6819.7383)" class="st46" width="2" height="8.3"/>
		<rect x="3419.9" y="-3398.1" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_92_" gradientUnits="userSpaceOnUse" x1="3260" y1="-3419.5515" x2="4860" y2="-3419.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st128" x1="3260" y1="-3419.6" x2="4860" y2="-3419.6"/>
	<linearGradient id="SVGID_93_" gradientUnits="userSpaceOnUse" x1="4662.5" y1="-3371.5515" x2="4697.5" y2="-3371.5515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st129" x1="4662.5" y1="-3371.6" x2="4697.5" y2="-3371.6"/>
	<line class="st130" x1="3261" y1="-3355.1" x2="4860" y2="-3355.1"/>
	<g>
		<path class="st14" d="M3306-3309.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C3300.5-3307.2,3302.9-3309.5,3306-3309.5z"/>
		<path class="st14" d="M3316.8-3298.1c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C3321.4-3299.9,3319.4-3298.1,3316.8-3298.1z M3316.8-3300c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C3314.7-3300.9,3315.7-3300,3316.8-3300z"/>
		<path class="st14" d="M3329-3303.1c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V-3303.1z"/>
		<path class="st14" d="M3333.5-3305.3h-1v-1.8h1v-0.4c0-2.2,1.2-3.2,3.6-3.1v1.9c-1.1,0-1.4,0.3-1.4,1.3v0.4h1.5v1.8h-1.5v7h-2.2
			V-3305.3z"/>
		<path class="st14" d="M3338.4-3309.5c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3
			C3339-3308.2,3338.4-3308.7,3338.4-3309.5z M3338.6-3307.1h2.2v8.9h-2.2V-3307.1z"/>
		<path class="st14" d="M3346.3-3307.2c1.4,0,2.3,0.6,2.9,1.4v-1.3h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3h2.2
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S3344.1-3307.2,3346.3-3307.2z
			 M3346.9-3305.3c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S3348.1-3305.3,3346.9-3305.3z"/>
		<path class="st14" d="M3361.7-3298.2h-2.2v-1.1c-0.5,0.8-1.5,1.2-2.6,1.2c-2,0-3.5-1.3-3.5-3.8v-5.2h2.2v4.9
			c0,1.4,0.8,2.2,1.9,2.2c1.2,0,1.9-0.8,1.9-2.2v-4.9h2.2V-3298.2z"/>
		<path class="st14" d="M3366-3298.2h-2.2v-8.9h2.2v1.4c0.5-0.9,1.5-1.5,2.7-1.5v2.4h-0.6c-1.3,0-2.1,0.5-2.1,2.2V-3298.2z"/>
		<path class="st14" d="M3373.9-3298.1c-2.5,0-4.3-1.8-4.3-4.6c0-2.8,1.8-4.6,4.3-4.6c2.5,0,4.3,1.7,4.3,4.4c0,0.3,0,0.6-0.1,0.9
			h-6.3c0.1,1.3,1,2,2.1,2c0.9,0,1.5-0.5,1.7-1.1h2.4C3377.5-3299.4,3376-3298.1,3373.9-3298.1z M3371.8-3303.5h4.1
			c0-1.2-0.9-1.9-2.1-1.9C3372.8-3305.4,3371.9-3304.7,3371.8-3303.5z"/>
		<path class="st14" d="M3392.7-3309.4v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H3392.7z"/>
		<path class="st14" d="M3398.9-3302.5h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C3402.8-3304.2,3401.7-3302.5,3398.9-3302.5z
			 M3398.8-3304.3c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H3398.8z"/>
		<path class="st14" d="M3404.3-3309.4h2.5l3.5,8.3l3.5-8.3h2.5v11.2h-2.2v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V-3309.4z"/>
	</g>
	<g>
		<path class="st14" d="M3301-3264.4h2.2v6.9c0,1.5,0.8,2.3,2.2,2.3c1.4,0,2.2-0.8,2.2-2.3v-6.9h2.2v6.9c0,2.9-2.1,4.4-4.4,4.4
			c-2.4,0-4.4-1.4-4.4-4.4V-3264.4z"/>
		<path class="st14" d="M3315.2-3253.1c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C3318.6-3254.2,3317.3-3253.1,3315.2-3253.1z"/>
		<path class="st14" d="M3320.2-3264.5c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3
			C3320.7-3263.2,3320.2-3263.7,3320.2-3264.5z M3320.4-3262.1h2.2v8.9h-2.2V-3262.1z"/>
		<path class="st14" d="M3330.8-3258.1c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V-3258.1z"/>
		<path class="st14" d="M3338.4-3262.2c1.4,0,2.3,0.6,2.9,1.4v-1.3h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3h2.2
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S3336.2-3262.2,3338.4-3262.2z
			 M3339-3260.3c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S3340.2-3260.3,3339-3260.3z"/>
		<path class="st14" d="M3358.6-3264.4v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H3358.6z"/>
		<path class="st14" d="M3364.8-3257.5h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C3368.7-3259.2,3367.6-3257.5,3364.8-3257.5z
			 M3364.7-3259.3c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H3364.7z"/>
		<path class="st14" d="M3370.2-3264.4h2.5l3.5,8.3l3.5-8.3h2.5v11.2h-2.2v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V-3264.4z"/>
	</g>
	<g>
		<path class="st14" d="M3306.9-3220.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C3301.4-3218.2,3303.7-3220.5,3306.9-3220.5z"/>
		<path class="st14" d="M3316-3220.4v9.4h3.6v1.8h-5.8v-11.2H3316z"/>
		<path class="st14" d="M3321-3220.4h2.2v11.2h-2.2V-3220.4z"/>
		<path class="st14" d="M3334-3220.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C3328.5-3218.2,3330.9-3220.5,3334-3220.5z"/>
		<path class="st14" d="M3344.8-3209.1c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C3349.4-3210.9,3347.4-3209.1,3344.8-3209.1z M3344.8-3211c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C3342.7-3211.9,3343.7-3211,3344.8-3211z"/>
		<path class="st14" d="M3363-3214.1c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9h-2.2v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2v-8.9h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2h-2.2V-3214.1z"/>
		<path class="st14" d="M3379.4-3214.1c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9h-2.2v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2v-8.9h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2h-2.2V-3214.1z"/>
		<path class="st14" d="M3387-3218.2c1.4,0,2.3,0.7,2.9,1.4v-1.3h2.2v8.9h-2.2v-1.3c-0.5,0.8-1.5,1.4-2.9,1.4
			c-2.2,0-3.9-1.8-3.9-4.6S3384.8-3218.2,3387-3218.2z M3387.6-3216.3c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6
			c1.2,0,2.3-0.9,2.3-2.6S3388.8-3216.3,3387.6-3216.3z"/>
		<path class="st14" d="M3400.3-3214.1c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V-3214.1z"/>
		<path class="st14" d="M3407.9-3218.2c1.1,0,2.2,0.5,2.8,1.4v-4.2h2.2v11.8h-2.2v-1.3c-0.5,0.8-1.5,1.5-2.8,1.5c-2.2,0-4-1.8-4-4.6
			S3405.7-3218.2,3407.9-3218.2z M3408.4-3216.3c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6
			S3409.6-3216.3,3408.4-3216.3z"/>
		<path class="st14" d="M3418.4-3209.1c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C3421.8-3210.2,3420.5-3209.1,3418.4-3209.1z"/>
	</g>
	<text transform="matrix(1 0 0 1 4662.9082 -3381.7502)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<text transform="matrix(1 0 0 1 1967.1631 -3324.1819)"><tspan x="0" y="0" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">These little terminal windows could be secretly </tspan><tspan x="0" y="14.4" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">dismissable, and if you close all they just reappear again</tspan></text>
	<text transform="matrix(1 0 0 1 2737.1631 -3012.1819)" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">&lt;----- imagine this is blinking </text>
	<text transform="matrix(1 0 0 1 2350.1631 -2185.1819)" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">Hmm I should probably put some CTAs in these sections</text>
	<g>
		<rect x="2047.1" y="-832.1" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 2047.1143 -818.7742)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod Lorem ipsum </tspan><tspan x="0" y="27" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">dolor sit amet, tetuer adipiscing elit, sed diam nonummy nibmod </tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 3299.8115 -3164.0515)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">set access level on published packages</text>
	
		<text transform="matrix(0.9997 -2.420000e-02 2.420000e-02 0.9997 3301.1204 -3179.4026)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">access</text>
	
		<text transform="matrix(1 0 0 1 3300.8115 -3135.0793)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">add user</text>
	<g>
		<text transform="matrix(1 0 0 1 3300.8115 -3042.0793)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bin</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 3300.8115 -2995.0793)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bugs</text>
	</g>
	<rect x="3888" y="-2665.1" class="st133" width="64" height="27"/>
	<rect x="4044" y="-2666.1" class="st133" width="64" height="27"/>
	<g>
		<text transform="matrix(1 0 0 1 3300.8115 -2947.0793)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">build</text>
	</g>
	<text transform="matrix(1 0 0 1 3300.8115 -2903.0793)" class="st51"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bundle</tspan><tspan x="0" y="39.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">cache </tspan><tspan x="0" y="53" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">manipulates packages cache</tspan><tspan x="0" y="86.6" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">ci </tspan><tspan x="0" y="98.6" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">install a project with a clean slate</tspan><tspan x="0" y="132.2" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">config</tspan><tspan x="0" y="144.2" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">manage npm configuration files</tspan><tspan x="0" y="177.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dedupe</tspan><tspan x="0" y="189.8" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">reduce duplication</tspan><tspan x="0" y="223.4" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">deprecate</tspan><tspan x="0" y="235.4" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">deprecate a version of a package</tspan><tspan x="0" y="269" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dist-tag</tspan><tspan x="0" y="281" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">modify package distribution tags</tspan></text>
	<text transform="matrix(1 0 0 1 3300.8115 -3118.0515)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">add a registry user account</text>
	<g>
		<text transform="matrix(1 0 0 1 3300.8115 -3088.2971)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">audit</text>
		<text transform="matrix(1 0 0 1 3299.8115 -3073.1648)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">run a security audit</text>
	</g>
	<text transform="matrix(1 0 0 1 3299.8115 -3028.0515)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">display npm bin folder</text>
	<rect x="3791" y="-2844.1" class="st133" width="96" height="25"/>
	<text transform="matrix(1 0 0 1 3301.8115 -2934.0515)" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">build a package</text>
	<text transform="matrix(1 0 0 1 3300.8115 -2891.0515)" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">removed</text>
	<rect x="3909" y="-2808.1" class="st133" width="49" height="21"/>
	<rect x="4407" y="-2844.1" class="st133" width="125" height="26"/>
	<text transform="matrix(1 0 0 1 3301.8115 -2981.0515)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">bugs for a package in a web browser maybe</text>
	<polyline class="st134" points="3428,-3307.1 3434,-3301.1 3440,-3307.1 	"/>
	<polyline class="st134" points="3430,-3212.1 3436,-3218.1 3442,-3212.1 	"/>
	<polyline class="st134" points="3394,-3262.1 3400,-3256.1 3406,-3262.1 	"/>
	<rect x="3291" y="-3014.1" class="st135" width="282" height="45"/>
	<text transform="matrix(1 0 0 1 3758.9707 -3270.0046)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-SemiBold'; font-size:42px;">    npm-bugs</tspan><tspan x="0" y="40" class="st98" style="font-family:'Poppins-Regular'; font-size:24px;">Bugs for a package in a web browser maybe</tspan></text>
	<text transform="matrix(1 0 0 1 3760.7861 -3147.7585)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Synopsis</text>
	<text transform="matrix(1 0 0 1 3760.7861 -2900.7585)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Description</text>
	<g>
		<rect x="3759.3" y="-2872.1" class="st47" width="894.4" height="310.2"/>
		<text transform="matrix(1 0 0 1 3759.2539 -2860.2273)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">This command tries to guess at the likely location of a package’s bug tracker URL, and then tries to open it using </tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">the</tspan><tspan x="26" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> --browser</tspan><tspan x="122" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> config param. If no package name is provided, it will search for a</tspan><tspan x="643.2" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> package.json</tspan><tspan x="768" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> in the current </tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">folder and use the  </tspan><tspan x="153.9" y="68" class="st98" style="font-family:'AndaleMono'; font-size:16px;">name</tspan><tspan x="192.3" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">  property.</tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 3760.7861 -2726.7585)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Configuration</text>
	<text transform="matrix(1 0 0 1 3760.7861 -2683.7585)" class="st137" style="font-family:'Poppins-Medium'; font-size:17px;">browser</text>
	<linearGradient id="SVGID_94_" gradientUnits="userSpaceOnUse" x1="1261.4564" y1="-789.5836" x2="384.5436" y2="255.4804">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="24" y="-487.1" class="st138" width="1598" height="440"/>
	<text transform="matrix(1 0 0 1 3761.7861 -2554.7585)" class="st137" style="font-family:'Poppins-Medium'; font-size:17px;">registry</text>
	<g>
		<text transform="matrix(1 0 0 1 3761.7861 -2395.7585)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">See Also</text>
	</g>
	<g>
		<rect x="3777.3" y="-2658.8" class="st47" width="754.9" height="125.6"/>
		<text transform="matrix(1 0 0 1 3777.2998 -2646.9675)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: OS X:</tspan><tspan x="104.6" y="0" class="st98" style="font-family:'Inconsolata-Bold'; font-size:16px;"> </tspan><tspan x="108" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;open&quot;,</tspan><tspan x="175.2" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> Windows: </tspan><tspan x="259.5" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;start&quot;</tspan><tspan x="326.7" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">, Others:  </tspan><tspan x="398.9" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;xdg-open&quot;</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: String</tspan></text>
	</g>
	<circle class="st98" cx="3767" cy="-2653.1" r="4"/>
	<circle class="st98" cx="3767" cy="-2618.1" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 3777.2998 -2522.2927)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: https://registry.npmjs.org/</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: url</tspan></text>
	</g>
	<rect x="4180" y="-2663.1" class="st133" width="94" height="25"/>
	<circle class="st98" cx="3766" cy="-2529.1" r="4"/>
	<circle class="st98" cx="3766" cy="-2494.1" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 3766.1631 -2348.1433)"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-docs</tspan><tspan x="0" y="29" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-view</tspan><tspan x="0" y="58" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-publish</tspan><tspan x="0" y="87" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-registry</tspan><tspan x="0" y="116" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="145" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="174" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npmrc</tspan><tspan x="0" y="203" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">package.json</tspan></text>
	</g>
	<line class="st2" x1="3758" y1="-2381.1" x2="4605.5" y2="-2381.1"/>
	<path class="st98" d="M4525.6-2970.1h-760.3c-1.9,0-3.4-1.5-3.4-3.4v-146.3c0-1.9,1.5-3.4,3.4-3.4h760.3c1.9,0,3.4,1.5,3.4,3.4
		v146.3C4529-2971.6,4527.5-2970.1,4525.6-2970.1z"/>
	<text transform="matrix(1 0 0 1 3788.2207 -3063.4768)"><tspan x="0" y="0" class="st8" style="font-family:'AndaleMono'; font-size:30px;">npm bugs [&lt;pkgname&gt;]</tspan><tspan x="0" y="60" class="st8" style="font-family:'AndaleMono'; font-size:30px;">aliases: issues</tspan></text>
	<rect x="4258" y="-1990.1" class="st133" width="247" height="30"/>
	<text transform="matrix(1 0 0 1 3791.667 -2004.1219)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">        Found a typo?</tspan><tspan x="147.4" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;"> Let us know!</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">The current stable version of npm is here. To upgrade run:  </tspan><tspan x="468.1" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;">npm install npm@latest -g</tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">To report bugs or submit feature requests for the docs, please post </tspan><tspan x="537" y="68" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here</tspan><tspan x="573.8" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">. </tspan><tspan x="0" y="102" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Submit npm issues</tspan><tspan x="151.9" y="102" style="font-family:'Poppins-Regular'; font-size:16px;"> </tspan><tspan x="156.2" y="102" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here.</tspan></text>
	<rect x="3260" y="-3355.1" class="st139" width="330" height="1207"/>
	<linearGradient id="SVGID_95_" gradientUnits="userSpaceOnUse" x1="3766.9973" y1="-3286.7183" x2="3786.4839" y2="-3286.7183">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_96_" gradientUnits="userSpaceOnUse" x1="3766.7563" y1="-3286.7183" x2="3786.7249" y2="-3286.7183">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st140" d="M3779.4-3280.9c0,0-0.1,0-0.1,0l-12-1.2c-0.2,0-0.3-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.9-9.9
		c0.1-0.1,0.2-0.1,0.3-0.1l12,1.2c0.2,0,0.3,0.1,0.3,0.3c0,0,0,0.1,0,0.1l-6.9,9.9C3779.5-3281,3779.4-3280.9,3779.4-3280.9z"/>
	<linearGradient id="SVGID_97_" gradientUnits="userSpaceOnUse" x1="3766.9963" y1="-3276.207" x2="3784.688" y2="-3276.207">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_98_" gradientUnits="userSpaceOnUse" x1="3766.7554" y1="-3276.207" x2="3784.929" y2="-3276.207">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st141" d="M3784.5-3269.8c0,0-0.1,0-0.1,0l-12-1.1c-0.1,0-0.2-0.1-0.2-0.2l-5.1-11.1c-0.1-0.1,0-0.3,0.1-0.4
		c0.1-0.1,0.3,0,0.4,0.1l5.1,11l11.4,1.1l-4.9-10.7c-0.1-0.1,0-0.3,0.1-0.4c0.1-0.1,0.3,0,0.4,0.1l5.1,11.1
		C3784.7-3270,3784.7-3269.9,3784.5-3269.8z"/>
	<linearGradient id="SVGID_99_" gradientUnits="userSpaceOnUse" x1="3784.1331" y1="-3280.5679" x2="3791.6021" y2="-3280.5679">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_100_" gradientUnits="userSpaceOnUse" x1="3783.8921" y1="-3280.5679" x2="3791.843" y2="-3280.5679">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st142" d="M3784.5-3269.8c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.2-0.3-0.1-0.4l6.8-9.8l-5-11c-0.1-0.1,0-0.3,0.1-0.4
		c0.1-0.1,0.3,0,0.4,0.1l5.1,11.1c0,0.1,0,0.2,0,0.3l-6.9,9.9C3784.6-3269.9,3784.6-3269.8,3784.5-3269.8z"/>
	<path class="st120" d="M625-351.1"/>
	<path class="st120" d="M242.5-505.6"/>
	<path class="st143" d="M146.6-237.4c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1l27.4-5.5
		c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5C146.8-237.4,146.7-237.4,146.6-237.4z
		"/>
	<path class="st14" d="M146.2-238.3c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2L118.8-232c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M137.4-210.6c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4c0.1-0.3,0.5-0.5,0.8-0.4
		c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5C137.6-210.6,137.5-210.6,137.4-210.6z"/>
	<linearGradient id="SVGID_101_" gradientUnits="userSpaceOnUse" x1="638.5889" y1="-427.4766" x2="668.5912" y2="-427.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_102_" gradientUnits="userSpaceOnUse" x1="638.2179" y1="-427.4766" x2="668.9622" y2="-427.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st144" d="M657.7-418.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C657.8-418.6,657.8-418.6,657.7-418.6z"/>
	<linearGradient id="SVGID_103_" gradientUnits="userSpaceOnUse" x1="638.5872" y1="-411.293" x2="665.8262" y2="-411.293">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st145" d="M665.6-401.4c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		s0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C665.9-401.8,665.8-401.5,665.6-401.4z"/>
	<linearGradient id="SVGID_104_" gradientUnits="userSpaceOnUse" x1="664.9719" y1="-418.0071" x2="676.4713" y2="-418.0071">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st146" d="M665.6-401.4c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C665.7-401.5,665.6-401.5,665.6-401.4z"/>
	<path class="st120" d="M1496.5-807.1"/>
	<path class="st92" d="M1436.9-373.4c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19C1437.1-373.5,1437-373.4,1436.9-373.4z
		"/>
	<path class="st92" d="M1443.1-345.7c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		s0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
		C1443.7-346.2,1443.4-345.8,1443.1-345.7z"/>
	<path class="st92" d="M1443.1-345.7c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C1443.3-345.8,1443.2-345.8,1443.1-345.7z"/>
	<path class="st93" d="M1080.1-172c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C1080.3-171.9,1080.2-171.9,1080.1-172z"/>
	<path class="st93" d="M1064.4-150.8c-0.1-0.1-0.2-0.1-0.2-0.2L1054-175c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C1065-150.7,1064.6-150.6,1064.4-150.8z"/>
	<path class="st93" d="M1105.8-169.9c0.2-0.3,0.6-0.3,0.8-0.1s0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2l-25.9-2.9
		c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<g>
		<rect x="357.1" y="-818.1" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 381.9971 -800.3338)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy</tspan><tspan x="-25.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="-20.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nibh</tspan><tspan x="32.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="37.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">euismod</tspan><tspan x="142.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="148.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem</tspan><tspan x="222.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="227.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">ipsum</tspan><tspan x="302.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="307.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">dolor</tspan><tspan x="369.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="374.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sit</tspan><tspan x="401.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="407" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">amet,</tspan><tspan x="476.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="481.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">tetuer</tspan><tspan x="552.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="558.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">adipiscing</tspan><tspan x="683.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="688.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">elit,</tspan><tspan x="728.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="733.8" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sed</tspan><tspan x="777.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="782.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">diam</tspan><tspan x="845.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="851" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nonum</tspan><tspan x="937.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">-</tspan><tspan x="393.3" y="100" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">my nibmod </tspan></text>
	</g>
	<g>
		<rect x="827.7" y="-638.2" class="st14" width="23.3" height="6.1"/>
	</g>
	<linearGradient id="SVGID_105_" gradientUnits="userSpaceOnUse" x1="468.1465" y1="-3211.3015" x2="469.3535" y2="-3211.3015">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st147" x1="469" y1="-3211.1" x2="468.5" y2="-3211.6"/>
	<line class="st40" x1="514.9" y1="-3278.3" x2="512.2" y2="-3278.3"/>
	<line class="st40" x1="483.2" y1="-3251" x2="480.5" y2="-3251"/>
	<line class="st40" x1="679.8" y1="-3219.9" x2="677.1" y2="-3219.9"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="1FE9CA9FC2C9422.png"  transform="matrix(1 0 0 1 358 -3315.0515)">
			</image>
			<g>
				<path class="st98" d="M1129.9-3296.4v468.9c0,2.2-1.8,4-4,4H377.3c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C1128.6-3299.4,1129.9-3298,1129.9-3296.4z"/>
				<path class="st81" d="M1129.9-3296.4v468.9c0,2.2-1.8,4-4,4H377.3c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C1128.6-3299.4,1129.9-3298,1129.9-3296.4z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M1129.9-3296.1v21.7c0,1.7-1.4,3-3,3H376.3c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C1128.6-3299.1,1129.9-3297.7,1129.9-3296.1z"/>
			<path class="st62" d="M1129.9-3296.1v21.7c0,1.7-1.4,3-3,3H376.3c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C1128.6-3299.1,1129.9-3297.7,1129.9-3296.1z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="515.4" y1="-3203.4" x2="512.6" y2="-3203.4"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="1FE9CA9FC2C9423.png"  transform="matrix(1 0 0 1 397 -3259.0515)">
			</image>
			<g>
				<path class="st98" d="M1168.2-3240.7v468.9c0,2.2-1.8,4-4,4H415.6c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C1166.9-3243.7,1168.2-3242.3,1168.2-3240.7z"/>
				<path class="st99" d="M1168.2-3240.7v468.9c0,2.2-1.8,4-4,4H415.6c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C1166.9-3243.7,1168.2-3242.3,1168.2-3240.7z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M1167.9-3241.1v21.7c0,1.7-1.4,3-3,3H414.3c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C1166.6-3244.1,1167.9-3242.7,1167.9-3241.1z"/>
			<path class="st62" d="M1167.9-3241.1v21.7c0,1.7-1.4,3-3,3H414.3c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C1166.6-3244.1,1167.9-3242.7,1167.9-3241.1z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="1FE9CA9FC2C9425.png"  transform="matrix(1 0 0 1 431 -3199.0515)">
			</image>
			<g>
				<path class="st98" d="M1202.6-3182.3v472.9c0,0.5-0.4,1-1,1H446.9c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C1201.7-3184.3,1202.6-3183.4,1202.6-3182.3z"/>
				<path class="st99" d="M1202.6-3182.3v472.9c0,0.5-0.4,1-1,1H446.9c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C1201.7-3184.3,1202.6-3183.4,1202.6-3182.3z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="936.7" y="-3031.2" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="546.4,-3037.4 542.5,-3042.1 553.7,-3051.6 542.5,-3061.3 546.4,-3065.9 563.2,-3051.6 				"/>
			</g>
			<rect x="541.3" y="-2964.6" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 541.2637 -2949.8225)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="543.3" y="-2865.1" class="st14" width="230" height="59.2"/>
				<rect x="549.2" y="-2859.2" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 600.1411 -2826.6399)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M1202.9-3181.1v24.7c0,0,0,0,0,0H446.4c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C1201.6-3184.1,1202.9-3182.7,1202.9-3181.1z"/>
			<path class="st62" d="M1202.9-3181.1v24.7c0,0,0,0,0,0H446.4c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C1201.6-3184.1,1202.9-3182.7,1202.9-3181.1z"/>
		</g>
		<line class="st66" x1="462.6" y1="-3174.4" x2="471" y2="-3165.4"/>
		<line class="st66" x1="462.3" y1="-3165.7" x2="471.3" y2="-3174.1"/>
		<line class="st66" x1="422.6" y1="-3234.4" x2="431" y2="-3225.4"/>
		<line class="st66" x1="422.3" y1="-3225.7" x2="431.3" y2="-3234.1"/>
		<line class="st66" x1="385.6" y1="-3289.4" x2="394" y2="-3280.4"/>
		<line class="st66" x1="385.3" y1="-3280.7" x2="394.3" y2="-3289.1"/>
	</g>
	<path class="st120" d="M90-2936.1"/>
	<path class="st120" d="M107.5-2918.6"/>
	<path class="st14" d="M209.6-2738.4c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1l27.4-5.5
		c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C209.8-2738.4,209.7-2738.4,209.6-2738.4z"/>
	<path class="st14" d="M209.2-2739.3c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M200.4-2711.6c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4c0.1-0.3,0.5-0.5,0.8-0.4
		c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5C200.6-2711.6,200.5-2711.6,200.4-2711.6z"/>
	<linearGradient id="SVGID_106_" gradientUnits="userSpaceOnUse" x1="246.5889" y1="-3012.4766" x2="276.5912" y2="-3012.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_107_" gradientUnits="userSpaceOnUse" x1="246.2179" y1="-3012.4766" x2="276.9622" y2="-3012.4766">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st148" d="M265.7-3003.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C265.8-3003.6,265.8-3003.6,265.7-3003.6z
		"/>
	<linearGradient id="SVGID_108_" gradientUnits="userSpaceOnUse" x1="246.5872" y1="-2996.293" x2="273.8262" y2="-2996.293">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_109_" gradientUnits="userSpaceOnUse" x1="246.2162" y1="-2996.293" x2="274.1972" y2="-2996.293">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st149" d="M273.6-2986.4c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		s0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C273.9-2986.8,273.8-2986.5,273.6-2986.4z"/>
	<linearGradient id="SVGID_110_" gradientUnits="userSpaceOnUse" x1="272.9719" y1="-3003.0071" x2="284.4713" y2="-3003.0071">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_111_" gradientUnits="userSpaceOnUse" x1="272.6009" y1="-3003.0071" x2="284.8423" y2="-3003.0071">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st150" d="M273.6-2986.4c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C273.7-2986.5,273.6-2986.5,273.6-2986.4z"/>
	<path class="st92" d="M87.7-3269.2c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.2,0,0.5-0.2,0.6c-0.1,0-0.1,0.1-0.2,0.1l-18.6,0.1
		c-0.2,0-0.3-0.1-0.4-0.2l-9.2-16.2c-0.1-0.2,0-0.5,0.2-0.6c0.1,0,0.1-0.1,0.2-0.1l18.6-0.1C87.6-3269.3,87.6-3269.3,87.7-3269.2z"
		/>
	<path class="st92" d="M87.9-3268.7c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.2,0.4-0.3,0.6-0.2
		c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.1,0.1,0.3,0,0.4l-9.4,16.4c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6"/>
	<path class="st92" d="M97.1-3285.6c0.1,0.1,0.2,0.2,0.2,0.4c0,0.2-0.2,0.4-0.4,0.4l-18.4,0.1l-9.3,16.1c-0.1,0.2-0.4,0.3-0.6,0.2
		c-0.2-0.1-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.1,0.2-0.2,0.4-0.2l18.6-0.1C97-3285.6,97.1-3285.6,97.1-3285.6z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="356" height="93" xlink:href="1FE9CA9FC2C9427.png"  transform="matrix(1 0 0 1 574.4106 -3094.6409)">
		</image>
		<g>
			<g>
				<path class="st8" d="M575.6-3025.9v-45.8h10.8v4.7c1.5-1.7,3.3-3.1,5.4-4.1c2.1-1,4.2-1.6,6.3-1.6c2.2,0,4.3,0.3,6.1,1
					c1.8,0.7,3.4,1.8,4.7,3.3c1.3,1.5,2.3,3.5,3,5.9c0.7,2.4,1.1,5.3,1.1,8.7v27.7h-10.5v-27.6c0-1.8-0.2-3.3-0.5-4.6
					c-0.3-1.3-0.8-2.3-1.3-3.1c-0.6-0.8-1.2-1.4-2-1.8c-0.8-0.4-1.6-0.6-2.6-0.6c-1.3,0-2.6,0.3-3.8,0.8c-1.2,0.6-2.2,1.3-3.1,2.3
					c-0.9,1-1.5,2.2-2,3.6c-0.5,1.4-0.7,2.9-0.7,4.6v26.5H575.6z"/>
				<path class="st8" d="M626.7-3071.7H637v4.7c0.7-1,1.5-1.9,2.4-2.6c0.9-0.7,1.8-1.3,2.7-1.8c0.9-0.5,1.9-0.8,2.8-1
					c1-0.2,1.9-0.3,2.8-0.3c2.6,0,5.1,0.5,7.4,1.6c2.3,1,4.3,2.6,6,4.7s3.1,4.7,4.1,7.8c1,3.1,1.5,6.8,1.5,11.1
					c0,3.9-0.5,7.2-1.5,10.1c-1,2.9-2.4,5.3-4.1,7.2c-1.7,1.9-3.7,3.4-6,4.4s-4.7,1.5-7.2,1.5c-1.8,0-3.7-0.4-5.6-1.2
					c-1.9-0.8-3.5-1.9-4.7-3.3v19.4h-10.7L626.7-3071.7z M637.5-3049.3c0,2.8,0.2,5.2,0.6,7.3c0.4,2,1,3.7,1.7,4.9
					c0.7,1.2,1.6,2.2,2.7,2.7c1.1,0.6,2.3,0.9,3.6,0.9c1.1,0,2.2-0.3,3.3-0.8c1.1-0.5,2.2-1.4,3.2-2.5c1-1.1,1.7-2.6,2.3-4.5
					c0.6-1.9,0.9-4.2,0.9-6.9c0-5-0.9-8.8-2.6-11.5c-1.7-2.7-4.2-4-7.5-4c-1.7,0-3.1,0.4-4.2,1.2c-1.1,0.8-1.9,1.8-2.6,3.2
					c-0.6,1.3-1.1,2.9-1.3,4.6C637.6-3053,637.5-3051.2,637.5-3049.3z"/>
				<path class="st8" d="M676.3-3025.9v-46.3h9.2v2.8c1.4-1.5,2.8-2.6,4.2-3.3c1.4-0.7,2.7-1,4-1c0.6,0,1.2,0.1,1.9,0.3
					c0.7,0.2,1.3,0.5,2,0.9c0.6,0.4,1.3,1,1.9,1.7c0.6,0.7,1.1,1.6,1.6,2.6c1.1-1.8,2.5-3.1,4.1-4.1c1.7-0.9,3.3-1.4,5-1.4
					c1.7,0,3.1,0.3,4.3,0.9c1.1,0.6,2,1.5,2.7,2.6c0.7,1.2,1.2,2.6,1.5,4.4c0.3,1.7,0.4,3.7,0.4,6v33.8H709v-32
					c0-2.5-0.2-4.4-0.6-5.5c-0.4-1.1-1.2-1.7-2.2-1.7c-2.4,0-3.6,3-3.6,9v30.1h-9.9v-31.5c0-1.6-0.1-2.9-0.2-3.9s-0.4-1.7-0.7-2.3
					c-0.3-0.6-0.6-0.9-0.9-1.1c-0.3-0.2-0.6-0.3-1-0.3c-0.6,0-1.1,0.1-1.6,0.4c-0.5,0.3-0.9,0.7-1.2,1.4c-0.3,0.7-0.6,1.6-0.8,2.7
					c-0.2,1.1-0.3,2.6-0.3,4.3v30.4H676.3z"/>
				<path class="st8" d="M813.8-3055.6c-0.3-0.1-0.4-0.2-0.6-0.4c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.2-0.1-0.4-0.2-0.7
					c-0.1-0.3-0.1-0.6-0.2-0.8c-0.9-1.5-2.2-2.8-3.8-3.8c-1.6-1-3.6-1.6-6-1.6c-1.6,0-3.1,0.4-4.5,1.1c-1.4,0.7-2.7,1.7-3.8,3
					c-1.1,1.3-1.9,2.8-2.5,4.6c-0.6,1.8-0.9,3.7-0.9,5.9c0,2.2,0.3,4.2,0.9,6c0.6,1.8,1.4,3.4,2.4,4.8c1.1,1.4,2.3,2.5,3.7,3.2
					c1.4,0.8,3,1.2,4.8,1.2c0.9,0,1.8-0.1,2.7-0.3c0.9-0.2,1.9-0.5,2.9-1c1-0.5,1.9-1.1,2.9-1.9c0.9-0.8,1.8-1.7,2.7-2.8l6.2,7.4
					c-2.6,2.9-5.3,5-8.3,6.2c-3,1.3-6.2,1.9-9.6,1.9c-3.3,0-6.2-0.6-9-1.8c-2.7-1.2-5.1-2.9-7-5c-1.9-2.1-3.5-4.7-4.6-7.6
					c-1.1-2.9-1.7-6.1-1.7-9.6c0-3.4,0.5-6.6,1.6-9.6c1.1-3,2.6-5.5,4.6-7.7c2-2.2,4.4-3.9,7.2-5.1c2.8-1.3,5.8-1.9,9.2-1.9
					c1.8,0,3.6,0.2,5.4,0.6c1.8,0.4,3.4,0.9,4.9,1.7c1.5,0.7,2.9,1.6,4.2,2.7c1.3,1.1,2.4,2.4,3.3,3.8L813.8-3055.6z"/>
				<path class="st8" d="M835.1-3092.1h22.4v57.7h12.2v8.5h-35.2v-8.5h12.2v-49.1h-11.7V-3092.1z"/>
				<path class="st8" d="M889.5-3071.7h20.9v37.3h9.9v8.5H889v-8.5h10.8v-28.7h-10.3V-3071.7z M905.2-3092.2c0.9,0,1.8,0.2,2.6,0.5
					c0.8,0.3,1.5,0.8,2.2,1.4c0.6,0.6,1.1,1.3,1.4,2c0.3,0.8,0.5,1.6,0.5,2.4c0,0.9-0.2,1.7-0.5,2.5c-0.4,0.8-0.8,1.5-1.4,2
					c-0.6,0.6-1.3,1-2.2,1.3c-0.8,0.3-1.7,0.5-2.6,0.5c-0.9,0-1.8-0.2-2.6-0.5c-0.8-0.3-1.5-0.8-2.2-1.3c-0.6-0.6-1.1-1.2-1.4-2
					s-0.5-1.6-0.5-2.5c0-0.8,0.1-1.5,0.4-2.3c0.3-0.7,0.7-1.4,1.2-2c0.5-0.6,1.2-1.1,2.1-1.5C903.1-3092,904.1-3092.2,905.2-3092.2z
					"/>
			</g>
		</g>
	</g>
	<path class="st151" d="M3727.9-1600.9"/>
	<path class="st152" d="M3360.5-1526.9c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C3360.6-1526.9,3360.5-1526.9,3360.5-1526.9z
		"/>
	<path class="st14" d="M3360.2-1527.4c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2s0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M3354.9-1510.8c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		s0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C3355-1510.7,3355-1510.8,3354.9-1510.8z"/>
	<linearGradient id="SVGID_112_" gradientUnits="userSpaceOnUse" x1="3736.0706" y1="-1607.1979" x2="3754.0979" y2="-1607.1979">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_113_" gradientUnits="userSpaceOnUse" x1="3735.8477" y1="-1607.1979" x2="3754.3208" y2="-1607.1979">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st153" d="M3747.5-1601.8c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C3747.6-1601.9,3747.6-1601.9,3747.5-1601.8z"/>
	<linearGradient id="SVGID_114_" gradientUnits="userSpaceOnUse" x1="3736.0696" y1="-1597.4739" x2="3752.4365" y2="-1597.4739">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st154" d="M3752.3-1591.6c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3
		C3752.5-1591.8,3752.4-1591.6,3752.3-1591.6z"/>
	<linearGradient id="SVGID_115_" gradientUnits="userSpaceOnUse" x1="3751.9231" y1="-1601.5081" x2="3758.8328" y2="-1601.5081">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st155" d="M3752.3-1591.6c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		s0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C3752.4-1591.6,3752.3-1591.6,3752.3-1591.6z"/>
	<path class="st92" d="M4728.8-1500.5c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4
		C4728.9-1500.6,4728.8-1500.6,4728.8-1500.5z"/>
	<path class="st92" d="M4732.5-1483.9c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C4732.8-1484.2,4732.7-1484,4732.5-1483.9z"/>
	<path class="st92" d="M4732.5-1483.9c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		s0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C4732.6-1484,4732.5-1483.9,4732.5-1483.9z"/>
	<path class="st93" d="M4479.2-1577.6c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C4479.3-1577.5,4479.2-1577.5,4479.2-1577.6z"
		/>
	<path class="st93" d="M4469.7-1564.9c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		c0.2,0.1,0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C4470.1-1564.8,4469.9-1564.7,4469.7-1564.9z"/>
	<path class="st93" d="M4494.6-1576.3c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1
		l-15.5-1.7c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<text transform="matrix(1 0 0 1 3896.54 -1500.453)" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Some footer text or something here </text>
	<path class="st92" d="M1765.5-3270.3l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C1765.6-3270,1765.6-3270.2,1765.5-3270.3z M1728.7-3270.2l8.9-15.4l17.6-0.1l-8.9,15.5
		L1728.7-3270.2z"/>
	<linearGradient id="SVGID_116_" gradientUnits="userSpaceOnUse" x1="1991.4084" y1="-3526.0515" x2="2047.5916" y2="-3526.0515">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st156" d="M2047.6-3531.6c0-0.1,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1-0.1-0.1c0,0,0,0,0,0l-18.2-21.2
		c-0.2-0.2-0.4-0.3-0.6-0.2l-27.4,5.5c0,0,0,0,0,0c-0.1,0-0.2,0.1-0.2,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1
		c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0l-9.3,26.9c-0.1,0.2,0,0.5,0.1,0.6l18.3,21.1c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0,0.2,0,0.3,0
		c0,0,0,0,0,0l27.4-5.5c0.2,0,0.4-0.2,0.5-0.4l9.2-26.8c0,0,0-0.1,0-0.1C2047.6-3531.5,2047.6-3531.5,2047.6-3531.6z M2037.2-3505.4
		l-26,5.2l8.7-25.4l26-5.2L2037.2-3505.4z"/>
	<linearGradient id="SVGID_117_" gradientUnits="userSpaceOnUse" x1="2093.5154" y1="-3529.0515" x2="2146.4846" y2="-3529.0515">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st157" d="M2146.5-3526c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0,0,0,0,0,0l-10.1-24
		c-0.1-0.2-0.3-0.3-0.5-0.4l-25.9-2.9c0,0,0,0,0,0c-0.1,0-0.2,0-0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0-0.1,0-0.1,0.1
		c0,0,0,0,0,0l-15.8,21.2c-0.1,0.2-0.2,0.4-0.1,0.6l10.2,23.9c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.2,0.1c0,0,0,0,0,0l25.9,2.9
		c0.2,0,0.4-0.1,0.5-0.2l15.7-21.1C2146.4-3525.9,2146.4-3525.9,2146.5-3526C2146.4-3526,2146.4-3526,2146.5-3526z M2141.6-3526.1
		l2.1,0.2l-0.2,0.1C2143-3525.8,2142.4-3526,2141.6-3526.1z M2109.8-3551.8l9.6,22.7l-14.9,20.1l-9.6-22.6L2109.8-3551.8z"/>
	<linearGradient id="SVGID_118_" gradientUnits="userSpaceOnUse" x1="2158.96" y1="-3529.5515" x2="2209.04" y2="-3529.5515">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st158" d="M2203.4-3547.2C2203.3-3547.2,2203.3-3547.3,2203.4-3547.2c0-0.1-0.1-0.1-0.1-0.2c0,0-0.1-0.1-0.1-0.1
		c0,0,0,0-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l-24.4-7.8c-0.2-0.1-0.4,0-0.6,0.1l-18.8,17.5c0,0,0,0,0,0
		c-0.1,0.1-0.1,0.1-0.1,0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0l5.7,25.5c0,0.2,0.2,0.4,0.4,0.4
		l24.4,7.8c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.2-0.1c0,0,0,0,0,0l18.8-17.5c0.2-0.1,0.2-0.4,0.2-0.6L2203.4-3547.2z M2165.7-3512.4
		l-5.4-24.1l23.1,7.4l5.4,24.1L2165.7-3512.4z"/>
	<linearGradient id="SVGID_119_" gradientUnits="userSpaceOnUse" x1="1777.058" y1="-3524.9287" x2="1804.2969" y2="-3524.9287">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_120_" gradientUnits="userSpaceOnUse" x1="1776.6869" y1="-3524.9287" x2="1804.668" y2="-3524.9287">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st159" d="M1804-3515.1c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6s0.5,0,0.6,0.2l7.9,17.1
		C1804.4-3515.4,1804.3-3515.2,1804-3515.1z"/>
	<linearGradient id="SVGID_121_" gradientUnits="userSpaceOnUse" x1="1803.4426" y1="-3531.6428" x2="1814.942" y2="-3531.6428">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_122_" gradientUnits="userSpaceOnUse" x1="1803.0717" y1="-3531.6428" x2="1815.3131" y2="-3531.6428">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st160" d="M1804-3515.1c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C1804.2-3515.2,1804.1-3515.1,1804-3515.1z"/>
	<linearGradient id="SVGID_123_" gradientUnits="userSpaceOnUse" x1="1777.0596" y1="-3541.1123" x2="1807.0619" y2="-3541.1123">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_124_" gradientUnits="userSpaceOnUse" x1="1776.6886" y1="-3541.1123" x2="1807.433" y2="-3541.1123">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st161" d="M1796.2-3532.2c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C1796.3-3532.3,1796.2-3532.2,1796.2-3532.2z"/>
	<g>
		<path class="st120" d="M619.1,152.1"/>
		<path class="st143" d="M140.7,265.7c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
			l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
			C140.9,265.8,140.8,265.8,140.7,265.7z"/>
		<path class="st14" d="M140.3,264.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
			c-0.1,0-0.2-0.1-0.3-0.2L113,271.2c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
		<path class="st14" d="M131.5,292.5c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4c0.1-0.3,0.5-0.5,0.8-0.4
			s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5C131.7,292.6,131.6,292.6,131.5,292.5z"/>
		<linearGradient id="SVGID_125_" gradientUnits="userSpaceOnUse" x1="632.7109" y1="75.6795" x2="662.7132" y2="75.6795">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="0.3721" style="stop-color:#FB8719"/>
			<stop  offset="0.5095" style="stop-color:#FA8420"/>
			<stop  offset="0.608" style="stop-color:#F9802C"/>
			<stop  offset="0.6881" style="stop-color:#F7793D"/>
			<stop  offset="0.7568" style="stop-color:#F47053"/>
			<stop  offset="0.8177" style="stop-color:#F1656E"/>
			<stop  offset="0.8729" style="stop-color:#ED578F"/>
			<stop  offset="0.9237" style="stop-color:#E948B5"/>
			<stop  offset="0.9691" style="stop-color:#E437DE"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<linearGradient id="SVGID_126_" gradientUnits="userSpaceOnUse" x1="632.3399" y1="75.6795" x2="663.0842" y2="75.6795">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st162" d="M651.8,84.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
			c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2L652,84.4C651.9,84.5,651.9,84.6,651.8,84.6z"/>
		<linearGradient id="SVGID_127_" gradientUnits="userSpaceOnUse" x1="632.7092" y1="91.8631" x2="659.9482" y2="91.8631">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st163" d="M659.7,101.7c-0.1,0-0.1,0-0.2,0L641,100c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
			c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
			C660,101.4,659.9,101.6,659.7,101.7z"/>
		<linearGradient id="SVGID_128_" gradientUnits="userSpaceOnUse" x1="659.0939" y1="85.149" x2="670.5933" y2="85.149">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st164" d="M659.7,101.7c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6L669.7,86l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
			c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C659.8,101.6,659.8,101.7,659.7,101.7z"/>
		<path class="st92" d="M1431,129.7c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
			c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19C1431.2,129.7,1431.1,129.7,1431,129.7
			z"/>
		<path class="st92" d="M1437.2,157.4c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
			c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
			C1437.8,157,1437.6,157.3,1437.2,157.4z"/>
		<path class="st92" d="M1437.2,157.4c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
			c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C1437.4,157.3,1437.3,157.4,1437.2,157.4z"/>
		<path class="st93" d="M1074.2,331.2c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
			c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C1074.4,331.3,1074.3,331.2,1074.2,331.2z"
			/>
		<path class="st93" d="M1058.5,352.3c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
			c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1s25.3,4,25.1,4.3
			l-40.7,17.7C1059.1,352.5,1058.8,352.5,1058.5,352.3z"/>
		<path class="st93" d="M1100,333.2c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
			l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	</g>
	<linearGradient id="SVGID_129_" gradientUnits="userSpaceOnUse" x1="3583.571" y1="-1498.0562" x2="3616.6108" y2="-1498.0562">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st165" d="M3612.9-1509.7C3612.9-1509.7,3612.8-1509.7,3612.9-1509.7c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L3612.9-1509.7z M3588-1486.8l-3.6-15.9l15.3,4.9l3.6,15.9L3588-1486.8z"/>
	<linearGradient id="SVGID_130_" gradientUnits="userSpaceOnUse" x1="4136.9287" y1="-1575.7438" x2="4158.665" y2="-1575.7438">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st166" d="M4158.7-1577.9C4158.7-1577.9,4158.7-1577.9,4158.7-1577.9C4158.7-1577.9,4158.7-1578,4158.7-1577.9
		c0-0.1,0-0.1,0-0.1c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.6,2.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		l10.6-2.1c0.1,0,0.2-0.1,0.2-0.2L4158.7-1577.9C4158.7-1577.8,4158.7-1577.8,4158.7-1577.9
		C4158.7-1577.9,4158.7-1577.9,4158.7-1577.9z M4154.7-1567.7l-10,2l3.4-9.8l10-2L4154.7-1567.7z"/>
	<linearGradient id="SVGID_131_" gradientUnits="userSpaceOnUse" x1="4738.5366" y1="-1608.4994" x2="4758.9146" y2="-1608.4994">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st167" d="M4756.6-1615.7C4756.6-1615.7,4756.6-1615.7,4756.6-1615.7C4756.6-1615.7,4756.6-1615.7,4756.6-1615.7
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L4756.6-1615.7z M4741.3-1601.5l-2.2-9.8l9.4,3
		l2.2,9.8L4741.3-1601.5z"/>
	<path class="st151" d="M3734.4-1335.2"/>
	<path class="st152" d="M3367-1261.1c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C3367.1-1261.1,3367.1-1261.1,3367-1261.1z"
		/>
	<path class="st14" d="M3366.8-1261.6c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2s0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M3361.4-1245c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		s0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C3361.6-1245,3361.5-1245,3361.4-1245z"/>
	<linearGradient id="SVGID_132_" gradientUnits="userSpaceOnUse" x1="3742.6116" y1="-1341.4586" x2="3760.6387" y2="-1341.4586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_133_" gradientUnits="userSpaceOnUse" x1="3742.3887" y1="-1341.4586" x2="3760.8616" y2="-1341.4586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st168" d="M3754.1-1336.1c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C3754.2-1336.2,3754.1-1336.1,3754.1-1336.1z"/>
	<linearGradient id="SVGID_134_" gradientUnits="userSpaceOnUse" x1="3742.6106" y1="-1331.7346" x2="3758.9773" y2="-1331.7346">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st169" d="M3758.8-1325.8c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		s0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3s0.3,0,0.3,0.1l4.7,10.3C3759-1326,3759-1325.9,3758.8-1325.8z"/>
	<linearGradient id="SVGID_135_" gradientUnits="userSpaceOnUse" x1="3758.4641" y1="-1335.7688" x2="3765.3735" y2="-1335.7688">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st170" d="M3758.8-1325.8c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C3758.9-1325.9,3758.9-1325.8,3758.8-1325.8z"/>
	<path class="st92" d="M4735.3-1234.8c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4
		C4735.4-1234.9,4735.4-1234.8,4735.3-1234.8z"/>
	<path class="st92" d="M4739-1218.2c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C4739.3-1218.4,4739.2-1218.2,4739-1218.2z"/>
	<path class="st92" d="M4739-1218.2c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C4739.1-1218.2,4739.1-1218.2,4739-1218.2z"/>
	<path class="st93" d="M4485.7-1311.8c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C4485.9-1311.8,4485.8-1311.8,4485.7-1311.8z"
		/>
	<path class="st93" d="M4476.3-1299.1c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		c0.2,0.1,0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C4476.7-1299,4476.4-1299,4476.3-1299.1z"/>
	<path class="st93" d="M4501.2-1310.6c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1
		l-15.5-1.7c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<linearGradient id="SVGID_136_" gradientUnits="userSpaceOnUse" x1="3590.1121" y1="-1232.3169" x2="3623.1516" y2="-1232.3169">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st171" d="M3619.4-1244C3619.4-1244,3619.4-1244,3619.4-1244c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L3619.4-1244z M3594.6-1221l-3.6-15.9l15.3,4.9l3.6,15.9L3594.6-1221z"/>
	<linearGradient id="SVGID_137_" gradientUnits="userSpaceOnUse" x1="4143.4697" y1="-1310.0045" x2="4165.2061" y2="-1310.0045">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st172" d="M4165.2-1312.1C4165.2-1312.2,4165.2-1312.2,4165.2-1312.1C4165.2-1312.2,4165.2-1312.2,4165.2-1312.1
		c0-0.1,0-0.1,0-0.1c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.6,2.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		l10.6-2.1c0.1,0,0.2-0.1,0.2-0.2L4165.2-1312.1C4165.2-1312.1,4165.2-1312.1,4165.2-1312.1
		C4165.2-1312.1,4165.2-1312.1,4165.2-1312.1z M4161.2-1302l-10,2l3.4-9.8l10-2L4161.2-1302z"/>
	<linearGradient id="SVGID_138_" gradientUnits="userSpaceOnUse" x1="4745.0776" y1="-1342.7601" x2="4765.4556" y2="-1342.7601">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st173" d="M4763.1-1349.9C4763.1-1350,4763.1-1350,4763.1-1349.9C4763.1-1350,4763.1-1350,4763.1-1349.9
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L4763.1-1349.9z M4747.8-1335.8l-2.2-9.8l9.4,3
		l2.2,9.8L4747.8-1335.8z"/>
	<linearGradient id="SVGID_139_" gradientUnits="userSpaceOnUse" x1="1034.4697" y1="137.7498" x2="1063.9042" y2="137.7498">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st174" d="M1063.9,134.9C1063.9,134.8,1063.9,134.8,1063.9,134.9c0-0.1,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0,0,0,0,0,0
		l-9.5-11.1c-0.1-0.1-0.2-0.1-0.3-0.1l-14.4,2.9c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0
		c0,0,0,0,0,0l-4.8,14.1c0,0.1,0,0.2,0.1,0.3l9.6,11.1c0,0,0.1,0.1,0.1,0.1c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0l14.4-2.9
		c0.1,0,0.2-0.1,0.3-0.2l4.8-14C1063.9,134.9,1063.9,134.9,1063.9,134.9C1063.9,134.9,1063.9,134.9,1063.9,134.9z M1058.5,148.6
		l-13.6,2.7l4.6-13.3l13.6-2.7L1058.5,148.6z"/>
	<linearGradient id="SVGID_140_" gradientUnits="userSpaceOnUse" x1="412.2302" y1="275.7741" x2="445.2698" y2="275.7741">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st175" d="M441.5,264.1C441.5,264.1,441.5,264.1,441.5,264.1c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L441.5,264.1z M416.7,287.1l-3.6-15.9l15.3,4.9l3.6,15.9L416.7,287.1z"/>
</g>
<g id="Layer_2">
</g>
</svg>
