{"version": 3, "file": "types.js", "sourceRoot": "", "sources": ["../../src/util/types.ts"], "names": [], "mappings": ";;;;;AAAA,qCAIiB;AACjB,4DAA4B;AAE5B;;;;GAIG;AACH,SAAgB,kBAAkB,CAChC,IAAa,EACb,QAAiB,EACjB,YAAyB;IAEzB,IAAI,aAAa,CAAC,IAAI,EAAE,oBAAE,CAAC,SAAS,CAAC,GAAG,GAAG,oBAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QAChE,OAAO,CAAC,QAAQ,CAAC;KAClB;IAED,IAAI,yBAAe,CAAC,IAAI,CAAC,EAAE;QACzB,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;KACpB;IAED,IACE,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;QAClC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAClC;QACA,OAAO,IAAI,CAAC;KACb;IAED,IAAI,mCAAyB,CAAC,IAAI,CAAC,EAAE;QACnC,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;KAC5E;IAED,MAAM,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IAClC,OAAO,CACL,OAAO,KAAK,KAAK,WAAW;QAC5B,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAC/D,CAAC;AACJ,CAAC;AA7BD,gDA6BC;AAED;;;;GAIG;AACH,SAAgB,WAAW,CACzB,WAA2B,EAC3B,IAAa;IAEb,0DAA0D;IAC1D,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE;QAChD,OAAO,QAAQ,CAAC;KACjB;IAED,wEAAwE;IACxE,sEAAsE;IACtE,8BAA8B;IAC9B,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,EAAE;QACnD,sEAAsE;QACtE,uEAAuE;QACvE,WAAW;QACX,MAAM,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,CAAC,CAAgC,CAAC;QACxE,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC3B,OAAO,WAAW,CAChB,WAAW,EACX,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,UAAU,CAAC,CACjD,CAAC;SACH;KACF;IAED,qEAAqE;IACrE,gCAAgC;IAChC,2BAA2B;IAC3B,uCAAuC;IACvC,IACE,IAAI,CAAC,OAAO,EAAE;QACd,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC7B;QACA,OAAO,QAAQ,CAAC;KACjB;IAED,0EAA0E;IAC1E,uEAAuE;IACvE,IACE,IAAI,CAAC,cAAc,EAAE;QACrB,IAAI,CAAC,KAAK;aACP,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;aAC7C,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,EAC5B;QACA,OAAO,QAAQ,CAAC;KACjB;IAED,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;AACxC,CAAC;AAlDD,kCAkDC;AAED;;GAEG;AACH,SAAgB,4BAA4B,CAC1C,OAAuB,EACvB,IAAa;IAEb,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IACjD,MAAM,WAAW,GAAG,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;IAE9D,OAAO,WAAW,IAAI,QAAQ,CAAC;AACjC,CAAC;AARD,oEAQC;AAED;;;GAGG;AACH,SAAgB,cAAc,CAC5B,IAAa,EACb,EACE,UAAU,GAAG,KAAK,EAClB,cAAc,GAAG,IAAI,MACiC,EAAE;IAE1D,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAEjC,IAAI,UAAU,IAAI,KAAK,GAAG,CAAC,oBAAE,CAAC,SAAS,CAAC,GAAG,GAAG,oBAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QACnE,OAAO,IAAI,CAAC;KACb;IAED,IAAI,cAAc,EAAE;QAClB,OAAO,CAAC,KAAK,GAAG,CAAC,oBAAE,CAAC,SAAS,CAAC,IAAI,GAAG,oBAAE,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,CAAC;KACrE;SAAM;QACL,OAAO,CAAC,KAAK,GAAG,oBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;KAC1C;AACH,CAAC;AAlBD,wCAkBC;AAED;;GAEG;AACH,SAAgB,cAAc,CAC5B,OAAuB,EACvB,IAAmB;IAEnB,MAAM,MAAM,GAAG,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IACjD,IAAI,CAAC,MAAM,EAAE;QACX,OAAO,IAAI,CAAC;KACb;IACD,MAAM,YAAY,GAAG,MAAM,CAAC,YAAY,CAAC;IACzC,IAAI,CAAC,YAAY,EAAE;QACjB,OAAO,IAAI,CAAC;KACb;IAED,OAAO,YAAY,CAAC,CAAC,CAAC,CAAC;AACzB,CAAC;AAdD,wCAcC;AAED;;GAEG;AACH,SAAgB,YAAY,CAAC,IAAa;IACxC,IAAI,KAAK,GAAiB,CAAC,CAAC;IAC5B,KAAK,MAAM,CAAC,IAAI,wBAAc,CAAC,IAAI,CAAC,EAAE;QACpC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC;KAClB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAND,oCAMC;AAED;;;GAGG;AACH,SAAgB,aAAa,CAC3B,IAAa,EACb,YAA0B,EAC1B,UAAoB;IAEpB,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;IAEjC,IAAI,UAAU,IAAI,KAAK,GAAG,CAAC,oBAAE,CAAC,SAAS,CAAC,GAAG,GAAG,oBAAE,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE;QACnE,OAAO,IAAI,CAAC;KACb;IAED,OAAO,CAAC,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,CAAC;AACtC,CAAC;AAZD,sCAYC;AAED;;GAEG;AACU,QAAA,mBAAmB,GAAG,CAAC,IAAa,EAAE,UAAmB,EAAE,EAAE;IACxE,IAAI,IAAI,CAAC,MAAM,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,EAAE;QAChE,OAAO,KAAK,CAAC;KACd;IAED,MAAM,gBAAgB,GAAG,CAAC,IAAI,CAAC,CAAC;IAChC,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IAE1C,IAAI,aAAa,KAAK,SAAS,EAAE;QAC/B,gBAAgB,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,CAAC;KACzC;IAED,KAAK,MAAM,QAAQ,IAAI,gBAAgB,EAAE;QACvC,IACE,QAAQ,CAAC,MAAM,KAAK,SAAS;YAC7B,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU,CAAC,MAAM,CAAC,IAAI,EAC/C;YACA,OAAO,IAAI,CAAC;SACb;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC,CAAC"}