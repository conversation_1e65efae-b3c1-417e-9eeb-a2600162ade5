{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ThisGo\\bluebell_frontend\\src\\main.js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\main.js", "mtime": 1600529066000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js", "mtime": 1756017426480}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgQXBwIGZyb20gJy4vQXBwLnZ1ZSc7CmltcG9ydCByb3V0ZXIgZnJvbSAnLi9yb3V0ZXInOwppbXBvcnQgc3RvcmUgZnJvbSAnLi9zdG9yZSc7CmltcG9ydCBheGlvcyBmcm9tICcuL3NlcnZpY2UvYXBpJzsKVnVlLnByb3RvdHlwZS4kYXhpb3MgPSBheGlvczsKVnVlLmNvbmZpZy5wcm9kdWN0aW9uVGlwID0gZmFsc2U7CnJvdXRlci5iZWZvcmVFYWNoKCh0bywgZnJvbSwgbmV4dCkgPT4gewogIGNvbnNvbGUubG9nKHRvKTsKICBjb25zb2xlLmxvZyhmcm9tKTsKICBpZiAodG8ubWV0YS5yZXF1aXJlQXV0aCkgewogICAgLy8g5Yik5pat6K+l6Lev55Sx5piv5ZCm6ZyA6KaB55m75b2V5p2D6ZmQCiAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oImxvZ2luUmVzdWx0IikpIHsKICAgICAgLy/liKTmlq3mnKzlnLDmmK/lkKblrZjlnKhhY2Nlc3NfdG9rZW4KICAgICAgbmV4dCgpOwogICAgfSBlbHNlIHsKICAgICAgaWYgKHRvLnBhdGggPT09ICcvbG9naW4nKSB7CiAgICAgICAgbmV4dCgpOwogICAgICB9IGVsc2UgewogICAgICAgIG5leHQoewogICAgICAgICAgcGF0aDogJy9sb2dpbicKICAgICAgICB9KTsKICAgICAgfQogICAgfQogIH0gZWxzZSB7CiAgICBuZXh0KCk7CiAgfQogIC8q5aaC5p6c5pys5ZywIOWtmOWcqCB0b2tlbiDliJkg5LiN5YWB6K6455u05o6l6Lez6L2s5YiwIOeZu+W9lemhtemdoiovCiAgaWYgKHRvLmZ1bGxQYXRoID09ICIvbG9naW4iKSB7CiAgICBpZiAobG9jYWxTdG9yYWdlLmdldEl0ZW0oImxvZ2luUmVzdWx0IikpIHsKICAgICAgbmV4dCh7CiAgICAgICAgcGF0aDogZnJvbS5mdWxsUGF0aAogICAgICB9KTsKICAgIH0gZWxzZSB7CiAgICAgIG5leHQoKTsKICAgIH0KICB9Cn0pOwpuZXcgVnVlKHsKICByb3V0ZXIsCiAgc3RvcmUsCiAgcmVuZGVyOiBoID0+IGgoQXBwKQp9KS4kbW91bnQoJyNhcHAnKTs="}, {"version": 3, "names": ["<PERSON><PERSON>", "App", "router", "store", "axios", "prototype", "$axios", "config", "productionTip", "beforeEach", "to", "from", "next", "console", "log", "meta", "requireAuth", "localStorage", "getItem", "path", "fullPath", "render", "h", "$mount"], "sources": ["E:/ThisGo/bluebell_frontend/src/main.js"], "sourcesContent": ["import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport axios from './service/api'\n\nVue.prototype.$axios = axios;\nVue.config.productionTip = false\n\n\nrouter.beforeEach((to, from, next) => {\n  console.log(to);\n  console.log(from);\n  if (to.meta.requireAuth) { // 判断该路由是否需要登录权限\n    if (localStorage.getItem(\"loginResult\")) { //判断本地是否存在access_token\n      next();\n    } else {\n      if (to.path === '/login') {\n        next();\n      } else {\n        next({\n          path: '/login'\n        })\n      }\n    }\n  }\n  else {\n    next();\n  }\n  /*如果本地 存在 token 则 不允许直接跳转到 登录页面*/\n  if (to.fullPath == \"/login\") {\n    if (localStorage.getItem(\"loginResult\")) {\n      next({\n        path: from.fullPath\n      });\n    } else {\n      next();\n    }\n  }\n})\n\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,GAAG,MAAM,WAAW;AAC3B,OAAOC,MAAM,MAAM,UAAU;AAC7B,OAAOC,KAAK,MAAM,SAAS;AAC3B,OAAOC,KAAK,MAAM,eAAe;AAEjCJ,GAAG,CAACK,SAAS,CAACC,MAAM,GAAGF,KAAK;AAC5BJ,GAAG,CAACO,MAAM,CAACC,aAAa,GAAG,KAAK;AAGhCN,MAAM,CAACO,UAAU,CAAC,CAACC,EAAE,EAAEC,IAAI,EAAEC,IAAI,KAAK;EACpCC,OAAO,CAACC,GAAG,CAACJ,EAAE,CAAC;EACfG,OAAO,CAACC,GAAG,CAACH,IAAI,CAAC;EACjB,IAAID,EAAE,CAACK,IAAI,CAACC,WAAW,EAAE;IAAE;IACzB,IAAIC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MAAE;MACzCN,IAAI,CAAC,CAAC;IACR,CAAC,MAAM;MACL,IAAIF,EAAE,CAACS,IAAI,KAAK,QAAQ,EAAE;QACxBP,IAAI,CAAC,CAAC;MACR,CAAC,MAAM;QACLA,IAAI,CAAC;UACHO,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;IACF;EACF,CAAC,MACI;IACHP,IAAI,CAAC,CAAC;EACR;EACA;EACA,IAAIF,EAAE,CAACU,QAAQ,IAAI,QAAQ,EAAE;IAC3B,IAAIH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,EAAE;MACvCN,IAAI,CAAC;QACHO,IAAI,EAAER,IAAI,CAACS;MACb,CAAC,CAAC;IACJ,CAAC,MAAM;MACLR,IAAI,CAAC,CAAC;IACR;EACF;AACF,CAAC,CAAC;AAEF,IAAIZ,GAAG,CAAC;EACNE,MAAM;EACNC,KAAK;EACLkB,MAAM,EAAEC,CAAC,IAAIA,CAAC,CAACrB,GAAG;AACpB,CAAC,CAAC,CAACsB,MAAM,CAAC,MAAM,CAAC", "ignoreList": []}]}