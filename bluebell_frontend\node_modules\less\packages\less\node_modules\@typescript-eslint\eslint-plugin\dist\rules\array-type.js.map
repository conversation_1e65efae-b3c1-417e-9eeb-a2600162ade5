{"version": 3, "file": "array-type.js", "sourceRoot": "", "sources": ["../../src/rules/array-type.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAI+C;AAC/C,8CAAgC;AAEhC;;;GAGG;AACH,SAAS,YAAY,CAAC,IAAmB;IACvC,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,mCAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,mCAAc,CAAC,YAAY,CAAC;QACjC,KAAK,mCAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,mCAAc,CAAC,cAAc,CAAC;QACnC,KAAK,mCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,mCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,mCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,mCAAc,CAAC,eAAe,CAAC;QACpC,KAAK,mCAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,mCAAc,CAAC,aAAa,CAAC;QAClC,KAAK,mCAAc,CAAC,aAAa,CAAC;QAClC,KAAK,mCAAc,CAAC,WAAW,CAAC;QAChC,KAAK,mCAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,mCAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,mCAAc,CAAC,eAAe;YACjC,OAAO,IAAI,CAAC;QACd,KAAK,mCAAc,CAAC,eAAe;YACjC,IACE,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,UAAU;gBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,EAC9B;gBACA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;oBACxB,OAAO,IAAI,CAAC;iBACb;gBACD,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3C,OAAO,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;iBACpD;aACF;iBAAM;gBACL,IAAI,IAAI,CAAC,cAAc,EAAE;oBACvB,OAAO,KAAK,CAAC;iBACd;gBACD,OAAO,YAAY,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;aACpC;YACD,OAAO,KAAK,CAAC;QACf;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AAED;;;GAGG;AACH,SAAS,oBAAoB,CAAC,IAAmB;IAC/C,QAAQ,IAAI,CAAC,IAAI,EAAE;QACjB,KAAK,mCAAc,CAAC,eAAe;YACjC,OAAO,oBAAoB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC7C,KAAK,mCAAc,CAAC,WAAW,CAAC;QAChC,KAAK,mCAAc,CAAC,cAAc,CAAC;QACnC,KAAK,mCAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,mCAAc,CAAC,cAAc,CAAC;QACnC,KAAK,mCAAc,CAAC,WAAW;YAC7B,OAAO,IAAI,CAAC;QACd,KAAK,mCAAc,CAAC,UAAU;YAC5B,OAAO,IAAI,CAAC,IAAI,KAAK,eAAe,CAAC;QACvC;YACE,OAAO,KAAK,CAAC;KAChB;AACH,CAAC;AASD,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,YAAY;IAClB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,sDAAsD;YACnE,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,OAAO;SACrB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,kBAAkB,EAChB,4EAA4E;YAC9E,wBAAwB,EACtB,iGAAiG;YACnG,gBAAgB,EACd,4EAA4E;YAC9E,sBAAsB,EACpB,6FAA6F;SAChG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,cAAc,CAAC;aAC3C;SACF;KACF;IACD,cAAc,EAAE,CAAC,OAAO,CAAC;IACzB,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C;;;WAGG;QACH,SAAS,uBAAuB,CAAC,IAAmB;YAClD,MAAM,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;YAClD,IAAI,CAAC,SAAS,EAAE;gBACd,OAAO,KAAK,CAAC;aACd;YAED,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE;gBAC1C,OAAO,KAAK,CAAC;aACd;YAED,OAAO,SAAS,CAAC,IAAI,KAAK,oCAAe,CAAC,UAAU,CAAC;QACvD,CAAC;QAED;;WAEG;QACH,SAAS,cAAc,CAAC,IAAmB;YACzC,IAAI,IAAI,EAAE;gBACR,IAAI,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,mBAAmB,EAAE;oBACpD,OAAO,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;iBAC5C;gBACD,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE;oBACtB,OAAO,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBACjC;aACF;YACD,OAAO,GAAG,CAAC;QACb,CAAC;QAED,OAAO;YACL,WAAW,CAAC,IAAI;gBACd,IACE,MAAM,KAAK,OAAO;oBAClB,CAAC,MAAM,KAAK,cAAc,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,EAC7D;oBACA,OAAO;iBACR;gBACD,MAAM,SAAS,GACb,MAAM,KAAK,SAAS;oBAClB,CAAC,CAAC,oBAAoB;oBACtB,CAAC,CAAC,0BAA0B,CAAC;gBAEjC,MAAM,UAAU,GACd,IAAI,CAAC,MAAM;oBACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc;oBAClD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,UAAU,CAAC;gBACtC,MAAM,UAAU,GAAG,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAO,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEpD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,MAAO,CAAC,CAAC,CAAC,IAAI;oBACtC,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC;qBACvC;oBACD,GAAG,CAAC,KAAK;wBACP,MAAM,SAAS,GAAG,uBAAuB,CAAC,IAAI,CAAC,CAAC;wBAChD,MAAM,KAAK,GAAG;4BACZ,KAAK,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC;4BAC/D,KAAK,CAAC,gBAAgB,CACpB,IAAI,EACJ,GAAG,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,QAAQ,CAC/D;yBACF,CAAC;wBACF,IAAI,UAAU,EAAE;4BACd,4CAA4C;4BAC5C,KAAK,CAAC,OAAO,CACX,KAAK,CAAC,WAAW,CAAC;gCAChB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;gCACnB,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,WAAW,CAAC,MAAM;6BACzC,CAAC,CACH,CAAC;yBACH;wBAED,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,mCAAc,CAAC,mBAAmB,EAAE;4BAChE,MAAM,KAAK,GAAG,UAAU,CAAC,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BACzD,MAAM,IAAI,GAAG,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;4BACvD,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;gCACnB,OAAO,IAAI,CAAC;6BACb;4BAED,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;4BAChC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;yBAChC;wBAED,OAAO,KAAK,CAAC;oBACf,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YACD,eAAe,CAAC,IAA8B;gBAC5C,IACE,MAAM,KAAK,SAAS;oBACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,UAAU,EAChD;oBACA,OAAO;iBACR;gBACD,IAAI,CAAC,CAAC,OAAO,EAAE,eAAe,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;oBAC5D,OAAO;iBACR;gBAED,MAAM,SAAS,GACb,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,kBAAkB,CAAC,CAAC,CAAC,wBAAwB,CAAC;gBACrE,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,eAAe,CAAC;gBAC1D,MAAM,cAAc,GAAG,UAAU,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC;gBAErD,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;gBAErE,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC1C,wBAAwB;oBACxB,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS;wBACT,IAAI,EAAE;4BACJ,IAAI,EAAE,KAAK;yBACZ;wBACD,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,GAAG,cAAc,OAAO,CAAC,CAAC;wBAC3D,CAAC;qBACF,CAAC,CAAC;oBACH,OAAO;iBACR;gBAED,IACE,UAAU,CAAC,MAAM,KAAK,CAAC;oBACvB,CAAC,MAAM,KAAK,cAAc,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,EAC3D;oBACA,OAAO;iBACR;gBAED,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC;gBAC3B,MAAM,MAAM,GAAG,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAE1C,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS;oBACT,IAAI,EAAE;wBACJ,IAAI,EAAE,cAAc,CAAC,IAAI,CAAC;qBAC3B;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO;4BACL,KAAK,CAAC,gBAAgB,CACpB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9B,GAAG,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CACxC;4BACD,KAAK,CAAC,gBAAgB,CACpB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAC9B,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CACtB;yBACF,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}