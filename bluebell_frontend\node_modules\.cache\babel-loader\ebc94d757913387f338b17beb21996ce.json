{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\components\\HeadBar.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\components\\HeadBar.vue", "mtime": 1596349902000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiSGVhZEJhciIsCiAgY3JlYXRlZCgpIHsKICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgiaW5pdCIpOwogIH0sCiAgY29tcHV0ZWQ6IHsKICAgIGlzTG9naW4oKSB7CiAgICAgIHJldHVybiB0aGlzLiRzdG9yZS5nZXR0ZXJzLmlzTG9naW47CiAgICB9LAogICAgY3VyclVzZXJuYW1lKCkgewogICAgICBjb25zb2xlLmxvZyh0aGlzLiRzdG9yZS5nZXR0ZXJzLnVzZXJuYW1lKTsKICAgICAgcmV0dXJuIHRoaXMuJHN0b3JlLmdldHRlcnMudXNlcm5hbWU7CiAgICB9CiAgfSwKICBtZXRob2RzOiB7CiAgICBnb0luZGV4KCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogIkhvbWUiCiAgICAgIH0pOwogICAgfSwKICAgIGdvTG9naW4oKSB7CiAgICAgIHRoaXMuJHJvdXRlci5wdXNoKHsKICAgICAgICBuYW1lOiAiTG9naW4iCiAgICAgIH0pOwogICAgfSwKICAgIGdvU2lnblVwKCkgewogICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgbmFtZTogIlNpZ25VcCIKICAgICAgfSk7CiAgICB9LAogICAgZ29Mb2dvdXQoKSB7CiAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgibG9nb3V0Iik7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["name", "created", "$store", "commit", "computed", "is<PERSON>ogin", "getters", "currUsername", "console", "log", "username", "methods", "goIndex", "$router", "push", "goLogin", "goSignUp", "goLogout"], "sources": ["src/components/HeadBar.vue"], "sourcesContent": ["<template>\n  <header class=\"header\">\n      <span class=\"logo\" @click=\"goIndex\">bluebell</span>\n    <div class=\"search\">\n      <label class=\"s-logo\"></label>\n      <input type=\"text\" class=\"s-input\" placeholder=\"搜索\" />\n    </div>\n    <div class=\"btns\">\n      <div v-show=\"!isLogin\">\n        <a class=\"login-btn\" @click=\"goLogin\">登录</a>\n        <a class=\"login-btn\" @click=\"goSignUp\">注册</a>\n      </div>\n      <div class=\"user-box\" v-show=\"isLogin\">\n        <span class=\"user\">{{ currUsername }}</span>\n        <div class=\"dropdown-content\">\n          <a @click=\"goLogout\">登出</a>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nexport default {\n  name: \"HeadBar\",\n  created(){\n    this.$store.commit(\"init\");\n  },\n  computed: {\n    isLogin() {\n      return this.$store.getters.isLogin;\n    },\n    currUsername(){\n      console.log(this.$store.getters.username);\n      return this.$store.getters.username;\n    }\n  },\n  methods: {\n    goIndex(){\n      this.$router.push({ name: \"Home\" });\n    },\n    goLogin() {\n      this.$router.push({ name: \"Login\" });\n    },\n    goSignUp() {\n      this.$router.push({ name: \"SignUp\" });\n    },\n    goLogout(){\n      this.$store.commit(\"logout\");\n    }\n  }\n};\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped lang=\"less\">\n.header {\n  width: 100%;\n  height: 48px;\n  position: fixed;\n  background: #ffffff;\n  display: flex;\n  display: -webkit-flex;\n  align-items: center;\n  top: 0;\n  z-index: 1;\n  .logo {\n    margin-left: 10px;\n    height: 32px;\n    background: url(\"../assets/images/logo.png\") no-repeat;\n    background-size: 32px 32px;\n    background-position: left center;\n    padding-left: 35px;\n    line-height: 32px;\n    flex-grow: 0;\n    margin-right: 16px;\n    cursor: pointer;\n  }\n  .search {\n    flex-grow: 1;\n    margin: 0 auto;\n    max-width: 690px;\n    position: relative;\n    display: flex;\n    display: -webkit-flex;\n    .s-logo {\n      width: 18px;\n      height: 18px;\n      background: url(\"../assets/images/search.png\") no-repeat;\n      background-size: cover;\n      display: inline-block;\n      position: absolute;\n      top: 50%;\n      margin-top: -9px;\n      left: 15px;\n    }\n    .s-input {\n      flex-grow: 1;\n      -webkit-appearance: none;\n      appearance: none;\n      background-color: #f6f7f8;\n      border-radius: 4px;\n      border: 1px solid #edeff1;\n      box-shadow: none;\n      color: #c1c1c1;\n      display: block;\n      height: 36px;\n      outline: none;\n      padding: 0 16px 0 40px;\n      width: 100%;\n    }\n  }\n  .btns {\n    flex-grow: 0;\n    margin-left: 16px;\n    margin-right: 10px;\n    display: flex;\n    display: -webkit-flex;\n    align-items: center;\n    .login-btn {\n      border: 1px solid transparent;\n      border-radius: 4px;\n      box-sizing: border-box;\n      text-align: center;\n      letter-spacing: 1px;\n      text-decoration: none;\n      font-size: 12px;\n      font-weight: 700;\n      letter-spacing: 0.5px;\n      line-height: 24px;\n      text-transform: uppercase;\n      padding: 3px 16px;\n      border-color: #0079d3;\n      color: #0079d3;\n      fill: #0079d3;\n      display: inline-block;\n      cursor: pointer;\n      &:nth-child(1) {\n        margin-right: 5px;\n      }\n      &:nth-child(2) {\n        margin-right: 10px;\n      }\n    }\n    .user {\n      width: auto;\n      height: 24px;\n      background: url(\"../assets/images/avatar.png\") no-repeat;\n      background-size: 24px 24px;\n      background-position: left center;\n      padding-left: 28px;\n      display: flex;\n      display: -webkit-flex;\n      align-items: center;\n      cursor: pointer;\n      padding: 12px 12px 12px 28px;\n      &::after {\n        content: \"\";\n        width: 0;\n        height: 0;\n        border-top: 5px solid #878a8c;\n        border-right: 5px solid transparent;\n        border-bottom: 5px solid transparent;\n        border-left: 5px solid transparent;\n        margin-top: 5px;\n        margin-left: 10px;\n      }\n    }\n    .dropdown-content {\n      display: none;\n      position: absolute;\n      background-color: #f9f9f9;\n      min-width: 160px;\n      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n      a {\n        color: black;\n        padding: 12px 16px;\n        text-decoration: none;\n        display: block;\n        cursor: pointer;\n      }\n      a:hover {background-color: #f1f1f1}\n    }\n    .user-box:hover .dropdown-content {\n      display: block;\n    }\n  }\n  \n}\n</style>\n"], "mappings": ";AAuBA;EACAA,IAAA;EACAC,QAAA;IACA,KAAAC,MAAA,CAAAC,MAAA;EACA;EACAC,QAAA;IACAC,QAAA;MACA,YAAAH,MAAA,CAAAI,OAAA,CAAAD,OAAA;IACA;IACAE,aAAA;MACAC,OAAA,CAAAC,GAAA,MAAAP,MAAA,CAAAI,OAAA,CAAAI,QAAA;MACA,YAAAR,MAAA,CAAAI,OAAA,CAAAI,QAAA;IACA;EACA;EACAC,OAAA;IACAC,QAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAd,IAAA;MAAA;IACA;IACAe,QAAA;MACA,KAAAF,OAAA,CAAAC,IAAA;QAAAd,IAAA;MAAA;IACA;IACAgB,SAAA;MACA,KAAAH,OAAA,CAAAC,IAAA;QAAAd,IAAA;MAAA;IACA;IACAiB,SAAA;MACA,KAAAf,MAAA,CAAAC,MAAA;IACA;EACA;AACA", "ignoreList": []}]}