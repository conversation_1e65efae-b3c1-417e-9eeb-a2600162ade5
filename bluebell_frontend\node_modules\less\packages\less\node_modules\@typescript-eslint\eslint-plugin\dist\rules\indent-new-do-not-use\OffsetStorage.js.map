{"version": 3, "file": "OffsetStorage.js", "sourceRoot": "", "sources": ["../../../src/rules/indent-new-do-not-use/OffsetStorage.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,kGAAkG;;AAGlG,yDAAsE;AAGtE;;GAEG;AACH,MAAa,aAAa;IAQxB;;;;OAIG;IACH,YAAY,SAAoB,EAAE,UAAkB,EAAE,UAAkB;QACtE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAE7B,IAAI,CAAC,IAAI,GAAG,IAAI,mCAAgB,EAAE,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC;QAE7D,IAAI,CAAC,iBAAiB,GAAG,IAAI,OAAO,EAAE,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG,IAAI,OAAO,EAAE,CAAC;QACxC,IAAI,CAAC,aAAa,GAAG,IAAI,OAAO,EAAE,CAAC;IACrC,CAAC;IAEO,mBAAmB,CAAC,KAAqB;QAC/C,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;IAChD,CAAC;IAED;;;;;;OAMG;IACI,aAAa,CAClB,SAAyB,EACzB,WAA2B;QAE3B;;;;;;WAMG;QACH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;IACrD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuDG;IACI,gBAAgB,CACrB,KAAqB,EACrB,SAAgC,EAChC,MAAc;QAEd,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACzD,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACI,iBAAiB,CACtB,KAAuB,EACvB,SAAgC,EAChC,SAAiB,CAAC,EAClB,QAAiB,KAAK;QAEtB;;;;;;;;;;;;;WAaG;QAEH,MAAM,kBAAkB,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;QAE9D,MAAM,oBAAoB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;QAE9D,MAAM,kBAAkB,GACtB,SAAS;YACT,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC;YAC9B,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC;QACjC,kFAAkF;QAClF,MAAM,mBAAmB,GAAG,kBAAkB;YAC5C,CAAC,CAAC,IAAI,CAAC,mBAAmB,CAAC,SAAU,CAAC;YACtC,CAAC,CAAC,IAAI,CAAC;QAET,+DAA+D;QAC/D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;QAE9C,iDAAiD;QACjD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;QAE/C;;;WAGG;QACH,IAAI,kBAAkB,EAAE;YACtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,mBAAoB,CAAC,CAAC;YAC5D,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,SAAU,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,kBAAkB,CAAC,CAAC;SAC3D;QAED;;;WAGG;QACH,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC;IACnD,CAAC;IAED;;;OAGG;IACI,gBAAgB,CAAC,KAAqB;QAC3C,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YACvC,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBACjC;;;mBAGG;gBACH,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACzB,KAAK,EACL,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,CACrC,CAAC;aACH;iBAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;gBAC5C,MAAM,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;gBAEtD,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACzB,KAAK;gBAEL,6CAA6C;gBAC7C,IAAI,CAAC,gBAAgB,CACnB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAC/C;oBACC,8EAA8E;oBAC9E,IAAI,CAAC,UAAU,CAAC,MAAM,CACpB,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM;wBACzB,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAClE,CACJ,CAAC;aACH;iBAAM;gBACL,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC;gBACnD,MAAM,MAAM,GACV,UAAU,CAAC,IAAI;oBACf,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI;oBACvD,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;oBAC7B,CAAC,UAAU,CAAC,KAAK;oBACf,CAAC,CAAC,CAAC;oBACH,CAAC,CAAC,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,CAAC;gBAE1C,IAAI,CAAC,kBAAkB,CAAC,GAAG,CACzB,KAAK,EACL,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;oBAC7D,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,CACjC,CAAC;aACH;SACF;QAED,OAAO,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACH,WAAW,CAAC,KAAqB;QAC/B,IAAI,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;YAC5C,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;SAC/B;IACH,CAAC;IAQD,kBAAkB,CAAC,KAAqB;QACtC,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC;IAC9C,CAAC;CACF;AA1QD,sCA0QC"}