
@a0: 123;
@a1: foo;
@a2: ~".foo";
@a4: ~"#foo";

.@{a0} {
    a: 0;
}

.@{a1} {
    a: 1;
}

@{a2} {
    a: 2;
}

#@{a1} {
    a: 3;
}

@{a4} {
    a: 4;
}

mi-test-a {
    .123;
    .foo;
    #foo;
}

.b .bb {
    &.@{a1}-xxx .yyy-@{a1}@{a4} {
        & @{a2}.bbb {
            b: 1;
        }
    }
}

mi-test-b {
    .b.bb.foo-xxx.yyy-foo#foo.foo.bbb;
}

@c1: @a1;
@c2: bar;
@c3: baz;

#@{c1}-foo {
    > .@{c2} {
        .@{c3} {
            c: c;
        }
    }
}

mi-test-c {
    &-1 {#foo-foo;}
    &-2 {#foo-foo > .bar;}
    &-3 {#foo-foo > .bar.baz;}
}

.Person(@name, @gender_) {
    .@{name} {
        @gender: @gender_;
        .sayGender() {
            gender: @gender;
        }
    }
}

mi-test-d {
    .Person(person, "Male");
    .person.sayGender();
}
