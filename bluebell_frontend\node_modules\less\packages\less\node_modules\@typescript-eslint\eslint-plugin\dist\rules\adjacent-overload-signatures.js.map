{"version": 3, "file": "adjacent-overload-signatures.js", "sourceRoot": "", "sources": ["../../src/rules/adjacent-overload-signatures.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAUhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,8BAA8B;IACpC,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,8CAA8C;YAC3D,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,OAAO;SACrB;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE;YACR,iBAAiB,EAAE,+CAA+C;SACnE;KACF;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C;;;;WAIG;QACH,SAAS,aAAa,CAAC,MAAqB;YAC1C,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,IAAI,CAAC;aACb;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE;gBACnB,KAAK,mCAAc,CAAC,wBAAwB,CAAC;gBAC7C,KAAK,mCAAc,CAAC,sBAAsB,CAAC,CAAC;oBAC1C,yCAAyC;oBACzC,uCAAuC;oBACvC,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;wBACvB,OAAO,IAAI,CAAC;qBACb;oBAED,OAAO,aAAa,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;iBAC1C;gBACD,KAAK,mCAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,mCAAc,CAAC,mBAAmB;oBACrC,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;gBACrC,KAAK,mCAAc,CAAC,iBAAiB;oBACnC,OAAO,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;gBAClD,KAAK,mCAAc,CAAC,0BAA0B;oBAC5C,OAAO,MAAM,CAAC;gBAChB,KAAK,mCAAc,CAAC,+BAA+B;oBACjD,OAAO,KAAK,CAAC;gBACf,KAAK,mCAAc,CAAC,gBAAgB;oBAClC,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;aAC1D;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAMD,SAAS,YAAY,CAAC,OAAe,EAAE,OAAsB;YAC3D,OAAO,CACL,CAAC,CAAC,OAAO;gBACT,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;gBAC7B,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,CAClC,CAAC;QACJ,CAAC;QAED,SAAS,UAAU,CAAC,IAAc;YAChC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,mCAAc,CAAC,SAAS,CAAC;gBAC9B,KAAK,mCAAc,CAAC,OAAO,CAAC;gBAC5B,KAAK,mCAAc,CAAC,aAAa,CAAC;gBAClC,KAAK,mCAAc,CAAC,eAAe;oBACjC,OAAO,IAAI,CAAC,IAAI,CAAC;gBAEnB,KAAK,mCAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,OAAO,CAAC;aACvB;YAED,OAAO,EAAE,CAAC;QACZ,CAAC;QAED;;;WAGG;QACH,SAAS,2BAA2B,CAAC,IAAc;YACjD,MAAM,OAAO,GAAG,UAAU,CAAC,IAAI,CAAC,CAAC;YAEjC,IAAI,OAAO,EAAE;gBACX,IAAI,UAAU,GAAkB,IAAI,CAAC;gBACrC,MAAM,WAAW,GAAa,EAAE,CAAC;gBAEjC,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACvB,MAAM,IAAI,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;oBACnC,IAAI,IAAI,KAAK,IAAI,EAAE;wBACjB,UAAU,GAAG,IAAI,CAAC;wBAClB,OAAO;qBACR;oBACD,MAAM,MAAM,GAAG;wBACb,IAAI;wBACJ,MAAM,EAAE,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,MAAM,CAAC,MAAM;qBAC9C,CAAC;oBAEF,MAAM,KAAK,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAC/C,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,CACjC,CAAC;oBACF,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,CAAC,EAAE;wBACnD,OAAO,CAAC,MAAM,CAAC;4BACb,IAAI,EAAE,MAAM;4BACZ,SAAS,EAAE,mBAAmB;4BAC9B,IAAI,EAAE;gCACJ,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,IAAI;6BACrD;yBACF,CAAC,CAAC;qBACJ;yBAAM,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;wBACvB,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;qBAC1B;oBAED,UAAU,GAAG,MAAM,CAAC;gBACtB,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,SAAS,EAAE,2BAA2B;YACtC,OAAO,EAAE,2BAA2B;YACpC,aAAa,EAAE,2BAA2B;YAC1C,aAAa,EAAE,2BAA2B;YAC1C,eAAe,EAAE,2BAA2B;SAC7C,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}