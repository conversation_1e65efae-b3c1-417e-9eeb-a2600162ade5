.add(@a, @b) {
  @r: @a + @b;
}
.foo {
  width: .add(10px, 10px)[];
  bar: @return[];
}

@return: {
  single: val;
}

// Issue #3405
#lookup {
  @prop: test;
}

.mix (@var) {
  width: @var;
}

.bar {
  .mix(#lookup[@prop]);
}

// Issue #3406
.mix2 (@n) {
  value: @n;
}
#lookup2 {
  @var: .mix2(lookup);
}
.example {
  // #lookup[@var](); -- fails, need the following alias
  @dr: #lookup2[@var];
  @dr();
}