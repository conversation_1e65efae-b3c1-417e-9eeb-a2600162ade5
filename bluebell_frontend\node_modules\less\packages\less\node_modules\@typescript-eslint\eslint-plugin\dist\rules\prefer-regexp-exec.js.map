{"version": 3, "file": "prefer-regexp-exec.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-regexp-exec.ts"], "names": [], "mappings": ";;AACA,kCAAqE;AACrE,+CAA8C;AAE9C,kBAAe,iBAAU,CAAC;IACxB,IAAI,EAAE,oBAAoB;IAC1B,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,wEAAwE;YAC1E,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,yBAAyB,EAAE,yCAAyC;SACrE;QACD,MAAM,EAAE,EAAE;KACX;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,OAAO,GAAG,wBAAiB,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,OAAO,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAErD;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAmB;YACvC,MAAM,UAAU,GAAG,WAAW,CAAC,iBAAiB,CAC9C,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CACxC,CAAC;YACF,OAAO,kBAAW,CAAC,WAAW,EAAE,UAAU,CAAC,KAAK,QAAQ,CAAC;QAC3D,CAAC;QAED,OAAO;YACL,qGAAqG,CACnG,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAiC,CAAC;gBACxD,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,SAAS,GAAG,6BAAc,CAAC,GAAG,EAAE,WAAW,CAAC,CAAC;gBAEnD,qDAAqD;gBACrD,IACE,SAAS;oBACT,SAAS,CAAC,KAAK,YAAY,MAAM;oBACjC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,EACnC;oBACA,OAAO;iBACR;gBAED,IAAI,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBAC7B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,QAAQ;wBACd,SAAS,EAAE,2BAA2B;qBACvC,CAAC,CAAC;oBACH,OAAO;iBACR;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}