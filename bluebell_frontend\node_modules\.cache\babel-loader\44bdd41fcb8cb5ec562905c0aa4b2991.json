{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ThisGo\\bluebell_frontend\\src\\store\\index.js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\store\\index.js", "mtime": 1598765102000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js", "mtime": 1756017426480}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IFZ1ZSBmcm9tICd2dWUnOwppbXBvcnQgVnVleCBmcm9tICd2dWV4JzsKVnVlLnVzZShWdWV4KTsKY29uc3QgZGVmYXVsdExvZ2luUmVzdWx0ID0gewogIHRva2VuOiBudWxsLAogIHVzZXJfaWQ6IG51bGwsCiAgdXNlcl9uYW1lOiBudWxsCn07CmV4cG9ydCBkZWZhdWx0IG5ldyBWdWV4LlN0b3JlKHsKICBzdGF0ZTogewogICAgaXNMb2dpbjogZmFsc2UsCiAgICBsb2dpblJlc3VsdDogZGVmYXVsdExvZ2luUmVzdWx0CiAgfSwKICBtdXRhdGlvbnM6IHsKICAgIGluaXQoc3RhdGUpIHsKICAgICAgbGV0IGxvZ2luUmVzdWx0ID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgibG9naW5SZXN1bHQiKSk7CiAgICAgIGNvbnNvbGUubG9nKGxvY2FsU3RvcmFnZS5nZXRJdGVtKCJsb2dpblJlc3VsdCIpKTsKICAgICAgaWYgKGxvZ2luUmVzdWx0ICE9IG51bGwpIHsKICAgICAgICBzdGF0ZS5sb2dpblJlc3VsdCA9IGxvZ2luUmVzdWx0OwogICAgICB9CiAgICB9LAogICAgbG9naW4oc3RhdGUsIGxvZ2luUmVzdWx0KSB7CiAgICAgIHN0YXRlLmxvZ2luUmVzdWx0ID0gbG9naW5SZXN1bHQ7CiAgICB9LAogICAgbG9nb3V0KHN0YXRlKSB7CiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCJsb2dpblJlc3VsdCIpOwogICAgICBzdGF0ZS5sb2dpblJlc3VsdCA9IGRlZmF1bHRMb2dpblJlc3VsdDsKICAgIH0KICB9LAogIGFjdGlvbnM6IHt9LAogIGdldHRlcnM6IHsKICAgIGlzTG9naW46IHN0YXRlID0+IHN0YXRlLmxvZ2luUmVzdWx0LnVzZXJfaWQgIT09IG51bGwsCiAgICB1c2VySUQ6IHN0YXRlID0+IHN0YXRlLmxvZ2luUmVzdWx0LnVzZXJfaWQsCiAgICB1c2VybmFtZTogc3RhdGUgPT4gc3RhdGUubG9naW5SZXN1bHQudXNlcl9uYW1lLAogICAgYWNjZXNzVG9rZW46IHN0YXRlID0+IHN0YXRlLmxvZ2luUmVzdWx0LnRva2VuCiAgfQp9KTs="}, {"version": 3, "names": ["<PERSON><PERSON>", "Vuex", "use", "defaultLoginResult", "token", "user_id", "user_name", "Store", "state", "is<PERSON>ogin", "loginResult", "mutations", "init", "JSON", "parse", "localStorage", "getItem", "console", "log", "login", "logout", "removeItem", "actions", "getters", "userID", "username", "accessToken"], "sources": ["E:/ThisGo/bluebell_frontend/src/store/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport Vuex from 'vuex'\n\nVue.use(Vuex)\n\nconst defaultLoginResult = {\n  token:null,\n  user_id:null,\n  user_name:null,\n}\n\nexport default new Vuex.Store({\n  state: {\n    isLogin: false,\n    loginResult: defaultLoginResult,\n  },\n  mutations: {\n    init(state){\n      let loginResult = JSON.parse(localStorage.getItem(\"loginResult\"));\n      console.log(localStorage.getItem(\"loginResult\"));\n      if (loginResult !=null){\n        state.loginResult = loginResult;\n      }\n    },\n    login(state, loginResult){\n      state.loginResult = loginResult;\n    },\n    logout(state){\n      localStorage.removeItem(\"loginResult\");\n      state.loginResult = defaultLoginResult;\n    }\n  },\n  actions: {\n  },\n  getters: {\n    isLogin:state=>state.loginResult.user_id !== null,\n    userID:state=>state.loginResult.user_id,\n    username:state=>state.loginResult.user_name,\n    accessToken:state=>state.loginResult.token,\n  }\n})\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,IAAI,MAAM,MAAM;AAEvBD,GAAG,CAACE,GAAG,CAACD,IAAI,CAAC;AAEb,MAAME,kBAAkB,GAAG;EACzBC,KAAK,EAAC,IAAI;EACVC,OAAO,EAAC,IAAI;EACZC,SAAS,EAAC;AACZ,CAAC;AAED,eAAe,IAAIL,IAAI,CAACM,KAAK,CAAC;EAC5BC,KAAK,EAAE;IACLC,OAAO,EAAE,KAAK;IACdC,WAAW,EAAEP;EACf,CAAC;EACDQ,SAAS,EAAE;IACTC,IAAIA,CAACJ,KAAK,EAAC;MACT,IAAIE,WAAW,GAAGG,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;MACjEC,OAAO,CAACC,GAAG,CAACH,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;MAChD,IAAIN,WAAW,IAAG,IAAI,EAAC;QACrBF,KAAK,CAACE,WAAW,GAAGA,WAAW;MACjC;IACF,CAAC;IACDS,KAAKA,CAACX,KAAK,EAAEE,WAAW,EAAC;MACvBF,KAAK,CAACE,WAAW,GAAGA,WAAW;IACjC,CAAC;IACDU,MAAMA,CAACZ,KAAK,EAAC;MACXO,YAAY,CAACM,UAAU,CAAC,aAAa,CAAC;MACtCb,KAAK,CAACE,WAAW,GAAGP,kBAAkB;IACxC;EACF,CAAC;EACDmB,OAAO,EAAE,CACT,CAAC;EACDC,OAAO,EAAE;IACPd,OAAO,EAACD,KAAK,IAAEA,KAAK,CAACE,WAAW,CAACL,OAAO,KAAK,IAAI;IACjDmB,MAAM,EAAChB,KAAK,IAAEA,KAAK,CAACE,WAAW,CAACL,OAAO;IACvCoB,QAAQ,EAACjB,KAAK,IAAEA,KAAK,CAACE,WAAW,CAACJ,SAAS;IAC3CoB,WAAW,EAAClB,KAAK,IAAEA,KAAK,CAACE,WAAW,CAACN;EACvC;AACF,CAAC,CAAC", "ignoreList": []}]}