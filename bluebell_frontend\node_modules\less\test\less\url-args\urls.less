@font-face {
  src: url("/fonts/garamond-pro.ttf");
  src: local(Futura-Medium),
       url(fonts.svg#MyGeometricModern) format("svg");
}
#shorthands {
  background: url("http://www.lesscss.org/spec.html") no-repeat 0 4px;
  background: url("img.jpg") center / 100px;
  background: #fff url(image.png) center / 1px 100px repeat-x scroll content-box padding-box;
}
#misc {
  background-image: url(images/image.jpg);
}
#data-uri {
  background: url(data:image/png;charset=utf-8;base64,
    kiVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAABlBMVEUAAAD/
    k//+l2Z/dAAAAM0lEQVR4nGP4/5/h/1+G/58ZDrAz3D/McH8yw83NDDeNGe4U
    kg9C9zwz3gVLMDA/A6P9/AFGGFyjOXZtQAAAAAElFTkSuQmCC);
  background-image: url(data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==);
  background-image: url(http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700);
  background-image: url("http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700");
}

#svg-data-uri {
  background: transparent url('data:image/svg+xml, <svg version="1.1"><g></g></svg>');
}

.comma-delimited {
  background: url(bg.jpg) no-repeat, url(bg.png) repeat-x top left, url(bg);
}
.values {
    @a: 'Trebuchet';
    url: url(@a);
}

@import "../import/imports/font";

#data-uri {
  uri: data-uri('image/jpeg;base64', '../../data/image.jpg');
}

#data-uri-guess {
  uri: data-uri('../../data/image.jpg');
}

#data-uri-ascii {
  uri-1: data-uri('text/html', '../../data/page.html');
  uri-2: data-uri('../../data/page.html');
}

#svg-functions {
  background-image: svg-gradient(to bottom, black, white);
  background-image: svg-gradient(to bottom, black, orange 3%, white);
  @green_5: green 5%;
  @orange_percentage: 3%;
  @orange_color: orange;
  background-image: svg-gradient(to bottom, (mix(black, white) + #444) 1%, @orange_color @orange_percentage, ((@green_5)), white 95%);
}

#data-uri-with-spaces {
  background-image: url( data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==);
  background-image: url( ' data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==');
}
