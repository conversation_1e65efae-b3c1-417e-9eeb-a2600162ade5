{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ThisGo\\bluebell_frontend\\src\\router\\index.js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\router\\index.js", "mtime": 1600528994000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js", "mtime": 1756017426480}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmltcG9ydCBWdWUgZnJvbSAndnVlJzsKaW1wb3J0IFZ1ZVJvdXRlciBmcm9tICd2dWUtcm91dGVyJzsKaW1wb3J0IEhvbWUgZnJvbSAnLi4vdmlld3MvSG9tZS52dWUnOwppbXBvcnQgQ29udGVudCBmcm9tICcuLi92aWV3cy9Db250ZW50LnZ1ZSc7CmltcG9ydCBQdWJsaXNoIGZyb20gJy4uL3ZpZXdzL1B1Ymxpc2gudnVlJzsKaW1wb3J0IExvZ2luIGZyb20gJy4uL3ZpZXdzL0xvZ2luLnZ1ZSc7CmltcG9ydCBTaWduVXAgZnJvbSAnLi4vdmlld3MvU2lnblVwLnZ1ZSc7CmNvbnN0IG9yaWdpbmFsUHVzaCA9IFZ1ZVJvdXRlci5wcm90b3R5cGUucHVzaDsKVnVlUm91dGVyLnByb3RvdHlwZS5wdXNoID0gZnVuY3Rpb24gcHVzaChsb2NhdGlvbikgewogIHJldHVybiBvcmlnaW5hbFB1c2guY2FsbCh0aGlzLCBsb2NhdGlvbikuY2F0Y2goZXJyID0+IGVycik7Cn07ClZ1ZS51c2UoVnVlUm91dGVyKTsKY29uc3Qgcm91dGVzID0gW3sKICBwYXRoOiAnLycsCiAgbmFtZTogJ0hvbWUnLAogIGNvbXBvbmVudDogSG9tZQp9LCB7CiAgcGF0aDogJy9wb3N0LzppZCcsCiAgbmFtZTogJ0NvbnRlbnQnLAogIGNvbXBvbmVudDogQ29udGVudAp9LCB7CiAgcGF0aDogJy9wdWJsaXNoJywKICBuYW1lOiAnUHVibGlzaCcsCiAgY29tcG9uZW50OiBQdWJsaXNoLAogIG1ldGE6IHsKICAgIHJlcXVpcmVBdXRoOiB0cnVlCiAgfQp9LCB7CiAgcGF0aDogJy9sb2dpbicsCiAgbmFtZTogIkxvZ2luIiwKICBjb21wb25lbnQ6IExvZ2luCn0sIHsKICBwYXRoOiAnL3NpZ251cCcsCiAgbmFtZTogIlNpZ25VcCIsCiAgY29tcG9uZW50OiBTaWduVXAKfV07CmNvbnN0IHJvdXRlciA9IG5ldyBWdWVSb3V0ZXIoewogIG1vZGU6ICdoaXN0b3J5JywKICBiYXNlOiBwcm9jZXNzLmVudi5CQVNFX1VSTCwKICByb3V0ZXMKfSk7CmV4cG9ydCBkZWZhdWx0IHJvdXRlcjs="}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Home", "Content", "Publish", "<PERSON><PERSON>", "SignUp", "originalPush", "prototype", "push", "location", "call", "catch", "err", "use", "routes", "path", "name", "component", "meta", "requireAuth", "router", "mode", "base", "process", "env", "BASE_URL"], "sources": ["E:/ThisGo/bluebell_frontend/src/router/index.js"], "sourcesContent": ["import Vue from 'vue'\nimport VueRouter from 'vue-router'\nimport Home from '../views/Home.vue'\nimport Content from '../views/Content.vue'\nimport Publish from '../views/Publish.vue'\nimport Login from '../views/Login.vue'\nimport SignUp from '../views/SignUp.vue'\nconst originalPush = VueRouter.prototype.push;\nVueRouter.prototype.push = function push(location) {\n  return originalPush.call(this, location).catch(err => err);\n}\nVue.use(VueRouter)\n\n  const routes = [\n  {\n    path: '/',\n    name: 'Home',\n    component: Home\n  },\n  {\n    path: '/post/:id',\n    name: 'Content',\n    component: Content\n  },\n  {\n    path: '/publish',\n    name: 'Publish',\n    component: Publish,\n    meta: { requireAuth: true }\n  },\n  {\n    path: '/login',\n    name:\"Login\",\n    component: Login\n  },\n  {\n    path: '/signup',\n    name:\"SignUp\",\n    component: SignUp\n  }\n]\n\nconst router = new VueRouter({\n  mode: 'history',\n  base: process.env.BASE_URL,\n  routes\n})\n\nexport default router\n"], "mappings": ";AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,KAAK,MAAM,oBAAoB;AACtC,OAAOC,MAAM,MAAM,qBAAqB;AACxC,MAAMC,YAAY,GAAGN,SAAS,CAACO,SAAS,CAACC,IAAI;AAC7CR,SAAS,CAACO,SAAS,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACC,QAAQ,EAAE;EACjD,OAAOH,YAAY,CAACI,IAAI,CAAC,IAAI,EAAED,QAAQ,CAAC,CAACE,KAAK,CAACC,GAAG,IAAIA,GAAG,CAAC;AAC5D,CAAC;AACDb,GAAG,CAACc,GAAG,CAACb,SAAS,CAAC;AAEhB,MAAMc,MAAM,GAAG,CACf;EACEC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAEhB;AACb,CAAC,EACD;EACEc,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEf;AACb,CAAC,EACD;EACEa,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,SAAS;EACfC,SAAS,EAAEd,OAAO;EAClBe,IAAI,EAAE;IAAEC,WAAW,EAAE;EAAK;AAC5B,CAAC,EACD;EACEJ,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAC,OAAO;EACZC,SAAS,EAAEb;AACb,CAAC,EACD;EACEW,IAAI,EAAE,SAAS;EACfC,IAAI,EAAC,QAAQ;EACbC,SAAS,EAAEZ;AACb,CAAC,CACF;AAED,MAAMe,MAAM,GAAG,IAAIpB,SAAS,CAAC;EAC3BqB,IAAI,EAAE,SAAS;EACfC,IAAI,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ;EAC1BX;AACF,CAAC,CAAC;AAEF,eAAeM,MAAM", "ignoreList": []}]}