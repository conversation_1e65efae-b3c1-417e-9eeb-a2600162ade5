{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Publish.vue?vue&type=style&index=0&id=695cd445&lang=less&scoped=true", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Publish.vue", "mtime": 1600574148000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Publish.vue"], "names": [], "mappings": ";AAsIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Publish.vue", "sourceRoot": "src/views", "sourcesContent": ["\n<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <div class=\"post-name\">我好想写点什么</div>\n      <div class=\"post-type\">\n        <input type=\"text\" class=\"post-type-value\" placeholder=\"选择一个频道\" v-model=\"selectCommunity.name\" @click=\"showCommunity()\"/>\n        <ul class=\"post-type-options\" v-show=\"showCommunityList\">\n          <li class=\"post-type-cell\"\n            v-for=\"(community, index) in communityList\"\n            :key=\"community.id\"\n            @click=\"selected(index)\"\n          >\n            {{community.name}}\n          </li>\n        </ul>\n        <i class=\"p-icon\"></i>\n      </div>\n      <div class=\"post-content\">\n        <ul class=\"cat\">\n          <li class=\"cat-item active\">\n            <i class=\"iconfont icon-edit\"></i>post\n          </li>\n          <li class=\"cat-item\">\n            <i class=\"iconfont icon-image\"></i>image/video\n          </li>\n        </ul>\n        <div class=\"post-sub-container\">\n          <div class=\"post-sub-header\">\n            <textarea class=\"post-title\" id cols=\"30\" rows=\"10\" v-model=\"title\" placeholder=\"标题\"></textarea>\n            <span class=\"textarea-num\">0/300</span>\n          </div>\n          <!---此处放置富文本--->\n          <div class=\"post-text-con\">\n            <textarea\n              class=\"post-content-t\"\n              id\n              cols=\"30\"\n              rows=\"10\"\n              v-model=\"content\"\n              placeholder=\"内容\"\n            ></textarea>\n          </div>\n        </div>\n        <div class=\"post-footer\">\n          <div class=\"btns\">\n            <button class=\"btn\">取消</button>\n            <button class=\"btn\" @click=\"submit()\">发表</button>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class=\"right\">\n      <div class=\"post-rank\">\n        <h5 class=\"p-r-title\">\n          <i class=\"p-r-icon\"></i>发帖规范\n        </h5>\n        <ul class=\"p-r-content\">\n          <li class=\"p-r-item\">1.网络不是法外之地</li>\n          <li class=\"p-r-item\">2.网络不是法外之地</li>\n          <li class=\"p-r-item\">3.网络不是法外之地</li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Publish\",\n  data() {\n    return {\n      title: \"\",\n      content: \"\",\n      showCommunityList: false,\n      selectCommunity: {},\n      communityList: []\n    };\n  },\n  methods: {\n    submit() {\n      this.$axios({\n        method: \"post\",\n        url: \"/post\",\n        data: JSON.stringify({\n          title: this.title,\n          content: this.content,\n          community_id: this.selectCommunity.id\n        })\n      })\n        .then(response => {\n          console.log(response.data);\n          if (response.code == 1000) {\n            this.$router.push({ path: this.redirect || \"/\" });\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    getCommunityList() {\n      this.$axios({\n        method: \"get\",\n        url: \"/community\"\n      })\n        .then(response => {\n          console.log(response.data);\n          if (response.code == 1000) {\n            this.communityList = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    showCommunity(){\n      this.showCommunityList = !this.showCommunityList;\n    },\n    selected(index) {\n      this.selectCommunity = this.communityList[index];\n      this.showCommunityList = false;\n      console.log(this.selectCommunity)\n    }\n  },\n  mounted: function() {\n    this.getCommunityList();\n  }\n};\n</script>\n<style lang=\"less\" scoped>\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 0 auto;\n  padding: 20px 24px;\n  margin-top: 48px;\n  .left {\n    flex-grow: 1;\n    max-width: 740px;\n    word-break: break-word;\n    flex: 1;\n    margin: 32px;\n    margin-right: 12px;\n    padding-bottom: 30px;\n    position: relative;\n    .post-name {\n      padding: 4px;\n      margin: 16px 0;\n      border-bottom: solid 1px #edeff1;\n      display: -webkit-flex;\n      display: flex;\n      justify-content: space-between;\n      .p-btn {\n        font-size: 12px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 24px;\n        text-transform: uppercase;\n        border: none;\n        padding: 0;\n        margin-left: 10px;\n        color: #0079d3;\n      }\n      .p-num {\n        font-size: 12px;\n        font-weight: 400;\n        line-height: 16px;\n        background: #878a8c;\n        border-radius: 2px;\n        color: #ffffff;\n        margin-left: 4px;\n        padding: 1px 3px;\n      }\n    }\n    .post-type {\n      position: relative;\n      box-sizing: border-box;\n      width: 300px;\n      height: 40px;\n      border-radius: 4px;\n      transition: box-shadow 0.2s ease;\n      box-shadow: 0 0 0 0 #ffffff;\n      border: 1px solid #edeff1;\n      background-color: #ffffff;\n      padding-left: 10px;\n      position: relative;\n      .post-type-value {\n        font-size: 14px;\n        font-weight: 500;\n        line-height: 40px;\n        width: 100%;\n        vertical-align: middle;\n        color: #1c1c1c;\n        background-color: transparent;\n        cursor: pointer;\n      }\n      .post-type-options {\n        position: absolute;\n        width: 100%;\n        background-color: white;\n        left: 0;\n        z-index: 1;\n        border-radius: 4px;\n        .post-type-cell {\n          margin: 14px 8px 5px;\n          font-size: 14px;\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          padding-bottom: 8px;\n          color: #1c1c1c;\n          cursor: pointer;\n        }\n      }\n      .p-icon {\n        width: 0;\n        height: 0;\n        border-top: 5px solid #878a8c;\n        border-right: 5px solid transparent;\n        border-bottom: 5px solid transparent;\n        border-left: 5px solid transparent;\n        margin-left: 10px;\n        position: absolute;\n        top: 50%;\n        right: 10px;\n        cursor: pointer;\n      }\n    }\n    .post-content {\n      background-color: #ffffff;\n      margin: 10px 0;\n      padding-bottom: 15px;\n      border-radius: 5px;\n      .cat {\n        display: flex;\n        display: -webkit-flex;\n        justify-content: space-between;\n        align-items: center;\n        width: 100%;\n        height: 53px;\n        .cat-item {\n          padding: 10px 0;\n          width: 50%;\n          height: 40px;\n          line-height: 40px;\n          text-align: center;\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          border-right: 1px solid #edeff1;\n          color: #878a8c;\n          .iconfont {\n            margin-right: 4px;\n          }\n        }\n        .active {\n          color: #0079d3;\n          font-weight: bolder;\n          background: none;\n        }\n      }\n      .post-sub-container {\n        padding: 16px;\n        .post-sub-header {\n          position: relative;\n          .post-title {\n            resize: none;\n            box-sizing: border-box;\n            overflow: hidden;\n            display: block;\n            width: 100%;\n            height: 40px;\n            padding: 0 0 0 10px;\n            outline: none;\n            border: 1px solid #edeff1;\n            border-radius: 4px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 400;\n            line-height: 40px;\n          }\n          .textarea-num {\n            font-size: 10px;\n            font-weight: 700;\n            letter-spacing: 0.5px;\n            line-height: 12px;\n            text-transform: uppercase;\n            bottom: 12px;\n            color: #878a8c;\n            pointer-events: none;\n            position: absolute;\n            right: 12px;\n          }\n        }\n        .post-text-con {\n          width: 100%;\n          height: 200px;\n          border: 1px solid #edeff1;\n          margin-top: 20px;\n          .post-content-t {\n            resize: none;\n            box-sizing: border-box;\n            overflow: hidden;\n            display: block;\n            width: 100%;\n            height: 200px;\n            padding: 12px 8px;\n            outline: none;\n            border: 1px solid #edeff1;\n            border-radius: 4px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 400;\n            line-height: 21px;\n          }\n        }\n      }\n      .post-footer {\n        display: flex;\n        display: -webkit-flex;\n        margin: 0 16px;\n        justify-content: flex-end;\n        .sign {\n          display: flex;\n          display: -webkit-flex;\n          .sign-item {\n            list-style: none;\n            padding: 5px 8px;\n            border: 1px solid #edeff1;\n            margin-right: 10px;\n            color: #878a8c;\n            font-size: 12px;\n            font-weight: 700;\n          }\n        }\n        .btns {\n          .btn {\n            border: 1px solid transparent;\n            border-radius: 4px;\n            box-sizing: border-box;\n            text-align: center;\n            text-decoration: none;\n            font-size: 12px;\n            font-weight: 700;\n            letter-spacing: 0.5px;\n            line-height: 24px;\n            text-transform: uppercase;\n            padding: 3px 16px;\n            background: #0079d3;\n            color: #ffffff;\n            margin-left: 8px;\n            cursor: pointer;\n          }\n        }\n      }\n      .alias {\n        background-color: #f6f7f8;\n        border-radius: 0 0 6px 6px;\n        border-top: solid 1px #edeff1;\n        display: -ms-flexbox;\n        display: flex;\n        -ms-flex-flow: column;\n        flex-flow: column;\n        padding: 8px 16px 21px;\n        position: relative;\n        .send-post {\n          font-size: 14px;\n          font-weight: 500;\n          line-height: 18px;\n          color: #1c1c1c;\n          margin-right: 4px;\n        }\n        .connect {\n          font-size: 14px;\n          font-weight: 500;\n          line-height: 18px;\n          color: #0079d3;\n          display: block;\n          margin-right: 4px;\n          margin-top: 10px;\n        }\n      }\n    }\n  }\n  .right {\n    flex-grow: 0;\n    width: 312px;\n    margin-top: 62px;\n    .post-rank {\n      background-color: #ffffff;\n      border-radius: 4px;\n      margin-top: 15px;\n      padding: 12px;\n      .p-r-title {\n        display: flex;\n        display: -webkit-flex;\n        align-items: center;\n        .p-r-icon {\n          width: 40px;\n          height: 40px;\n          background: url(\"../assets/images/avatar.png\") no-repeat;\n          background-size: cover;\n          margin-right: 10px;\n        }\n        font-size: 16px;\n        font-weight: 500;\n        line-height: 20px;\n        -ms-flex-align: center;\n        align-items: center;\n        border-bottom: 1px solid #edeff1;\n        color: #1c1c1c;\n        padding-bottom: 10px;\n        // display: -ms-flexbox;\n        // display: flex;\n      }\n      .p-r-content {\n        display: flex;\n        display: -webkit-flex;\n        flex-direction: column;\n        .p-r-item {\n          list-style: none;\n          border-bottom: 1px solid #edeff1;\n          color: #1c1c1c;\n          padding: 10px 5px;\n        }\n      }\n    }\n  }\n}\n</style>"]}]}