@varToGet: default-color;
.foo {
  color1: @defaults[@default-color];
  color2: @defaults[@nested][@color];
  color3: @theme[color];
  color4: @theme[@nested][color];
  color5: @defaults[@@varToGet];
  prop: #ns1[foo];
  var: #ns1[@foo];
  sub: #ns1.vars[$sub];
}

@defaults: {
  @default-color: red;
  @nested: {
    @color: yellow;
  }
};

@theme: {
  color: red;
  @nested: {
    color: yellow;
  }
};

#ns1 {
  foo: bar;
  @foo: baz;
  .vars() {
    sub: value;
  }
}

// Test that it matches more than one mixin
#ns1 {
  foo: uno;
  @foo: dos;
  .vars() {
    sub: tres;
  }
}

// https://github.com/less/less.js/issues/3346
#DEF() {
  .colors() { primary: grey; }
}

.button {
  color: #DEF.colors[primary];
  border-color: #AAA #CCC;
}