{"version": 3, "file": "prefer-function-type.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-function-type.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAI+C;AAC/C,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,sBAAsB;IAC5B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,+DAA+D;YACjE,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,4BAA4B,EAC1B,2EAA2E;SAC9E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C;;;WAGG;QACH,SAAS,eAAe,CAAC,IAAqC;YAC5D,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC9C,OAAO,KAAK,CAAC;aACd;YACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC7B,OAAO,IAAI,CAAC;aACb;YACD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;YAExC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU,CACpE,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,oBAAoB,CAAC,MAAiC;YAC7D,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO,KAAK,CAAC;aACd;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE;gBACnB,KAAK,mCAAc,CAAC,WAAW,CAAC;gBAChC,KAAK,mCAAc,CAAC,kBAAkB,CAAC;gBACvC,KAAK,mCAAc,CAAC,WAAW;oBAC7B,OAAO,IAAI,CAAC;gBACd;oBACE,OAAO,KAAK,CAAC;aAChB;QACH,CAAC;QAED;;;;WAIG;QACH,SAAS,gBAAgB,CACvB,IAE4C,EAC5C,MAAqB;YAErB,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5B,MAAM,QAAQ,GAAG,IAAI,CAAC,UAAW,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC;YACnD,MAAM,IAAI,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAE9D,IAAI,UAAU,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,QAAQ,CAAC,MAAM,IAAI,CAAC,KAAK,CACzD,QAAQ,GAAG,CAAC,CACb,EAAE,CAAC;YAEJ,IAAI,oBAAoB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;gBACvC,UAAU,GAAG,IAAI,UAAU,GAAG,CAAC;aAChC;YACD,IAAI,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,sBAAsB,EAAE;gBACzD,IAAI,OAAO,MAAM,CAAC,cAAc,KAAK,WAAW,EAAE;oBAChD,OAAO,QAAQ,UAAU;yBACtB,OAAO,EAAE;yBACT,KAAK,CACJ,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,EAClB,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,CAC/B,MAAM,UAAU,EAAE,CAAC;iBACvB;gBACD,OAAO,QAAQ,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,UAAU,EAAE,CAAC;aACjD;YACD,OAAO,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;QACzE,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAAC,MAA4B,EAAE,IAAmB;YACpE,IACE,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,0BAA0B;gBACxD,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,+BAA+B,CAAC;gBACjE,OAAO,MAAM,CAAC,UAAU,KAAK,WAAW,EACxC;gBACA,MAAM,UAAU,GAAG,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;gBAClD,MAAM,QAAQ,GACZ,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa;oBACxC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACf,CAAC,CAAC,UAAU;yBACP,SAAS,CAAC,IAAI,CAAC;yBACf,MAAM,CACL,KAAK,CAAC,EAAE,CACN,KAAK,CAAC,IAAI,KAAK,oCAAe,CAAC,OAAO;wBACtC,KAAK,CAAC,KAAK,KAAK,WAAW,CAC9B,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,8BAA8B;oBACzC,IAAI,EAAE;wBACJ,IAAI,EACF,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa;4BACxC,CAAC,CAAC,cAAc;4BAChB,CAAC,CAAC,WAAW;wBACjB,aAAa,EAAE,UAAU;qBAC1B;oBACD,GAAG,CAAC,KAAK;wBACP,OAAO,KAAK,CAAC,gBAAgB,CAC3B,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EACzB,UAAU,CACX,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,sBAAsB,CAAC,IAAI;gBACzB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;oBACzD,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;iBACtC;YACH,CAAC;YACD,mCAAmC,CAAC,IAA4B;gBAC9D,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;YACrC,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}