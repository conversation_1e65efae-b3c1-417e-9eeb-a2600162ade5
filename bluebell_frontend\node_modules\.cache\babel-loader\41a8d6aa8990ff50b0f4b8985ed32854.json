{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\SignUp.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\SignUp.vue", "mtime": 1598770390000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiU2lnblVwIiwKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgdXNlcm5hbWU6ICIiLAogICAgICBwYXNzd29yZDogIiIsCiAgICAgIGNvbmZpcm1fcGFzc3dvcmQ6ICIiLAogICAgICBzdWJtaXR0ZWQ6IGZhbHNlCiAgICB9OwogIH0sCiAgY29tcHV0ZWQ6IHt9LAogIGNyZWF0ZWQoKSB7fSwKICBtZXRob2RzOiB7CiAgICBzdWJtaXQoKSB7CiAgICAgIHRoaXMuJGF4aW9zKHsKICAgICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgICB1cmw6ICcvc2lnbnVwJywKICAgICAgICBkYXRhOiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICB1c2VybmFtZTogdGhpcy51c2VybmFtZSwKICAgICAgICAgIHBhc3N3b3JkOiB0aGlzLnBhc3N3b3JkLAogICAgICAgICAgY29uZmlybV9wYXNzd29yZDogdGhpcy5jb25maXJtX3Bhc3N3b3JkCiAgICAgICAgfSkKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhKTsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gMTAwMCkgewogICAgICAgICAgY29uc29sZS5sb2coJ3NpZ251cCBzdWNjZXNzJyk7CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgIG5hbWU6ICJMb2dpbiIKICAgICAgICAgIH0pOwogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBjb25zb2xlLmxvZyhyZXMubXNnKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsKICAgICAgICBjb25zb2xlLmxvZyhlcnJvcik7CiAgICAgIH0pOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "username", "password", "confirm_password", "submitted", "computed", "created", "methods", "submit", "$axios", "method", "url", "JSON", "stringify", "then", "res", "console", "log", "code", "$router", "push", "msg", "catch", "error"], "sources": ["src/views/SignUp.vue"], "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"container\">\n      <h2 class=\"form-title\">注册</h2>\n      <div class=\"form-group\">\n        <label for=\"name\">用户名</label>\n        <input type=\"text\" class=\"form-control\" name=\"name\" id=\"name\" placeholder=\"用户名\" v-model=\"username\"/>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"pass\">密码</label>\n        <input type=\"password\" class=\"form-control\" name=\"pass\" id=\"pass\" placeholder=\"密码\" v-model=\"password\"/>\n      </div>\n      <div class=\"form-group\">\n        <label for=\"re_pass\">确认密码</label>\n        <input type=\"password\" class=\"form-control\" name=\"re_pass\" id=\"re_pass\" placeholder=\"确认密码\"  v-model=\"confirm_password\"/>\n      </div>\n      <div class=\"form-btn\">\n        <button type=\"button\" class=\"btn btn-info\" @click=\"submit\">提交</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n\tname: \"SignUp\",\n\tdata() {\n\t\treturn {\n\t\t\tusername: \"\",\n\t\t\tpassword: \"\",\n\t\t\tconfirm_password: \"\",\n\t\t\tsubmitted: false\n\t\t};\n\t},\n\tcomputed: {\n\t},\n\tcreated() {\n\n\t},\n\tmethods: {\n\t\tsubmit() {\n\t\t\tthis.$axios({\n\t\t\t\tmethod: 'post',\n\t\t\t\turl:'/signup',\n\t\t\t\tdata: JSON.stringify({\n\t\t\t\t\tusername: this.username,\n\t\t\t\t\tpassword: this.password,\n\t\t\t\t\tconfirm_password: this.confirm_password\n\t\t\t\t})\n\t\t\t}).then((res)=>{\n\t\t\t\tconsole.log(res.data);\n\t\t\t\tif (res.code == 1000) {\n          console.log('signup success');\n          this.$router.push({ name: \"Login\" });\n\t\t\t\t}else{\n          console.log(res.msg);\n        }\n\t\t\t}).catch((error)=>{\n\t\t\t\tconsole.log(error)\n\t\t\t})\n\t\t}\n\t}\n};\n</script>\n<style lang=\"less\" scoped>\n.main {\n  background: #f8f8f8;\n  padding: 150px 0;\n  .container {\n    width: 600px;\n    background: #fff;\n    margin: 0 auto;\n    max-width: 1200px;\n    padding: 20px;\n    .form-title {\n      margin-bottom: 33px;\n      text-align: center;\n    }\n    .form-group {\n      margin: 15px;\n      label {\n        display: inline-block;\n        max-width: 100%;\n        margin-bottom: 5px;\n        font-weight: 700;\n      }\n      .form-control {\n        display: block;\n        width: 100%;\n        height: 34px;\n        padding: 6px 12px;\n        font-size: 14px;\n        line-height: 1.42857143;\n        color: #555;\n        background-color: #fff;\n        background-image: none;\n        border: 1px solid #ccc;\n        border-radius: 4px;\n      }\n    }\n    .form-btn {\n      display: flex;\n      justify-content: center;\n      .btn {\n        padding: 6px 20px;\n        font-size: 18px;\n        line-height: 1.3333333;\n        border-radius: 6px;\n        display: inline-block;\n        margin-bottom: 0;\n        font-weight: 400;\n        text-align: center;\n        white-space: nowrap;\n        vertical-align: middle;\n        -ms-touch-action: manipulation;\n        touch-action: manipulation;\n        cursor: pointer;\n        border: 1px solid transparent;\n      }\n      .btn-info {\n        color: #fff;\n        background-color: #5bc0de;\n        border-color: #46b8da;\n      }\n    }\n  }\n}\n</style>"], "mappings": ";AAwBA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;MACAC,gBAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA,GACA;EACAC,QAAA,GAEA;EACAC,OAAA;IACAC,OAAA;MACA,KAAAC,MAAA;QACAC,MAAA;QACAC,GAAA;QACAX,IAAA,EAAAY,IAAA,CAAAC,SAAA;UACAZ,QAAA,OAAAA,QAAA;UACAC,QAAA,OAAAA,QAAA;UACAC,gBAAA,OAAAA;QACA;MACA,GAAAW,IAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA,CAAAf,IAAA;QACA,IAAAe,GAAA,CAAAG,IAAA;UACAF,OAAA,CAAAC,GAAA;UACA,KAAAE,OAAA,CAAAC,IAAA;YAAArB,IAAA;UAAA;QACA;UACAiB,OAAA,CAAAC,GAAA,CAAAF,GAAA,CAAAM,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,KAAA;QACAP,OAAA,CAAAC,GAAA,CAAAM,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}