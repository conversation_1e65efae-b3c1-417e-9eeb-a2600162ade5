<!DOCTYPE html><html><head><script>"use strict";!function(){var i=(window.location.pathname.match(/^(\/(?:ipfs|ipns)\/[^/]+)/)||[])[1]||"";window.__GATSBY_IPFS_PATH_PREFIX__=i}();</script><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><style data-href="../../styles.e93b5499b63484750fba.css">code[class*=language-],pre[class*=language-]{color:#ccc;background:none;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:#2d2d2d}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal}.token.block-comment,.token.cdata,.token.comment,.token.doctype,.token.prolog{color:#999}.token.punctuation{color:#ccc}.token.attr-name,.token.deleted,.token.namespace,.token.tag{color:#e2777a}.token.function-name{color:#6196cc}.token.boolean,.token.function,.token.number{color:#f08d49}.token.class-name,.token.constant,.token.property,.token.symbol{color:#f8c555}.token.atrule,.token.builtin,.token.important,.token.keyword,.token.selector{color:#cc99cd}.token.attr-value,.token.char,.token.regex,.token.string,.token.variable{color:#7ec699}.token.entity,.token.operator,.token.url{color:#67cdcc}.token.bold,.token.important{font-weight:700}.token.italic{font-style:italic}.token.entity{cursor:help}.token.inserted{color:green}a,abbr,acronym,address,applet,article,aside,audio,b,big,blockquote,body,canvas,caption,center,cite,code,dd,del,details,dfn,div,dl,dt,em,embed,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,ins,kbd,label,legend,li,mark,menu,nav,object,ol,output,p,pre,q,ruby,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,u,ul,var,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:after,blockquote:before,q:after,q:before{content:"";content:none}table{border-collapse:collapse;border-spacing:0}[hidden]{display:none}html{font-family:Poppins,sans-serif}*{box-sizing:border-box}li,p{font-size:15px;line-height:1.7;font-weight:300}p,ul{padding:10px 0}strong{font-weight:700;color:#c3f}li{list-style-type:disc;list-style-position:inside;padding:8px 0}.documentation h1{font-size:42px;font-weight:600;padding:30px 0 10px}.documentation h2{font-size:22px;font-weight:300}.documentation h3{color:#c3f;font-size:22px;padding:30px 0 5px;font-weight:500}.documentation h4{font-weight:600;padding:20px 0 5px}.documentation p{display:inline-block}:not(pre)>code[class*=language-],pre[class*=language-]{border-radius:4px;background-color:#413844;font-size:13px}:not(pre)>code[class*=language-text]{background-color:rgba(204,139,216,.1);color:#413844;padding:2px 6px;border-radius:0;font-size:14px;font-weight:700;border-radius:1px;display:inline-block}.documentation a,a>code[class*=language-text]{color:#fb3b49;font-weight:600}p>code[class*=language-text]{display:inline-block}.documentation h1:before{content:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 27 26'%3E%3Cdefs%3E%3ClinearGradient id='a' x1='18.13' x2='25.6' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='.37' stop-color='%23fb8719'/%3E%3Cstop offset='.51' stop-color='%23fa8420'/%3E%3Cstop offset='.61' stop-color='%23f9802c'/%3E%3Cstop offset='.69' stop-color='%23f7793d'/%3E%3Cstop offset='.76' stop-color='%23f47053'/%3E%3Cstop offset='.82' stop-color='%23f1656e'/%3E%3Cstop offset='.87' stop-color='%23ed578f'/%3E%3Cstop offset='.92' stop-color='%23e948b5'/%3E%3Cstop offset='.97' stop-color='%23e437de'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='b' x1='17.89' x2='25.84' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='1' x2='18.69' y1='17.84' y2='17.84' xlink:href='%23a'/%3E%3ClinearGradient id='d' x1='.76' x2='18.93' y1='17.84' y2='17.84' xlink:href='%23b'/%3E%3ClinearGradient id='e' x1='1' x2='20.48' y1='7.33' y2='7.33' xlink:href='%23a'/%3E%3ClinearGradient id='f' x1='.76' x2='20.72' y1='7.33' y2='7.33' xlink:href='%23b'/%3E%3C/defs%3E%3Cpath fill='url(%23a)' stroke='url(%23b)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.34-.41L25 14.06l-5-11a.28.28 0 11.5-.23L25.58 14a.28.28 0 010 .28l-6.91 9.9a.28.28 0 01-.14.06z'/%3E%3Cpath fill='url(%23c)' stroke='url(%23d)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.14 0l-12-1.15a.28.28 0 01-.23-.09L1 11.81a.28.28 0 11.5-.23l5.07 11L18 23.68 13 13a.28.28 0 11.5-.23l5.12 11.12a.28.28 0 01-.09.35z'/%3E%3Cpath fill='url(%23e)' stroke='url(%23f)' stroke-miterlimit='10' stroke-width='.48' d='M13.4 13.12a.25.25 0 01-.14 0L1.25 12a.28.28 0 01-.2-.44L8 1.64a.28.28 0 01.25-.12l12 1.18a.28.28 0 01.2.44L13.51 13a.25.25 0 01-.11.12z'/%3E%3C/svg%3E");position:relative;display:inline-block;padding-right:8px;top:3px;width:28px}.active-sidebar-link{background-color:#ffebff}.active-navbar-link{border-bottom:3px solid #c3f}.header-link-class{margin-left:-24px}.disabled-body{overflow:hidden}</style><meta name="generator" content="Gatsby 2.18.18"/><title data-react-helmet="true"></title><style data-styled="UihHA jAtLxz bCnUTx bAGJfc hJcdbU kOyZtC eCQAUi fsnHHg bXQeSB dsecBh iPgskl bNiGAM gJQTGP fMOzaj" data-styled-version="4.4.1">
/* sc-component-id: links__NavLink-sc-19vgq0o-1 */
.kOyZtC{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .kOyZtC:hover{opacity:.5;}
/* sc-component-id: links__BasicNavLink-sc-19vgq0o-2 */
.eCQAUi{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .eCQAUi:hover{opacity:.5;}
/* sc-component-id: links__SidebarLink-sc-19vgq0o-3 */
.iPgskl{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#fb3b49;padding:10px;-webkit-transition:background-color .3s;transition:background-color .3s;} .iPgskl:hover{background-color:#ffebff;}
/* sc-component-id: Accordion__SectionButton-i8yhwx-0 */
.dsecBh{outline:none;background-color:transparent;cursor:pointer;color:red;border:none;font-size:18px;font-weight:bold;padding:5px 0;-webkit-transition:opacity .5s;transition:opacity .5s;} .dsecBh:after{background:center / contain no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNi41IDEwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZiM2I0OTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnVwLWNhcnJvdDwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNOC4yNS44NWExLjE1LDEuMTUsMCwwLDAtLjgxLjM0bC02LDZBMS4xNSwxLjE1LDAsMCwwLDMuMDYsOC44MUw4LjI1LDMuNjNsNS4xOSw1LjE5YTEuMTUsMS4xNSwwLDAsMCwxLjYzLTEuNjNsLTYtNkExLjE1LDEuMTUsMCwwLDAsOC4yNS44NVoiLz48L3N2Zz4=);content:'';height:11px;width:28px;display:inline-block;} .dsecBh:hover{opacity:.6;}
/* sc-component-id: DocLinks__LinkDesc-sc-1vrw6od-0 */
.bNiGAM{font-size:11px;line-height:1.5;text-transform:lowercase;display:block;font-weight:400;color:#767676;}
/* sc-component-id: Sidebar__Container-gs0c67-0 */
.bXQeSB{border-right:1px solid #86838333;padding:30px;height:100vh;display:none;width:380px;position:-webkit-sticky;position:sticky;overflow:scroll;padding-bottom:200px;top:54px;background-color:#ffffff;} @media screen and (min-width:48em){.bXQeSB{display:block;}}
/* sc-component-id: navbar__Container-kjuegf-0 */
.UihHA{width:100%;border-bottom:1px solid #86838333;position:-webkit-sticky;position:sticky;top:0;background-color:#ffffff;z-index:1;}
/* sc-component-id: navbar__Inner-kjuegf-1 */
.jAtLxz{border-top:3px solid;border-image:linear-gradient(139deg,#fb8817,#ff4b01,#c12127,#e02aff) 3;margin:auto;height:53px;padding:0 30px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}
/* sc-component-id: navbar__Logo-kjuegf-2 */
.bAGJfc{width:120px;padding:0px 5px;height:18px;vertical-align:middle;display:inline-block;-webkit-transition:opacity .5s;transition:opacity .5s;} .bAGJfc:hover{opacity:.8;}
/* sc-component-id: navbar__Links-kjuegf-3 */
.hJcdbU{display:none;} @media screen and (min-width:48em){.hJcdbU{display:block;margin-left:auto;}}
/* sc-component-id: navbar__Heart-kjuegf-4 */
.bCnUTx{font-size:15px;display:inline-block;}
/* sc-component-id: navbar__Hamburger-kjuegf-5 */
.fsnHHg{border:none;background:center no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMzUgMjMiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudCk7fS5jbHMtMntmaWxsOnVybCgjbGluZWFyLWdyYWRpZW50LTIpO30uY2xzLTN7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudC0zKTt9PC9zdHlsZT48bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhci1ncmFkaWVudCIgeTE9IjIiIHgyPSIzNSIgeTI9IjIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBvZmZzZXQ9IjAiIHN0b3AtY29sb3I9IiNmYjg4MTciLz48c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNlMDJhZmYiLz48L2xpbmVhckdyYWRpZW50PjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTIiIHkxPSIxMS41IiB5Mj0iMTEuNSIgeGxpbms6aHJlZj0iI2xpbmVhci1ncmFkaWVudCIvPjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTMiIHkxPSIyMSIgeTI9IjIxIiB4bGluazpocmVmPSIjbGluZWFyLWdyYWRpZW50Ii8+PC9kZWZzPjx0aXRsZT5oYW1idXJnZXI8L3RpdGxlPjxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjxyZWN0IGNsYXNzPSJjbHMtMiIgeT0iOS41IiB3aWR0aD0iMzUiIGhlaWdodD0iNCIgcng9IjIiIHJ5PSIyIi8+PHJlY3QgY2xhc3M9ImNscy0zIiB5PSIxOSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjwvc3ZnPg==);height:30px;width:30px;display:block;margin-left:auto;-webkit-transition:opacity .5s;transition:opacity .5s;cursor:pointer;} .fsnHHg:hover{opacity:.6;} @media screen and (min-width:48em){.fsnHHg{display:none;}}
/* sc-component-id: FoundTypo__Container-sc-1e373sc-0 */
.fMOzaj{margin:80px 0;border-top:1px solid black;padding:20px 0;}
/* sc-component-id: Page__Content-sc-4b62ym-0 */
.gJQTGP{max-width:760px;margin:auto;padding:0 30px 120px;}</style><link rel="icon" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="manifest" href="../../manifest.webmanifest"/><meta name="theme-color" content="#663399"/><link rel="apple-touch-icon" sizes="48x48" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="72x72" href="../../icons/icon-72x72.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="96x96" href="../../icons/icon-96x96.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="144x144" href="../../icons/icon-144x144.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="192x192" href="../../icons/icon-192x192.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="256x256" href="../../icons/icon-256x256.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="384x384" href="../../icons/icon-384x384.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="512x512" href="../../icons/icon-512x512.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2"/><style type="text/css">@font-face{font-family:Poppins;font-style:normal;font-weight:300;src:local('Poppins Light'),local('Poppins-Light'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:400;src:local('Poppins Regular'),local('Poppins-Regular'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:500;src:local('Poppins Medium'),local('Poppins-Medium'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:400;src:local('Inconsolata Regular'),local('Inconsolata-Regular'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5q4.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:700;src:local('Inconsolata Bold'),local('Inconsolata-Bold'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_o.woff) format('woff');font-display: swap;}</style><style type="text/css">
    .header-link-class.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }
    .header-link-class.after {
      display: inline-block;
      padding-left: 4px;
    }
    h1 .header-link-class svg,
    h2 .header-link-class svg,
    h3 .header-link-class svg,
    h4 .header-link-class svg,
    h5 .header-link-class svg,
    h6 .header-link-class svg {
      visibility: hidden;
    }
    h1:hover .header-link-class svg,
    h2:hover .header-link-class svg,
    h3:hover .header-link-class svg,
    h4:hover .header-link-class svg,
    h5:hover .header-link-class svg,
    h6:hover .header-link-class svg,
    h1 .header-link-class:focus svg,
    h2 .header-link-class:focus svg,
    h3 .header-link-class:focus svg,
    h4 .header-link-class:focus svg,
    h5 .header-link-class:focus svg,
    h6 .header-link-class:focus svg {
      visibility: visible;
    }
  </style><script>
    document.addEventListener("DOMContentLoaded", function(event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var offset = element.offsetTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function() {
            window.scrollTo(0, offset - 100)
          }), 0)
        }
      }
    })
  </script><link as="script" rel="preload" href="../../webpack-runtime-b622568e0ef6e093f777.js"/><link as="script" rel="preload" href="../../styles-de5e304580bcba768a01.js"/><link as="script" rel="preload" href="../../commons-4df35f6dbd2fdc25d817.js"/><link as="script" rel="preload" href="../../app-041f7e4f56e7debd8d98.js"/><link as="script" rel="preload" href="../../component---src-templates-page-js-7faf8ceb01991e80d244.js"/><link as="fetch" rel="preload" href="../../page-data/cli-commands/npm-edit/page-data.json" crossorigin="anonymous"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" role="group" id="gatsby-focus-wrapper"><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="navbar__Container-kjuegf-0 UihHA css-4cffwv"><div class="navbar__Inner-kjuegf-1 jAtLxz css-4cffwv"><a href="../../"><style data-emotion-css="26z63x">.css-26z63x{box-sizing:border-box;margin:0;min-width:0;margin-left:4px;margin-right:24px;}</style><div class="navbar__Heart-kjuegf-4 bCnUTx css-26z63x">❤</div><style data-emotion-css="9taffg">.css-9taffg{box-sizing:border-box;margin:0;min-width:0;max-width:100%;height:auto;}</style><img src="data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDcgMTciPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMjMxZjIwO30uY2xzLTJ7ZmlsbDpub25lO308L3N0eWxlPjwvZGVmcz48dGl0bGU+Y2xpLWxvZ288L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTS41NCwxMy40aDYuNFYzLjY3aDMuMlYxMy40aDMuMlYuNDJILjU0Wk0zMS4yNi40MnYxM2g2LjRWMy42N2gzLjJWMTMuNGgzLjJWMy42N2gzLjE5VjEzLjRoMy4yVi40MlptLTksMy4yNWgzLjJ2Ni40OUgyMi4zWm0tNi40LDEzaDYuNFYxMy40aDYuNFYuNDJIMTUuOVoiLz48cmVjdCBjbGFzcz0iY2xzLTIiIHg9IjAuNTQiIHk9IjAuNDIiIHdpZHRoPSI0OS45MSIgaGVpZ2h0PSIxNi4yMiIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI2NS41OCAzLjU2IDY1LjU4IDkuODYgNzEuNjYgOS44NiA3MS42NiAxMy4wMiA2NS40NCAxMy4wMiA1OS4yIDEzLjA0IDU5LjIyIDAuNDEgNzEuNjYgMC40MSA3MS42NiAzLjU0IDY1LjU4IDMuNTYiLz48cG9seWdvbiBjbGFzcz0iY2xzLTEiIHBvaW50cz0iODAuNjIgMTAuMjMgODAuNjIgMC4zNiA3NC4yMyAwLjM2IDc0LjIzIDEzLjMgNzYuOTIgMTMuMyA4MC42MiAxMy4zIDg2LjQ3IDEzLjMgODYuNDcgMTAuMjMgODAuNjIgMTAuMjMiLz48cmVjdCBjbGFzcz0iY2xzLTEiIHg9IjEwMS4zMiIgeT0iOC4zNyIgd2lkdGg9IjEuOTkiIGhlaWdodD0iOC4yOSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTE0LjgzIC04OS43OSkgcm90YXRlKDkwKSIvPjxyZWN0IGNsYXNzPSJjbHMtMSIgeD0iODguMzMiIHk9IjAuMzYiIHdpZHRoPSI2LjM5IiBoZWlnaHQ9IjEyLjk0Ii8+PC9zdmc+" class="navbar__Logo-kjuegf-2 bAGJfc css-9taffg"/></a><ul class="navbar__Links-kjuegf-3 hJcdbU"><a class="links__NavLink-sc-19vgq0o-1 kOyZtC" href="../../cli-commands/npm/index.html">docs</a><a href="https://www.npmjs.com/" class="links__BasicNavLink-sc-19vgq0o-2 eCQAUi">npmjs.org</a></ul><button class="navbar__Hamburger-kjuegf-5 fsnHHg"></button></div></div><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-4cffwv"><nav class="Sidebar__Container-gs0c67-0 bXQeSB sidebar"><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">cli-commands</button><div><style data-emotion-css="l3rx45">.css-l3rx45{box-sizing:border-box;margin:0;min-width:0;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm">npm<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">javascript package manager</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-access">npm access<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Set access level on published packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-audit">npm audit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run a security audit</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bin">npm bin<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm bin folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bugs">npm bugs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bugs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-build">npm build<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Build a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bundle">npm bundle<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">REMOVED</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-cache">npm cache<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manipulates packages cache</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ci">npm ci<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-completion">npm completion<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Tab Completion for npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-config">npm config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage the npm configuration files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-dedupe">npm dedupe<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Reduce duplication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-deprecate">npm deprecate<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Deprecate a version of a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-docs">npm docs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Docs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-doctor">npm doctor<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check your environments</span></a></div><div class="css-l3rx45"><a aria-current="page" class="links__SidebarLink-sc-19vgq0o-3 iPgskl active-sidebar-link" href="../../cli-commands/npm-edit">npm edit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Edit an installed package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-fund">npm fund<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Retrieve funding information</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help">npm help<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Get help on npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help-search">npm help-search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search npm help documentation</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-hook">npm hook<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage registry hooks</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-init">npm init<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">create a package.json file</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install">npm install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-ci-test">npm install-ci-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-test">npm install-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install package(s) and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-link">npm link<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Symlink a package folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-logout">npm logout<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Log out of the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ls">npm ls<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">List installed packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-org">npm org<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-outdated">npm outdated<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check for outdated packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-owner">npm owner<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage package owners</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-pack">npm pack<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Create a tarball from a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ping">npm ping<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Ping npm registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prefix">npm prefix<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display prefix</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-profile">npm profile<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Change settings on your registry profile</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prune">npm prune<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove extraneous packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-publish">npm publish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Publish a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-rebuild">npm rebuild<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Rebuild a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-repo">npm repo<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Open package repository page in the browser</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-restart">npm restart<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Restart a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-root">npm root<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm root</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-run-script">npm run-script<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run arbitrary package scripts</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-search">npm search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search for packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-shrinkwrap">npm shrinkwrap<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Lock down dependency versions for publication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-star">npm star<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Mark your favorite packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stars">npm stars<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View packages marked as favorites</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-start">npm start<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Start a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stop">npm stop<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Stop a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-team">npm team<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage organization teams and team memberships</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-test">npm test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Test a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-token">npm token<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage your authentication tokens</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-uninstall">npm uninstall<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-unpublish">npm unpublish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package from the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-update">npm update<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Update a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-version">npm version<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bump a package version</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-view">npm view<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View registry info</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-whoami">npm whoami<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm username</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">configuring-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/folders">folders<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Folder Structures Used by npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/install">install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Download and install node and npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/npmrc">npmrc<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The npm config files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-lock-json">package-lock.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A manifestation of the manifest</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-locks">package-locks<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">An explanation of npm lockfiles</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-json">package.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Specifics of npm&#x27;s package.json handling</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/shrinkwrap-json">shrinkwrap.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A publishable lockfile</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">using-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/config">config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">More than you probably want to know about npm configuration</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/developers">developers<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Developer Guide</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/disputes">disputes<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Handling Module Name Disputes</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/orgs">orgs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Working with Teams &amp; Orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/registry">registry<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The JavaScript Package Registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/removal">removal<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Cleaning the Slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scope">scope<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Scoped packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scripts">scripts<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">How npm handles the &quot;scripts&quot; field</span></a></div></div></div></nav><style data-emotion-css="16vu25q">.css-16vu25q{box-sizing:border-box;margin:0;min-width:0;width:100%;}</style><div class="css-16vu25q"><div class="Page__Content-sc-4b62ym-0 gJQTGP documentation"><div><h1 id="npm-edit1" style="position:relative;"><a href="#npm-edit1" aria-label="npm edit1 permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>npm edit</h1>
<h2 id="edit-an-installed-package" style="position:relative;"><a href="#edit-an-installed-package" aria-label="edit an installed package permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Edit an installed package</h2>
<h3 id="synopsis" style="position:relative;"><a href="#synopsis" aria-label="synopsis permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Synopsis</h3>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash"><span class="token function">npm</span> edit <span class="token operator">&lt;</span>pkg<span class="token operator">></span><span class="token punctuation">[</span>/<span class="token operator">&lt;</span>subpkg<span class="token operator">></span><span class="token punctuation">..</span>.<span class="token punctuation">]</span></code></pre></div>
<h3 id="description" style="position:relative;"><a href="#description" aria-label="description permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Description</h3>
<p>Selects a (sub)dependency in the current
working directory and opens the package folder in the default editor
(or whatever you've configured as the npm <code class="language-text">editor</code> config -- see
<a href="npm-config"><code class="language-text">npm-config</code></a>.)</p>
<p>After it has been edited, the package is rebuilt so as to pick up any
changes in compiled packages.</p>
<p>For instance, you can do <code class="language-text">npm install connect</code> to install connect
into your package, and then <code class="language-text">npm edit connect</code> to make a few
changes to your locally installed copy.</p>
<h3 id="configuration" style="position:relative;"><a href="#configuration" aria-label="configuration permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Configuration</h3>
<h4 id="editor" style="position:relative;"><a href="#editor" aria-label="editor permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>editor</h4>
<ul>
<li>Default: <code class="language-text">EDITOR</code> environment variable if set, or <code class="language-text">&quot;vi&quot;</code> on Posix,
or <code class="language-text">&quot;notepad&quot;</code> on Windows.</li>
<li>Type: path</li>
</ul>
<p>The command to run for <code class="language-text">npm edit</code> or <code class="language-text">npm config edit</code>.</p>
<h3 id="see-also" style="position:relative;"><a href="#see-also" aria-label="see also permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>See Also</h3>
<ul>
<li><a href="../../configuring-npm/folders">npm folders</a></li>
<li><a href="../../cli-commands/explore">npm explore</a></li>
<li><a href="../../cli-commands/install">npm install</a></li>
<li><a href="../../cli-commands/config">npm config</a></li>
<li><a href="../../configuring-npm/npmrc">npmrc</a></li>
</ul></div><div class="FoundTypo__Container-sc-1e373sc-0 fMOzaj"><p><span role="img" aria-label="eyes-emoji">👀</span> Found a typo? <a href="https://github.com/npm/cli/">Let us know!</a></p><p>The current stable version of npm is <a href="https://github.com/npm/cli/">here</a>. To upgrade, run: <code class="language-text">npm install npm@latest -g</code></p><p>To report bugs or submit feature requests for the docs, please post <a href="https://npm.community/c/support/docs-needed">here</a>. Submit npm issues <a href="https://npm.community/c/bugs">here.</a></p></div><script>
          var anchors = document.querySelectorAll(".sidebar a, .documentation a")
          Array.prototype.slice.call(anchors).map(function(el) {
            if (el.href.match(/file:\/\//)) {
              el.href = el.href + "/index.html"
            }
          })
          </script></div></div></div></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/cli-commands/npm-edit";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-041f7e4f56e7debd8d98.js"],"component---src-templates-page-js":["/component---src-templates-page-js-7faf8ceb01991e80d244.js"],"component---src-pages-404-js":["/component---src-pages-404-js-6c8c4e2e908a7101a231.js"],"component---src-pages-index-js":["/component---src-pages-index-js-6b93f80c513be8d7330c.js"]};/*]]>*/</script><script src="../../component---src-templates-page-js-7faf8ceb01991e80d244.js" async=""></script><script src="../../app-041f7e4f56e7debd8d98.js" async=""></script><script src="../../commons-4df35f6dbd2fdc25d817.js" async=""></script><script src="../../styles-de5e304580bcba768a01.js" async=""></script><script src="../../webpack-runtime-b622568e0ef6e093f777.js" async=""></script></body></html>