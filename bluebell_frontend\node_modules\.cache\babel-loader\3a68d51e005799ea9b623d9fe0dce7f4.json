{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Login.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Login.vue", "mtime": 1598764894000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiTG9naW4iLAogIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICB1c2VybmFtZTogIiIsCiAgICAgIHBhc3N3b3JkOiAiIiwKICAgICAgc3VibWl0dGVkOiBmYWxzZQogICAgfTsKICB9LAogIGNvbXB1dGVkOiB7fSwKICBjcmVhdGVkKCkge30sCiAgbWV0aG9kczogewogICAgc3VibWl0KCkgewogICAgICB0aGlzLiRheGlvcyh7CiAgICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgICAgdXJsOiAnL2xvZ2luJywKICAgICAgICBkYXRhOiBKU09OLnN0cmluZ2lmeSh7CiAgICAgICAgICB1c2VybmFtZTogdGhpcy51c2VybmFtZSwKICAgICAgICAgIHBhc3N3b3JkOiB0aGlzLnBhc3N3b3JkCiAgICAgICAgfSkKICAgICAgfSkudGhlbihyZXMgPT4gewogICAgICAgIGNvbnNvbGUubG9nKHJlcy5kYXRhKTsKICAgICAgICBpZiAocmVzLmNvZGUgPT0gMTAwMCkgewogICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oImxvZ2luUmVzdWx0IiwgSlNPTi5zdHJpbmdpZnkocmVzLmRhdGEpKTsKICAgICAgICAgIHRoaXMuJHN0b3JlLmNvbW1pdCgibG9naW4iLCByZXMuZGF0YSk7CiAgICAgICAgICB0aGlzLiRyb3V0ZXIucHVzaCh7CiAgICAgICAgICAgIHBhdGg6IHRoaXMucmVkaXJlY3QgfHwgJy8nCiAgICAgICAgICB9KTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5sb2cocmVzLm1zZyk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgY29uc29sZS5sb2coZXJyb3IpOwogICAgICB9KTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "data", "username", "password", "submitted", "computed", "created", "methods", "submit", "$axios", "method", "url", "JSON", "stringify", "then", "res", "console", "log", "code", "localStorage", "setItem", "$store", "commit", "$router", "push", "path", "redirect", "msg", "catch", "error"], "sources": ["src/views/Login.vue"], "sourcesContent": ["<template>\n  <div class=\"main\">\n    <div class=\"container\">\n      <h2 class=\"form-title\">登录</h2>\n      <div class=\"form-group\">\n        <label for=\"name\">用户名</label>\n        <input type=\"text\" class=\"form-control\" v-model=\"username\" name=\"name\" id=\"name\" placeholder=\"用户名\" />\n      </div>\n      <div class=\"form-group\">\n        <label for=\"pass\">密码</label>\n        <input type=\"password\" class=\"form-control\" v-model=\"password\"  name=\"pass\" id=\"pass\" placeholder=\"密码\" />\n      </div>\n      <div class=\"form-btn\">\n        <button type=\"button\" class=\"btn btn-info\" @click=\"submit\">提交</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n\tname: \"Login\",\n\tdata() {\n\t\treturn {\n\t\t\tusername: \"\",\n\t\t\tpassword: \"\",\n\t\t\tsubmitted: false\n\t\t};\n\t},\n\tcomputed: {\n\t},\n\tcreated() {\n\n\t},\n\tmethods: {\n\t\tsubmit() {\n\t\t\tthis.$axios({\n\t\t\t\tmethod: 'post',\n\t\t\t\turl:'/login',\n\t\t\t\tdata: JSON.stringify({\n\t\t\t\t\tusername: this.username,\n\t\t\t\t\tpassword: this.password\n\t\t\t\t})\n\t\t\t}).then((res)=>{\n\t\t\t\tconsole.log(res.data)\n\t\t\t\tif (res.code == 1000) {\n          localStorage.setItem(\"loginResult\", JSON.stringify(res.data));\n          this.$store.commit(\"login\", res.data);\n          this.$router.push({path: this.redirect || '/' })\n\t\t\t\t} else {\n\t\t\t\t\tconsole.log(res.msg)\n\t\t\t\t}\n\t\t\t}).catch((error)=>{\n\t\t\t\tconsole.log(error)\n\t\t\t})\n\t\t}\n\t}\n};\n</script>\n<style lang=\"less\" scoped>\n.main {\n  background: #f8f8f8;\n  padding: 150px 0;\n  .container {\n    width: 600px;\n    background: #fff;\n    margin: 0 auto;\n    max-width: 1200px;\n    padding: 20px;\n    .form-title {\n      margin-bottom: 33px;\n      text-align: center;\n    }\n    .form-group {\n      margin: 15px;\n      label {\n        display: inline-block;\n        max-width: 100%;\n        margin-bottom: 5px;\n        font-weight: 700;\n      }\n      .form-control {\n        display: block;\n        width: 100%;\n        height: 34px;\n        padding: 6px 12px;\n        font-size: 14px;\n        line-height: 1.42857143;\n        color: #555;\n        background-color: #fff;\n        background-image: none;\n        border: 1px solid #ccc;\n        border-radius: 4px;\n      }\n    }\n    .form-btn {\n      display: flex;\n      justify-content: center;\n      .btn {\n        padding: 6px 20px;\n        font-size: 18px;\n        line-height: 1.3333333;\n        border-radius: 6px;\n        display: inline-block;\n        margin-bottom: 0;\n        font-weight: 400;\n        text-align: center;\n        white-space: nowrap;\n        vertical-align: middle;\n        -ms-touch-action: manipulation;\n        touch-action: manipulation;\n        cursor: pointer;\n        border: 1px solid transparent;\n      }\n      .btn-info {\n        color: #fff;\n        background-color: #5bc0de;\n        border-color: #46b8da;\n      }\n    }\n  }\n}\n</style>"], "mappings": ";AAoBA;EACAA,IAAA;EACAC,KAAA;IACA;MACAC,QAAA;MACAC,QAAA;MACAC,SAAA;IACA;EACA;EACAC,QAAA,GACA;EACAC,QAAA,GAEA;EACAC,OAAA;IACAC,OAAA;MACA,KAAAC,MAAA;QACAC,MAAA;QACAC,GAAA;QACAV,IAAA,EAAAW,IAAA,CAAAC,SAAA;UACAX,QAAA,OAAAA,QAAA;UACAC,QAAA,OAAAA;QACA;MACA,GAAAW,IAAA,CAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,CAAAF,GAAA,CAAAd,IAAA;QACA,IAAAc,GAAA,CAAAG,IAAA;UACAC,YAAA,CAAAC,OAAA,gBAAAR,IAAA,CAAAC,SAAA,CAAAE,GAAA,CAAAd,IAAA;UACA,KAAAoB,MAAA,CAAAC,MAAA,UAAAP,GAAA,CAAAd,IAAA;UACA,KAAAsB,OAAA,CAAAC,IAAA;YAAAC,IAAA,OAAAC,QAAA;UAAA;QACA;UACAV,OAAA,CAAAC,GAAA,CAAAF,GAAA,CAAAY,GAAA;QACA;MACA,GAAAC,KAAA,CAAAC,KAAA;QACAb,OAAA,CAAAC,GAAA,CAAAY,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}