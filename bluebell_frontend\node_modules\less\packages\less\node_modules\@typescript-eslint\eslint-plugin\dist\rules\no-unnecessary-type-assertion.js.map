{"version": 3, "file": "no-unnecessary-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-unnecessary-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,qCAUiB;AACjB,4DAA4B;AAC5B,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,qEAAqE;YACvE,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,oBAAoB,EAClB,oFAAoF;YACtF,uBAAuB,EACrB,+FAA+F;SAClG;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,aAAa,EAAE;wBACb,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;yBACf;qBACF;iBACF;aACF;SACF;QACD,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,CAAC,EAAE,CAAC;IACpB,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QACxD,MAAM,eAAe,GAAG,cAAc,CAAC,OAAO,CAAC,kBAAkB,EAAE,CAAC;QAEpE;;;WAGG;QACH,SAAS,gBAAgB,CAAC,IAAmB;YAC3C,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;YAExC,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;gBAC3B,OAAO,KAAK,CAAC;aACd;YACD,IAAI,CAAC,GAAG,CAAC,CAAC;YAEV,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,MAAM,IAAI,GAAG,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBAEhC,IAAI,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;oBACtB,IAAI,CAAC,KAAK,CAAC,EAAE;wBACX,0DAA0D;wBAC1D,OAAO,KAAK,CAAC;qBACd;oBACD,MAAM;iBACP;aACF;YACD,OAAO,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACjC,IAAI,MAAM,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;oBACtD,OAAO,KAAK,CAAC,CAAC,iEAAiE;iBAChF;aACF;YACD,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;WAIG;QACH,SAAS,iBAAiB,CACxB,OAAuB,EACvB,IAAmB;YAEnB,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC3B,IAAI,CAAC,MAAM,EAAE;gBACX,OAAO;aACR;YAED,IAAI,0BAAgB,CAAC,MAAM,CAAC,IAAI,yBAAe,CAAC,MAAM,CAAC,EAAE;gBACvD,IAAI,IAAI,KAAK,MAAM,CAAC,UAAU,EAAE;oBAC9B,2CAA2C;oBAC3C,OAAO;iBACR;aACF;iBAAM,IACL,+BAAqB,CAAC,MAAM,CAAC;gBAC7B,+BAAqB,CAAC,MAAM,CAAC;gBAC7B,gCAAsB,CAAC,MAAM,CAAC,EAC9B;gBACA,OAAO,MAAM,CAAC,IAAI;oBAChB,CAAC,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC;oBAC1C,CAAC,CAAC,SAAS,CAAC;aACf;iBAAM,IACL,CAAC,CAAC,oBAAE,CAAC,UAAU,CAAC,YAAY,EAAE,oBAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,QAAQ,CACjE,MAAM,CAAC,IAAI,CACZ,EACD;gBACA,oEAAoE;gBACpE,OAAO;aACR;YACD,2CAA2C;YAE3C,OAAO,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;QACzC,CAAC;QAED;;WAEG;QACH,SAAS,4BAA4B,CAAC,IAAmB;YACvD,MAAM,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YACvD,IAAI,CAAC,WAAW,EAAE;gBAChB,+EAA+E;gBAC/E,OAAO,IAAI,CAAC;aACb;YAED;YACE,iEAAiE;YACjE,uCAA6B,CAAC,eAAe,EAAE,kBAAkB,CAAC;gBAClE,2DAA2D;gBAC3D,sEAAsE;gBACtE,+BAAqB,CAAC,WAAW,CAAC;gBAClC,2BAA2B;gBAC3B,WAAW,CAAC,WAAW,KAAK,SAAS;gBACrC,WAAW,CAAC,gBAAgB,KAAK,SAAS;gBAC1C,WAAW,CAAC,IAAI,KAAK,SAAS,EAC9B;gBACA,kEAAkE;gBAClE,MAAM,eAAe,GAAG,OAAO,CAAC,mBAAmB,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;gBACtE,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;gBAC9D,IAAI,eAAe,KAAK,IAAI,EAAE;oBAC5B,iDAAiD;oBACjD,6FAA6F;oBAC7F,EAAE;oBACF,6CAA6C;oBAC7C,uDAAuD;oBACvD,OAAO,IAAI,CAAC;iBACb;aACF;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,mBAAmB,CAAC,IAAI;gBACtB,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAE3D,IAAI,CAAC,CAAC;gBACR,MAAM,IAAI,GAAG,IAAI,CAAC,4BAA4B,CAC5C,OAAO,EACP,YAAY,CAAC,UAAU,CACxB,CAAC;gBAEF,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAC9B,IAAI,4BAA4B,CAAC,YAAY,CAAC,UAAU,CAAC,EAAE;wBACzD,OAAO;qBACR;oBAED,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,OAAO,KAAK,CAAC,WAAW,CAAC;gCACvB,YAAY,CAAC,UAAU,CAAC,GAAG;gCAC3B,YAAY,CAAC,GAAG;6BACjB,CAAC,CAAC;wBACL,CAAC;qBACF,CAAC,CAAC;iBACJ;qBAAM;oBACL,+BAA+B;oBAC/B,+EAA+E;oBAE/E,MAAM,cAAc,GAAG,iBAAiB,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC;oBAChE,IAAI,cAAc,EAAE;wBAClB,kFAAkF;wBAClF,sCAAsC;wBACtC,MAAM,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAC9C,IAAI,EACJ,oBAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CACzC,IAAI,EACJ,oBAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;wBAEF,MAAM,+BAA+B,GAAG,IAAI,CAAC,aAAa,CACxD,cAAc,EACd,oBAAE,CAAC,SAAS,CAAC,SAAS,CACvB,CAAC;wBACF,MAAM,0BAA0B,GAAG,IAAI,CAAC,aAAa,CACnD,cAAc,EACd,oBAAE,CAAC,SAAS,CAAC,IAAI,CAClB,CAAC;wBACF,IACE,CAAC,qBAAqB,IAAI,+BAA+B,CAAC;4BAC1D,CAAC,gBAAgB,IAAI,0BAA0B,CAAC,EAChD;4BACA,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI;gCACJ,SAAS,EAAE,yBAAyB;gCACpC,GAAG,CAAC,KAAK;oCACP,OAAO,KAAK,CAAC,WAAW,CAAC;wCACvB,YAAY,CAAC,UAAU,CAAC,GAAG;wCAC3B,YAAY,CAAC,GAAG;qCACjB,CAAC,CAAC;gCACL,CAAC;6BACF,CAAC,CAAC;yBACJ;qBACF;iBACF;YACH,CAAC;YACD,iCAAiC,CAC/B,IAAwD;gBAExD,IACE,OAAO;oBACP,OAAO,CAAC,aAAa;oBACrB,OAAO,CAAC,aAAa,CAAC,OAAO,CAC3B,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,CACxC,KAAK,CAAC,CAAC,EACR;oBACA,OAAO;iBACR;gBAED,MAAM,YAAY,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAE3D,IAAI,CAAC,CAAC;gBACR,MAAM,QAAQ,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAEzD,IACE,uBAAa,CAAC,QAAQ,EAAE,oBAAE,CAAC,SAAS,CAAC,OAAO,CAAC;oBAC7C,CAAC,sBAAY,CAAC,QAAQ,CAAC;wBACrB,CAAC,yBAAe,CAAC,QAAQ,EAAE,oBAAE,CAAC,WAAW,CAAC,KAAK,CAAC;4BAC9C,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAC,EAChC;oBACA,mEAAmE;oBACnE,+DAA+D;oBAC/D,OAAO;iBACR;gBAED,MAAM,UAAU,GAAG,OAAO,CAAC,iBAAiB,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;gBAEtE,IAAI,UAAU,KAAK,QAAQ,EAAE;oBAC3B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,sBAAsB;wBACjC,GAAG,CAAC,KAAK;4BACP,OAAO,YAAY,CAAC,IAAI,KAAK,oBAAE,CAAC,UAAU,CAAC,uBAAuB;gCAChE,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;oCAChB,YAAY,CAAC,QAAQ,EAAE;oCACvB,YAAY,CAAC,UAAU,CAAC,QAAQ,EAAE;iCACnC,CAAC;gCACJ,CAAC,CAAC,KAAK,CAAC,WAAW,CAAC;oCAChB,YAAY,CAAC,UAAU,CAAC,GAAG;oCAC3B,YAAY,CAAC,GAAG;iCACjB,CAAC,CAAC;wBACT,CAAC;qBACF,CAAC,CAAC;iBACJ;gBAED,qDAAqD;YACvD,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}