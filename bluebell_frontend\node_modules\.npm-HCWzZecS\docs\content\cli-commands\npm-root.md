---
section: cli-commands 
title: npm-root
description: Display npm root
---

# npm-root(1)

## Display npm root

### Synopsis
```bash
npm root [-g]
```

### Description

Print the effective `node_modules` folder to standard out.

### See Also

* [npm prefix](/cli-commands/prefix)
* [npm bin](/cli-commands/bin)
* [npm folders](/configuring-npm/folders)
* [npm config](/cli-commands/config)
* [npmrc](/configuring-npm/npmrc)
