@fat: 0;
@cloudhead: "#000000";

.nav {
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity = 20);
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=@fat);
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="#333333", endColorstr=@cloudhead, GradientType=@fat);
}
.evalTest(@arg) {
  filter: progid:DXImageTransform.Microsoft.Alpha(opacity=@arg);
}
.evalTest1 {
  .evalTest(30);
  .evalTest(5);
}