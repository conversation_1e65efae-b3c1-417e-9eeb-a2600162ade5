{"version": 3, "file": "member-ordering.js", "sourceRoot": "", "sources": ["../../src/rules/member-ordering.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAchC,MAAM,cAAc,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC,CAAC,MAAM,CAC9D,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE;IACZ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAEf,CAAC,QAAQ,EAAE,WAAW,EAAE,SAAS,CAAC,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;QACzD,GAAG,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,sBAAsB;QAE5D,IAAI,IAAI,KAAK,aAAa,EAAE;YAC1B,4DAA4D;YAC5D,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;gBACrC,IAAI,GAAG,CAAC,OAAO,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;oBAC1C,GAAG,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;iBAC9B;gBAED,GAAG,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;SACJ;IACH,CAAC,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC;AACb,CAAC,EACD,EAAE,CACH,CAAC;AAEF,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,+CAA+C;YAC5D,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,cAAc,EACZ,qEAAqE;SACxE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,OAAO,EAAE;wBACP,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,cAAc;iCACrB;6BACF;yBACF;qBACF;oBACD,OAAO,EAAE;wBACP,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,cAAc;iCACrB;6BACF;yBACF;qBACF;oBACD,gBAAgB,EAAE;wBAChB,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,cAAc;iCACrB;6BACF;yBACF;qBACF;oBACD,UAAU,EAAE;wBACV,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;iCACzC;6BACF;yBACF;qBACF;oBACD,YAAY,EAAE;wBACZ,KAAK,EAAE;4BACL;gCACE,IAAI,EAAE,CAAC,OAAO,CAAC;6BAChB;4BACD;gCACE,IAAI,EAAE,OAAO;gCACb,KAAK,EAAE;oCACL,IAAI,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,CAAC;iCACzC;6BACF;yBACF;qBACF;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,OAAO,EAAE;gBACP,qBAAqB;gBACrB,wBAAwB;gBACxB,sBAAsB;gBAEtB,uBAAuB;gBACvB,0BAA0B;gBAC1B,wBAAwB;gBAExB,cAAc;gBACd,iBAAiB;gBACjB,eAAe;gBAEf,cAAc;gBACd,gBAAgB;gBAEhB,OAAO;gBAEP,aAAa;gBAEb,sBAAsB;gBACtB,yBAAyB;gBACzB,uBAAuB;gBAEvB,wBAAwB;gBACxB,2BAA2B;gBAC3B,yBAAyB;gBAEzB,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;gBAEhB,eAAe;gBACf,iBAAiB;gBAEjB,QAAQ;aACT;SACF;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAE3C,MAAM,mBAAmB,GAAG;YAC1B,mCAAc,CAAC,kBAAkB;YACjC,mCAAc,CAAC,uBAAuB;SACvC,CAAC;QAEF;;;WAGG;QACH,SAAS,WAAW,CAClB,IAAkD;YAElD,+CAA+C;YAC/C,qCAAqC;YACrC,4CAA4C;YAC5C,+CAA+C;YAC/C,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,mCAAc,CAAC,gBAAgB;oBAClC,OAAO,IAAI,CAAC,IAAI,CAAC;gBACnB,KAAK,mCAAc,CAAC,iBAAiB;oBACnC,OAAO,QAAQ,CAAC;gBAClB,KAAK,mCAAc,CAAC,+BAA+B;oBACjD,OAAO,aAAa,CAAC;gBACvB,KAAK,mCAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,KAAK,IAAI,mBAAmB,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;wBACpE,CAAC,CAAC,QAAQ;wBACV,CAAC,CAAC,OAAO,CAAC;gBACd,KAAK,mCAAc,CAAC,mBAAmB;oBACrC,OAAO,OAAO,CAAC;gBACjB;oBACE,OAAO,IAAI,CAAC;aACf;QACH,CAAC;QAED;;;WAGG;QACH,SAAS,aAAa,CACpB,IAAkD;YAElD,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,mCAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,mCAAc,CAAC,iBAAiB,CAAC;gBACtC,KAAK,mCAAc,CAAC,aAAa;oBAC/B,OAAO,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBAChD,KAAK,mCAAc,CAAC,gBAAgB;oBAClC,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa;wBAChC,CAAC,CAAC,aAAa;wBACf,CAAC,CAAC,IAAI,CAAC,sBAAsB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;gBACpD,KAAK,mCAAc,CAAC,+BAA+B;oBACjD,OAAO,KAAK,CAAC;gBACf;oBACE,OAAO,IAAI,CAAC;aACf;QACH,CAAC;QAED;;;;;;;;;;;WAWG;QACH,SAAS,YAAY,CAAC,WAAqB,EAAE,KAAe;YAC1D,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC;YACd,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,iCAAiC;YAEpE,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;gBACtC,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,EAAG,CAAC,CAAC;aACtC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;;;WAKG;QACH,SAAS,OAAO,CACd,IAAkD,EAClD,KAAe,EACf,iBAA0B;YAE1B,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;YAC/B,IAAI,IAAI,KAAK,IAAI,EAAE;gBACjB,uDAAuD;gBACvD,OAAO,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;aACzB;YAED,MAAM,KAAK,GAAG,QAAQ,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC;YACtE,MAAM,aAAa,GACjB,eAAe,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa;gBAC3C,CAAC,CAAC,IAAI,CAAC,aAAa;gBACpB,CAAC,CAAC,QAAQ,CAAC;YAEf,MAAM,WAAW,GAAG,EAAE,CAAC;YAEvB,IAAI,iBAAiB,EAAE;gBACrB,IAAI,IAAI,KAAK,aAAa,EAAE;oBAC1B,6BAA6B;oBAC7B,WAAW,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;oBACtD,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,CAAC;iBACtC;gBAED,WAAW,CAAC,IAAI,CAAC,GAAG,aAAa,IAAI,IAAI,EAAE,CAAC,CAAC;aAC9C;YAED,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvB,OAAO,YAAY,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;QAC1C,CAAC;QAED;;;;;;;;;;;;;;;;;;WAkBG;QACH,SAAS,aAAa,CACpB,KAAe,EACf,MAAc,EACd,KAAe;YAEf,IAAI,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAErC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;gBACnB,IAAI,IAAI,GAAG,MAAM,EAAE;oBACjB,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;iBACjC;YACH,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC;QAED;;;;;;WAMG;QACH,SAAS,oBAAoB,CAC3B,OAAyD,EACzD,KAAkB,EAClB,iBAA0B;YAE1B,IAAI,OAAO,IAAI,KAAK,KAAK,OAAO,EAAE;gBAChC,MAAM,aAAa,GAAa,EAAE,CAAC;gBAEnC,iDAAiD;gBACjD,OAAO,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;oBACvB,MAAM,IAAI,GAAG,OAAO,CAAC,MAAM,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC;oBAEvD,IAAI,IAAI,KAAK,CAAC,CAAC,EAAE;wBACf,IAAI,IAAI,GAAG,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,EAAE;4BAClD,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,MAAM;gCACZ,SAAS,EAAE,gBAAgB;gCAC3B,IAAI,EAAE;oCACJ,IAAI,EAAE,aAAa,CAAC,MAAM,CAAC;oCAC3B,IAAI,EAAE,aAAa,CAAC,aAAa,EAAE,IAAI,EAAE,KAAK,CAAC;iCAChD;6BACF,CAAC,CAAC;yBACJ;6BAAM;4BACL,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;yBAC1B;qBACF;gBACH,CAAC,CAAC,CAAC;aACJ;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAAI;gBACnB,oBAAoB,CAClB,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAQ,EACnC,IAAI,CACL,CAAC;YACJ,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,oBAAoB,CAClB,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,OAAO,CAAC,gBAAgB,IAAI,OAAO,CAAC,OAAQ,EAC5C,IAAI,CACL,CAAC;YACJ,CAAC;YACD,sBAAsB,CAAC,IAAI;gBACzB,oBAAoB,CAClB,IAAI,CAAC,IAAI,CAAC,IAAI,EACd,OAAO,CAAC,UAAU,IAAI,OAAO,CAAC,OAAQ,EACtC,KAAK,CACN,CAAC;YACJ,CAAC;YACD,aAAa,CAAC,IAAI;gBAChB,oBAAoB,CAClB,IAAI,CAAC,OAAO,EACZ,OAAO,CAAC,YAAY,IAAI,OAAO,CAAC,OAAQ,EACxC,KAAK,CACN,CAAC;YACJ,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}