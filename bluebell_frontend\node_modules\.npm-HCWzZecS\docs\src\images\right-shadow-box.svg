<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 52 53" style="enable-background:new 0 0 52 53;" xml:space="preserve">
<style type="text/css">
	.st0{opacity:0.3;fill:url(#SVGID_1_);}
	.st1{opacity:0.3;fill:url(#SVGID_2_);}
	.st2{opacity:0.1;fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st3{opacity:0.3;fill:#E8D9D9;}
	.st4{opacity:0.5;fill:url(#SVGID_3_);}
	.st5{opacity:0.3;fill:url(#SVGID_4_);}
	.st6{opacity:0.3;fill:url(#SVGID_5_);}
	.st7{fill:#F6D2C9;}
	.st8{fill:#FFFFFF;}
	.st9{fill:#FF2EDD;}
	.st10{fill:none;stroke:url(#SVGID_6_);stroke-width:3;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#B3B3B3;stroke-width:0.75;stroke-miterlimit:10;}
	.st12{fill:none;stroke:url(#SVGID_7_);stroke-miterlimit:10;}
	.st13{fill:none;stroke:url(#SVGID_8_);stroke-width:3;stroke-miterlimit:10;}
	.st14{fill:#FB3B49;}
	.st15{fill:url(#SVGID_9_);}
	.st16{opacity:0.7;}
	.st17{fill:url(#SVGID_10_);}
	.st18{fill:#333333;}
	.st19{opacity:0.2;fill:#FB3B49;}
	.st20{opacity:0.3;fill:url(#SVGID_11_);}
	.st21{fill:none;stroke:url(#SVGID_12_);stroke-width:3;stroke-miterlimit:10;}
	.st22{fill:url(#SVGID_13_);}
	.st23{fill:url(#SVGID_14_);}
	.st24{fill:none;stroke:url(#SVGID_15_);stroke-width:10.069;stroke-miterlimit:10;}
	.st25{fill:none;stroke:url(#SVGID_16_);stroke-width:10.069;stroke-miterlimit:10;}
	.st26{fill:none;stroke:url(#SVGID_17_);stroke-width:3;stroke-miterlimit:10;}
	.st27{clip-path:url(#XMLID_6_);}
	.st28{opacity:0.3;fill:url(#SVGID_18_);}
	.st29{fill:none;stroke:url(#SVGID_19_);stroke-width:3;stroke-miterlimit:10;}
	.st30{fill:url(#SVGID_20_);}
	.st31{fill:url(#SVGID_21_);}
	.st32{fill:none;stroke:url(#SVGID_22_);stroke-width:3;stroke-miterlimit:10;}
	.st33{opacity:0.8;}
	.st34{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st35{fill:#7C2EDD;}
	.st36{fill:none;stroke:url(#SVGID_23_);stroke-width:3;stroke-miterlimit:10;}
	.st37{fill:none;stroke:url(#SVGID_24_);stroke-width:3;stroke-miterlimit:10;}
	.st38{fill:none;stroke:#B3B3B3;stroke-miterlimit:10;}
	.st39{fill:none;stroke:#B3B3B3;stroke-width:1.1228;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#B3B3B3;stroke-width:1.2168;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#333333;stroke-miterlimit:10;}
	.st42{fill:url(#SVGID_25_);}
	.st43{fill:url(#SVGID_26_);}
	.st44{fill:url(#SVGID_27_);}
	.st45{fill:url(#SVGID_28_);}
	.st46{fill:#231F20;}
	.st47{fill:none;}
	.st48{opacity:0.6;fill:url(#SVGID_29_);}
	.st49{fill:none;stroke:url(#SVGID_30_);stroke-miterlimit:10;}
	.st50{fill:none;stroke:#B3B3B3;stroke-width:0.7877;stroke-miterlimit:10;}
	.st51{opacity:0.9;}
	.st52{opacity:0.1;}
	.st53{fill:none;stroke:#808080;stroke-miterlimit:10;}
	.st54{opacity:5.000000e-02;}
	.st55{fill:none;stroke:#FF00FF;stroke-miterlimit:10;}
	.st56{fill:url(#SVGID_31_);}
	.st57{fill:url(#SVGID_32_);}
	.st58{opacity:0.19;fill:url(#SVGID_33_);}
	.st59{fill:none;stroke:url(#SVGID_34_);stroke-width:3;stroke-miterlimit:10;}
	.st60{opacity:0.19;fill:url(#SVGID_35_);}
	.st61{opacity:0.5;fill:#FFFFFF;}
	.st62{fill:none;stroke:#333333;stroke-width:2;stroke-miterlimit:10;}
	.st63{opacity:0.19;fill:url(#SVGID_36_);}
	.st64{fill:#333333;stroke:#333333;stroke-miterlimit:10;}
	.st65{opacity:0.19;fill:url(#SVGID_37_);}
	.st66{fill:none;stroke:#333333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st67{fill:none;stroke:url(#SVGID_38_);stroke-width:3;stroke-miterlimit:10;}
	.st68{opacity:0.6;fill:url(#SVGID_39_);}
	.st69{opacity:0.4;fill:url(#SVGID_40_);}
	.st70{opacity:0.4;fill:url(#SVGID_41_);}
	.st71{opacity:0.4;fill:url(#SVGID_42_);}
	.st72{fill:#F2F2F2;}
	.st73{opacity:0.4;fill:url(#SVGID_43_);}
	.st74{fill:#413844;stroke:#223839;stroke-miterlimit:10;}
	
		.st75{fill:#FFFFFF;fill-opacity:0.5;stroke:#223839;stroke-width:1.802;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st76{fill:url(#SVGID_44_);}
	.st77{fill:url(#SVGID_45_);}
	.st78{fill:url(#SVGID_46_);}
	.st79{fill:url(#SVGID_47_);}
	.st80{fill:url(#SVGID_48_);}
	.st81{fill:none;stroke:#223839;stroke-width:2;stroke-miterlimit:10;}
	.st82{fill:url(#SVGID_49_);}
	.st83{fill:url(#SVGID_50_);}
	.st84{fill:url(#SVGID_51_);}
	.st85{fill:url(#SVGID_52_);}
	.st86{fill:url(#SVGID_53_);}
	.st87{fill:url(#SVGID_54_);}
	.st88{fill:url(#SVGID_55_);}
	.st89{fill:url(#SVGID_56_);}
	.st90{fill:url(#SVGID_57_);}
	.st91{fill:url(#SVGID_58_);}
	.st92{fill:#FF00FF;}
	.st93{fill:#7457D9;}
	.st94{opacity:0.3;fill:url(#SVGID_59_);}
	.st95{fill:none;stroke:url(#SVGID_60_);stroke-width:3;stroke-miterlimit:10;}
	.st96{fill:#333333;stroke:#333333;stroke-width:1.0718;stroke-miterlimit:10;}
	.st97{fill:none;stroke:url(#SVGID_61_);stroke-miterlimit:10;}
	.st98{fill:#413844;}
	.st99{fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st100{opacity:0.6;fill:url(#SVGID_62_);}
	.st101{opacity:0.4;fill:url(#SVGID_63_);}
	.st102{opacity:0.4;fill:url(#SVGID_64_);}
	.st103{opacity:0.4;fill:url(#SVGID_65_);}
	.st104{opacity:0.4;fill:url(#SVGID_66_);}
	.st105{fill:url(#SVGID_67_);}
	.st106{fill:url(#SVGID_68_);}
	.st107{fill:url(#SVGID_69_);}
	.st108{fill:url(#SVGID_70_);}
	.st109{fill:url(#SVGID_71_);}
	.st110{fill:url(#SVGID_72_);}
	.st111{fill:url(#SVGID_73_);}
	.st112{fill:url(#SVGID_74_);}
	.st113{fill:url(#SVGID_75_);}
	.st114{fill:url(#SVGID_76_);}
	.st115{fill:url(#SVGID_77_);}
	.st116{fill:url(#SVGID_78_);}
	.st117{fill:url(#SVGID_79_);}
	.st118{fill:url(#SVGID_80_);}
	.st119{fill:url(#SVGID_81_);}
	.st120{fill:none;stroke:#FF00FF;stroke-miterlimit:10;stroke-dasharray:40,2;}
	.st121{fill:url(#SVGID_82_);stroke:url(#SVGID_83_);stroke-width:0.742;stroke-miterlimit:10;}
	.st122{fill:url(#SVGID_84_);stroke:url(#SVGID_85_);stroke-width:0.742;stroke-miterlimit:10;}
	.st123{fill:url(#SVGID_86_);stroke:url(#SVGID_87_);stroke-width:0.742;stroke-miterlimit:10;}
	.st124{fill:url(#SVGID_88_);}
	.st125{fill:url(#SVGID_89_);}
	.st126{fill:url(#SVGID_90_);}
	.st127{opacity:0.9;fill:url(#SVGID_91_);}
	.st128{fill:none;stroke:url(#SVGID_92_);stroke-width:3;stroke-miterlimit:10;}
	.st129{opacity:0.1;fill:none;stroke:#4D4D4D;stroke-miterlimit:10;}
	.st130{fill:#ED1C24;}
	.st131{fill:#666666;}
	.st132{opacity:0.2;fill:#D4BEB8;}
	.st133{fill:none;stroke:#FB3B49;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st134{opacity:8.000000e-02;fill:#CC33FF;}
	.st135{fill:#CC33FF;}
	.st136{fill:#AF2AF7;}
	.st137{opacity:0.3;fill:url(#SVGID_93_);}
	.st138{fill:none;stroke:#F2F2F2;stroke-miterlimit:10;}
	.st139{fill:url(#SVGID_94_);stroke:url(#SVGID_95_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st140{fill:url(#SVGID_96_);stroke:url(#SVGID_97_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st141{fill:url(#SVGID_98_);stroke:url(#SVGID_99_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st142{fill:none;stroke:#FB3B49;stroke-miterlimit:10;}
	.st143{fill:url(#SVGID_100_);stroke:url(#SVGID_101_);stroke-width:0.742;stroke-miterlimit:10;}
	.st144{fill:url(#SVGID_102_);}
	.st145{fill:url(#SVGID_103_);}
	.st146{fill:none;stroke:url(#SVGID_104_);stroke-miterlimit:10;}
	.st147{fill:url(#SVGID_105_);stroke:url(#SVGID_106_);stroke-width:0.742;stroke-miterlimit:10;}
	.st148{fill:url(#SVGID_107_);stroke:url(#SVGID_108_);stroke-width:0.742;stroke-miterlimit:10;}
	.st149{fill:url(#SVGID_109_);stroke:url(#SVGID_110_);stroke-width:0.742;stroke-miterlimit:10;}
	.st150{fill:none;stroke:#FF00FF;stroke-width:0.6009;stroke-miterlimit:10;stroke-dasharray:24.0344,1.2017;}
	.st151{fill:none;stroke:#FB3B49;stroke-width:0.6009;stroke-miterlimit:10;}
	.st152{fill:url(#SVGID_111_);stroke:url(#SVGID_112_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st153{fill:url(#SVGID_113_);}
	.st154{fill:url(#SVGID_114_);}
	.st155{fill:url(#SVGID_115_);}
	.st156{fill:url(#SVGID_116_);}
	.st157{fill:url(#SVGID_117_);}
	.st158{fill:url(#SVGID_118_);stroke:url(#SVGID_119_);stroke-width:0.742;stroke-miterlimit:10;}
	.st159{fill:url(#SVGID_120_);stroke:url(#SVGID_121_);stroke-width:0.742;stroke-miterlimit:10;}
	.st160{fill:url(#SVGID_122_);stroke:url(#SVGID_123_);stroke-width:0.742;stroke-miterlimit:10;}
	.st161{fill:url(#SVGID_124_);stroke:url(#SVGID_125_);stroke-width:0.742;stroke-miterlimit:10;}
	.st162{fill:url(#SVGID_126_);}
	.st163{fill:url(#SVGID_127_);}
	.st164{opacity:0.9;fill:url(#SVGID_128_);}
	.st165{fill:url(#SVGID_129_);}
	.st166{opacity:0.9;fill:url(#SVGID_130_);}
	.st167{fill:url(#SVGID_131_);stroke:url(#SVGID_132_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st168{fill:url(#SVGID_133_);}
	.st169{fill:url(#SVGID_134_);}
	.st170{opacity:0.9;fill:url(#SVGID_135_);}
	.st171{fill:url(#SVGID_136_);}
	.st172{opacity:0.9;fill:url(#SVGID_137_);}
	.st173{fill:url(#SVGID_138_);}
	.st174{opacity:0.9;fill:url(#SVGID_139_);}
	.st175{fill:url(#SVGID_140_);}
	.st176{fill:url(#SVGID_141_);}
</style>
<g id="Layer_1">
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="2289.9851" y1="1563.1174" x2="1514.015" y2="2487.8826">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.5"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="1103" y="1908" class="st0" width="1598" height="235"/>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="-820.99" y1="-38.4568" x2="-1853.01" y2="1191.4568">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-2136" y="199" class="st1" width="1598" height="755"/>
	<path class="st2" d="M2391.4,1732l-790.9,0c-1.4,0-2.6-1.2-2.6-2.6l0-239.8c0-1.4,1.2-2.6,2.6-2.6l790.9,0c1.4,0,2.6,1.2,2.6,2.6
		v239.8C2394,1730.8,2392.8,1732,2391.4,1732z"/>
	<rect x="-3759" y="945" class="st3" width="1598" height="1797"/>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-5377" y1="567" x2="-3779" y2="567">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st4" points="-3779,943 -3779,191 -5377,191 -5377,941.4 	"/>
	<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="-6997" y1="468.8795" x2="-5399" y2="468.8795">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<polygon class="st5" points="-5399,742.2 -5399,190 -6997,190 -6997,747.8 	"/>
	<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="-7038.6743" y1="-1850.8542" x2="-5359.3257" y2="-1432.1458">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<rect x="-6997" y="-2018" class="st6" width="1596" height="753"/>
	<g>
		<g>
			<rect x="-7986.6" y="741.4" class="st7" width="318" height="1481"/>
		</g>
		<g>
			<rect x="-7994" y="734" class="st8" width="318" height="1481"/>
		</g>
	</g>
	<rect x="-6952" y="-2057" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -6946 -2042.8003)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -5986.0918 -2041.6987)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-6998" y1="-2076" x2="-5399" y2="-2076">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st10" x1="-6998" y1="-2076" x2="-5399" y2="-2076"/>
	<line class="st11" x1="-5398.5" y1="-2020" x2="-6998" y2="-2020"/>
	<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="-5002.8535" y1="366.75" x2="-5001.6465" y2="366.75">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st12" x1="-5002" y1="367" x2="-5002.5" y2="366.5"/>
	<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="-5622" y1="-2035" x2="-5587.5" y2="-2035">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st13" x1="-5622" y1="-2035" x2="-5587.5" y2="-2035"/>
	<rect x="-6301" y="-1557" class="st14" width="276" height="71"/>
	<g>
		<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="-6424.5444" y1="-1869.6499" x2="-6397.4526" y2="-1869.6499">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st15" points="-6419.4,-1851 -6424.5,-1857.1 -6409.8,-1869.6 -6424.5,-1882.2 -6419.3,-1888.3 -6397.5,-1869.6 		
			"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-5934.0264" y1="-1841.5068" x2="-5903.5" y2="-1841.5068">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-5934" y="-1845.5" class="st17" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="1618" height="1337" xlink:href="3A9306F390EA591E.png"  transform="matrix(1 0 0 1 -7008 750)">
		</image>
		<g>
			<rect x="-6999" y="764" class="st18" width="1600" height="1319"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="3A9306F390EA5922.png"  transform="matrix(1 0 0 1 -6377 -1915)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-6325.1-1838.6h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-1838.6z"/>
				<path class="st18" d="M-6309.5-1907h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-1907z M-6297-1896.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-6297z"/>
				<path class="st18" d="M-6250.2-1907h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-1907z"/>
				<path class="st18" d="M-6088.2-1887.7c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-6088.2-1887.7z"/>
				<path class="st18" d="M-6018.7-1848.9v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-6018.7z"/>
				<path class="st18" d="M-5998.9-1907h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-1907z"/>
			</g>
		</g>
	</g>
	<rect x="-6531.6" y="-1752.6" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -6531.584 -1730.3711)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">The</tspan><tspan x="61" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="81.8" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">intelligent</tspan><tspan x="255.3" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="276.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">package</tspan><tspan x="424.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="444.9" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">manager</tspan><tspan x="600.4" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="621.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">for</tspan><tspan x="668.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="689" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">the </tspan><tspan x="0" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Node</tspan><tspan x="87.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="101.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Javascript</tspan><tspan x="282.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="296.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Platform.</tspan><tspan x="452.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="466.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Install</tspan><tspan x="572.3" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="586.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">stuff</tspan><tspan x="664.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="678.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">and </tspan><tspan x="275.1" y="86" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">get coding!</tspan></text>
	<rect x="-6294" y="-1550" class="st19" width="276" height="71"/>
	
		<text transform="matrix(1 0 0 1 -6251.0112 -1512.5884)" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Read Docs</text>
	<path class="st18" d="M-5394-1329.5c18.3,18.3-25.9-40-51.8-40c-25.9,0-25.9,40-51.8,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		s-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40s-25.9-40-51.7-40
		s-25.9,40-51.7,40s-25.9-40-51.7-40V-86h1603.5C-5394.5-86-5396.4-1331.9-5394-1329.5z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="3A9306F390EA5925.png"  transform="matrix(1 0 0 1 -6758 -1220)">
		</image>
		<g>
			<circle class="st8" cx="-6585" cy="-1047" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="3A9306F390EA5926.png"  transform="matrix(1 0 0 1 -6765 -521)">
		</image>
		<g>
			<circle class="st8" cx="-6592" cy="-348" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="3A9306F390EA5924.png"  transform="matrix(1 0 0 1 -5942 -796)">
		</image>
		<g>
			<circle class="st8" cx="-5769" cy="-623" r="128"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -6176.6538 441.0776)"><tspan x="0" y="0" style="font-family:'MyriadPro-Regular'; font-size:30px; letter-spacing:1;">❤</tspan><tspan x="16.8" y="0" style="font-family:'MonotypeSorts'; font-size:30px; letter-spacing:1;">,</tspan></text>
	<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="-5377" y1="-1706.4" x2="-3781" y2="-1706.4">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st20" d="M-3781-2022v630.9c-21-2.9-22.7-23.8-46.8-23.8c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2s-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		c-25.9,0-25.9,24.2-51.8,24.2s-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-24.5,0-25.8-21.6-47.8-24V-2022H-3781z"/>
	<g>
		
			<image style="overflow:visible;" width="1608" height="1247" xlink:href="3A9306F390EA5923.png"  transform="matrix(1 0 0 1 -5385 -1401)">
		</image>
		<g>
			<path class="st18" d="M-3781-1369.1V-158h-1596v-1234.8c22,2.3,23.3,24,47.8,24c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2s25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2s25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				s25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2s25.9,24.2,51.8,24.2
				c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2
				c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2C-3803.7-1393-3802-1372-3781-1369.1z"/>
		</g>
	</g>
	<rect x="-5332" y="-2055" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -5326 -2040.8003)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -4366.0918 -2045.6987)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-3778.5" y1="-2022" x2="-5378" y2="-2022"/>
	<linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="-4002" y1="-2039" x2="-3967.5" y2="-2039">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st21" x1="-4002" y1="-2039" x2="-3967.5" y2="-2039"/>
	<rect x="-4708.8" y="-1634" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="-4846.5444" y1="-1838.6499" x2="-4819.4526" y2="-1838.6499">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st22" points="-4841.4,-1820 -4846.5,-1826.1 -4831.8,-1838.6 -4846.5,-1851.2 -4841.3,-1857.3 -4819.5,-1838.6 		
			"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="-4356.0264" y1="-1810.5068" x2="-4325.5" y2="-1810.5068">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-4356" y="-1814.5" class="st23" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="3A9306F390EA592D.png"  transform="matrix(1 0 0 1 -4799 -1884)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-4747.1-1807.6h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-1807.6z"/>
				<path class="st18" d="M-4731.5-1876h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-1876z M-4719-1865.2v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-4719z"/>
				<path class="st18" d="M-4672.2-1876h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-1876z"/>
				<path class="st18" d="M-4510.2-1856.7c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-4510.2-1856.7z"/>
				<path class="st18" d="M-4440.7-1817.9v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-4440.7z"/>
				<path class="st18" d="M-4420.9-1876h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-1876z"/>
			</g>
		</g>
	</g>
	<rect x="-4953.6" y="-1743.6" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -4806.5601 -1728.771)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-4703" y="-1628.2" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -4652.0112 -1595.5884)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="3A9306F390EA592E.png"  transform="matrix(1 0 0 1 -5203.6226 -1295.6224)">
		</image>
		<g>
			<ellipse class="st8" cx="-5049.2" cy="-1143.6" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="-3781.7075" y1="-1346.9401" x2="-3776" y2="-1346.9401">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st24" d="M-3776-1346.8c-1.8,0-3.4-0.1-5-0.3"/>
	<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="-4571" y1="1188.1196" x2="-4566.4438" y2="1188.1196">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st25" d="M-4567,1188.2c-1.3-0.1-2.6-0.2-4-0.2"/>
	<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="-5377" y1="-2075.5" x2="-3778" y2="-2075.5">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st26" x1="-5377" y1="-2075.5" x2="-3778" y2="-2075.5"/>
	
		<text transform="matrix(1 0 0 1 -4856.1777 -1177.2793)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g>
		
			<text transform="matrix(1 0 0 1 -5148.1777 -835.2793)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -4843.1777 -430.2793)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.7;" width="309" height="304" xlink:href="3A9306F390EA592C.png"  transform="matrix(1 0 0 1 -5213.6226 -567.6224)">
		</image>
		<g>
			<ellipse class="st8" cx="-5059.6" cy="-415.6" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -4857.5654 -1129.5498)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -5149.5654 -787.5498)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed accumsan vehicula diam vel auctor. Suspendisse</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;"> id interdum lectus. Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">vestibulum vitae eget lacus. </tspan></text>
	<g>
		<defs>
			<text id="XMLID_1_" transform="matrix(1 0 0 1 -4847.5654 -379.5498)"><tspan x="0" y="0" style="font-family:'Poppins-SemiBold'; font-size:25px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
		</defs>
		<clipPath id="XMLID_6_">
			<use xlink:href="#XMLID_1_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.4;" width="247" height="242" xlink:href="77400133F1DEE1A1.png"  transform="matrix(1 0 0 1 -4424.0918 -719.0377)">
			</image>
			<g>
				<ellipse class="st8" cx="-4307.5" cy="-600.7" rx="113.9" ry="111.5"/>
			</g>
		</g>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="77400133F1DEE1A7.png"  transform="matrix(1 0 0 1 -4315.9448 -885.687)">
			</image>
			<g>
				<ellipse class="st8" cx="-4161.5" cy="-733.7" rx="113.9" ry="111.5"/>
			</g>
		</g>
	</g>
	<linearGradient id="SVGID_18_" gradientUnits="userSpaceOnUse" x1="-4260" y1="-777.5" x2="-3974" y2="-777.5">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st28" cx="-4117" cy="-777.5" r="143"/>
	<circle class="st8" cx="-4117" cy="-777.5" r="134"/>
	<rect x="-6952" y="157" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -6946 171.1997)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -5986.0918 166.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-5398.5" y1="190" x2="-6998" y2="190"/>
	<linearGradient id="SVGID_19_" gradientUnits="userSpaceOnUse" x1="-5622" y1="173" x2="-5587.5" y2="173">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st29" x1="-5622" y1="173" x2="-5587.5" y2="173"/>
	<rect x="-6328.8" y="578" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_20_" gradientUnits="userSpaceOnUse" x1="-6466.5444" y1="373.3501" x2="-6439.4526" y2="373.3501">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st30" points="-6461.4,392 -6466.5,385.9 -6451.8,373.4 -6466.5,360.8 -6461.3,354.7 -6439.5,373.4 		"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_21_" gradientUnits="userSpaceOnUse" x1="-5976.0264" y1="401.4932" x2="-5945.5" y2="401.4932">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-5976" y="397.5" class="st31" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="77400133F1DEE1A0.png"  transform="matrix(1 0 0 1 -6419 328)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-6367.1,404.4h-13.6l-23.2-51.7v51.7h-11.1V336h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V404.4z"/>
				<path class="st18" d="M-6351.5,336h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V336z M-6339,346.8v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-6339z"/>
				<path class="st18" d="M-6292.2,336h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V336z"/>
				<path class="st18" d="M-6130.2,355.3c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-6130.2,355.3z"/>
				<path class="st18" d="M-6060.7,394.1v10.3h-43.7V336h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-6060.7z"/>
				<path class="st18" d="M-6040.9,336h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V336z"/>
			</g>
		</g>
	</g>
	<rect x="-6573.6" y="468.4" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -6426.5601 483.229)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-6323" y="583.8" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -6272.0112 616.4116)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="77400133F1DEE1A3.png"  transform="matrix(1 0 0 1 -6812.6226 927.3776)">
		</image>
		<g>
			<ellipse class="st8" cx="-6658.2" cy="1079.4" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_22_" gradientUnits="userSpaceOnUse" x1="-6997" y1="136.5" x2="-5398" y2="136.5">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st32" x1="-6997" y1="136.5" x2="-5398" y2="136.5"/>
	
		<text transform="matrix(1 0 0 1 -6465.1777 1032.7207)" style="opacity:0.8;fill:#FFFFFF; font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -6757.1777 1387.7207)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -6452.1777 1792.7207)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<text transform="matrix(1 0 0 1 -6466.5654 1080.4502)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -6758.5654 1435.4502)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">interdum lectus.  Phasellus sed tortor sed dui rutrum vestibulum vitae </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">eget lacus. </tspan></text>
	<text id="XMLID_2_" transform="matrix(1 0 0 1 -6456.5654 1843.4502)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	<circle class="st18" cx="-5043" cy="1201" r="143"/>
	<g class="st33">
		<path class="st8" d="M-5666.8,1500H-5788v-112.5h121.2V1500z M-5675.5,1422.1h-103.8v69.2h103.8V1422.1z M-5675.5,1413.5v-17.3
			h-103.8v17.3H-5675.5z"/>
		<circle class="st34" cx="-5771.4" cy="1404.8" r="3.6"/>
		<circle class="st34" cx="-5759.9" cy="1404.8" r="3.6"/>
		<circle class="st34" cx="-5748.3" cy="1404.8" r="3.6"/>
		<path class="st8" d="M-5732.3,1456.5l-20.8,21.9l-6.3-6l15.2-16l-15.2-16.3l6.3-5.9C-5753.1,1434.3-5732.3,1456.5-5732.3,1456.5z"
			/>
		<path class="st8" d="M-5727.4,1469.7h30.3v8.7h-30.3V1469.7z"/>
	</g>
	
		<text transform="matrix(1 0 0 1 -6454.1777 874.7207)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use NPM CLI?</text>
	<rect x="-5330" y="156" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -5324 170.1997)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -4364.0918 169.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_23_" gradientUnits="userSpaceOnUse" x1="-5378" y1="136.5" x2="-3778" y2="136.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st36" x1="-5378" y1="136.5" x2="-3778" y2="136.5"/>
	<linearGradient id="SVGID_24_" gradientUnits="userSpaceOnUse" x1="-4000" y1="176" x2="-3965.5" y2="176">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st37" x1="-4000" y1="176" x2="-3965.5" y2="176"/>
	<line class="st11" x1="-3776.5" y1="192" x2="-5376" y2="192"/>
	<circle class="st8" cx="-5043" cy="1201" r="125"/>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="77400133F1DEE1BF.png"  transform="matrix(1 0 0 1 -5889.8604 1292.3591)">
		</image>
		<g>
			<ellipse class="st8" cx="-5735.4" cy="1444.4" rx="113.9" ry="111.5"/>
		</g>
	</g>
	
		<text transform="matrix(1 0 0 1 -4731.1777 1123.7207)" class="st18" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use this?</text>
	<line class="st38" x1="-4471.5" y1="1255.5" x2="-4473.6" y2="1255.5"/>
	<line class="st38" x1="-5066.3" y1="1255.5" x2="-5068.5" y2="1255.5"/>
	<line class="st39" x1="-4044" y1="1535.9" x2="-4046.4" y2="1535.9"/>
	<line class="st40" x1="-5009.6" y1="279.7" x2="-5012.4" y2="279.7"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="77400133F1DEE1A2.png"  transform="matrix(1 0 0 1 -5062.3643 221.6355)">
		</image>
		<g>
			<path class="st18" d="M-4169.8,262.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V262.4c0-1.7,1.3-3,3-3h849.6
				C-4171.1,259.4-4169.8,260.7-4169.8,262.4z"/>
			<path class="st41" d="M-4169.8,262.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V262.4c0-1.7,1.3-3,3-3h849.6
				C-4171.1,259.4-4169.8,260.7-4169.8,262.4z"/>
		</g>
	</g>
	<line class="st40" x1="-4160" y1="275.7" x2="-4162.5" y2="275.7"/>
	<line class="st40" x1="-4956.1" y1="354.8" x2="-4958.8" y2="354.8"/>
	<line class="st40" x1="-4987.8" y1="327.1" x2="-4990.5" y2="327.1"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="77400133F1DEE1A5.png"  transform="matrix(1 0 0 1 -5030.3643 276.6355)">
		</image>
		<g>
			<path class="st18" d="M-4137.1,316.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V316.7c0-1.7,1.3-3,3-3h849.6
				C-4138.4,313.7-4137.1,315-4137.1,316.7z"/>
			<path class="st41" d="M-4137.1,316.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V316.7c0-1.7,1.3-3,3-3h849.6
				C-4138.4,313.7-4137.1,315-4137.1,316.7z"/>
		</g>
	</g>
	<g>
		<path class="st8" d="M-4137.1,317v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V317c0-1.7,1.3-3,3-3h849.6
			C-4138.4,314-4137.1,315.3-4137.1,317z"/>
		<path class="st41" d="M-4137.1,317v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V317c0-1.7,1.3-3,3-3h849.6
			C-4138.4,314-4137.1,315.3-4137.1,317z"/>
	</g>
	<line class="st40" x1="-4127.1" y1="334.1" x2="-4129.7" y2="334.1"/>
	<line class="st40" x1="-4791.2" y1="413.2" x2="-4793.9" y2="413.2"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="77400133F1DEE1A6.png"  transform="matrix(1 0 0 1 -4996.3643 335.6355)">
		</image>
		<g>
			<path class="st18" d="M-4103.4,375.8v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V375.8c0-1.7,1.3-3,3-3h849.6
				C-4104.8,372.8-4103.4,374.1-4103.4,375.8z"/>
			<path class="st41" d="M-4103.4,375.8v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V375.8c0-1.7,1.3-3,3-3h849.6
				C-4104.8,372.8-4103.4,374.1-4103.4,375.8z"/>
		</g>
	</g>
	<line class="st40" x1="-4083.3" y1="391.3" x2="-4085.9" y2="391.3"/>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_25_" gradientUnits="userSpaceOnUse" x1="-4313.0264" y1="527.4932" x2="-4282.5" y2="527.4932">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-4313" y="523.5" class="st42" width="30.5" height="8"/>
		</g>
		<g>
			<linearGradient id="SVGID_26_" gradientUnits="userSpaceOnUse" x1="-4803.5444" y1="499.3501" x2="-4776.4526" y2="499.3501">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st43" points="-4798.4,518 -4803.5,511.9 -4788.8,499.4 -4803.5,486.8 -4798.3,480.7 -4776.5,499.4 			"/>
		</g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="77400133F1DEE1A4.png"  transform="matrix(1 0 0 1 -4753 457)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-4704.1,530.4h-13.6l-23.2-51.7v51.7h-11.1V462h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V530.4z"/>
					<path class="st8" d="M-4688.5,462h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						h-11.2v27.7h-12.5V462z M-4676,472.8v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-4676z"/>
					<path class="st8" d="M-4629.2,462h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V462z"/>
					<path class="st8" d="M-4467.2,481.3c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-4467.2,481.3z"/>
					<path class="st8" d="M-4397.7,520.1v10.3h-43.7V462h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-4397.7z"/>
					<path class="st8" d="M-4377.9,462h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V462z"/>
				</g>
			</g>
		</g>
		<rect x="-4781.7" y="596.4" class="st47" width="489.6" height="304.6"/>
		<text transform="matrix(1 0 0 1 -4781.7363 611.229)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="54" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="167.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="181.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="279.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="293.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="396.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="409.9" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="440.6" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="454.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the </tspan><tspan x="0" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node</tspan><tspan x="57.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="67.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript</tspan><tspan x="186.4" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="196.3" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Platform.</tspan><tspan x="298" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="307.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Install</tspan><tspan x="376.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="386.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">stuff</tspan><tspan x="437.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="447.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">and </tspan><tspan x="181.2" y="62" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">get coding!</tspan></text>
		<g>
			<rect x="-4650.7" y="729" class="st14" width="230" height="59.2"/>
			<rect x="-4644.8" y="734.8" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -4593.8589 767.4116)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g>
		<path class="st8" d="M-4170.1,262v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V262c0-1.7,1.3-3,3-3h849.6
			C-4171.4,259-4170.1,260.3-4170.1,262z"/>
		<path class="st41" d="M-4170.1,262v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V262c0-1.7,1.3-3,3-3h849.6
			C-4171.4,259-4170.1,260.3-4170.1,262z"/>
	</g>
	<g>
		<path class="st8" d="M-4103.1,376v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V376c0-1.7,1.3-3,3-3h849.6
			C-4104.4,373-4103.1,374.3-4103.1,376z"/>
		<path class="st41" d="M-4103.1,376v21.7c0,1.6-1.4,3-3,3h-849.6c-1.6,0-3-1.4-3-3V376c0-1.7,1.3-3,3-3h849.6
			C-4104.4,373-4103.1,374.3-4103.1,376z"/>
	</g>
	<linearGradient id="SVGID_27_" gradientUnits="userSpaceOnUse" x1="-4329" y1="1436" x2="-4043" y2="1436">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st44" cx="-4186" cy="1436" r="143"/>
	<circle class="st8" cx="-4186" cy="1436" r="125"/>
	<linearGradient id="SVGID_28_" gradientUnits="userSpaceOnUse" x1="-5044" y1="1773" x2="-4758" y2="1773">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st45" cx="-4901" cy="1773" r="143"/>
	<circle class="st8" cx="-4901" cy="1773" r="125"/>
	<g>
		<g>
			<path class="st46" d="M-5294.9,170h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V170z M-5264.2,157.1v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H-5264.2L-5264.2,157.1z M-5273.2,160.3h3.2v6.5h-3.2V160.3z M-5279.6,173.3h6.4V170h6.4v-13h-12.8V173.3z"/>
			<rect x="-5294.9" y="157.1" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-5229.9,160.2 -5229.9,166.5 -5223.8,166.5 -5223.8,169.7 -5230,169.7 -5236.3,169.7 -5236.2,157.1 
			-5223.8,157.1 -5223.8,160.2 		"/>
		<rect x="-5221.2" y="157" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-5215.3" y="163.6" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -5045.3496 5382.1753)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-5194.1" y="165" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -5023.9805 5362.313)" class="st46" width="2" height="8.3"/>
		<rect x="-5207.1" y="157" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_29_" gradientUnits="userSpaceOnUse" x1="-3757" y1="570.5" x2="-2159" y2="570.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st48" points="-2159,950 -2159,191 -3757,191 -3757,948.4 	"/>
	<linearGradient id="SVGID_30_" gradientUnits="userSpaceOnUse" x1="-3360.8535" y1="372.75" x2="-3359.6465" y2="372.75">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st49" x1="-3360" y1="373" x2="-3360.5" y2="372.5"/>
	<rect x="-3710" y="155" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -3704 169.1997)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -2551.0918 170.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<line class="st11" x1="-2156.5" y1="191" x2="-3756" y2="191"/>
	<line class="st50" x1="-3030.2" y1="1172.4" x2="-3031.9" y2="1172.4"/>
	<line class="st38" x1="-3446.3" y1="1195.5" x2="-3448.5" y2="1195.5"/>
	<line class="st39" x1="-2424" y1="1475.9" x2="-2426.4" y2="1475.9"/>
	<line class="st40" x1="-3367.6" y1="285.7" x2="-3370.4" y2="285.7"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="77400133F1DEE1AC.png"  transform="matrix(1 0 0 1 -3508.3645 207.6355)">
		</image>
		<g>
			<path class="st18" d="M-2615.8,248.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V248.4c0-1.7,1.3-3,3-3h849.6
				C-2617.1,245.4-2615.8,246.7-2615.8,248.4z"/>
			<path class="st41" d="M-2615.8,248.4v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V248.4c0-1.7,1.3-3,3-3h849.6
				C-2617.1,245.4-2615.8,246.7-2615.8,248.4z"/>
		</g>
	</g>
	<line class="st40" x1="-2518" y1="281.7" x2="-2520.5" y2="281.7"/>
	<line class="st40" x1="-3314.1" y1="360.8" x2="-3316.8" y2="360.8"/>
	<line class="st40" x1="-3345.8" y1="333.1" x2="-3348.5" y2="333.1"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="77400133F1DEE1C4.png"  transform="matrix(1 0 0 1 -3454.3645 282.6355)">
		</image>
		<g>
			<path class="st18" d="M-2561.1,322.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V322.7c0-1.7,1.3-3,3-3h849.6
				C-2562.4,319.7-2561.1,321-2561.1,322.7z"/>
			<path class="st41" d="M-2561.1,322.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V322.7c0-1.7,1.3-3,3-3h849.6
				C-2562.4,319.7-2561.1,321-2561.1,322.7z"/>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-2561.1,323v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V323c0-1.7,1.3-3,3-3h849.6
			C-2562.4,320-2561.1,321.3-2561.1,323z"/>
		<path class="st41" d="M-2561.1,323v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V323c0-1.7,1.3-3,3-3h849.6
			C-2562.4,320-2561.1,321.3-2561.1,323z"/>
	</g>
	<line class="st40" x1="-2485.1" y1="340.1" x2="-2487.7" y2="340.1"/>
	<line class="st40" x1="-3149.2" y1="419.2" x2="-3151.9" y2="419.2"/>
	<g class="st52">
		<line class="st53" x1="-3757.5" y1="191.3" x2="-2156.5" y2="191.3"/>
		<line class="st53" x1="-3757.4" y1="381.3" x2="-2156.4" y2="381.3"/>
		<line class="st53" x1="-3757.2" y1="571.4" x2="-2156.2" y2="571.4"/>
		<line class="st53" x1="-3757.1" y1="761.4" x2="-2156.1" y2="761.4"/>
		<line class="st53" x1="-3757" y1="951.5" x2="-2156" y2="951.5"/>
		<line class="st53" x1="-3756.8" y1="1141.5" x2="-2155.8" y2="1141.5"/>
		<line class="st53" x1="-3756.7" y1="1331.6" x2="-2155.7" y2="1331.6"/>
		<line class="st53" x1="-3756.5" y1="1521.6" x2="-2155.5" y2="1521.6"/>
		<line class="st53" x1="-3756.4" y1="1711.7" x2="-2155.4" y2="1711.7"/>
		<line class="st53" x1="-3756.3" y1="1901.7" x2="-2155.3" y2="1901.7"/>
		<line class="st53" x1="-3756.1" y1="2091.7" x2="-2155.1" y2="2091.7"/>
		<line class="st53" x1="-3756" y1="2281.8" x2="-2155" y2="2281.8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="77400133F1DEE1C7.png"  transform="matrix(1 0 0 1 -3385.3645 374.6355)">
		</image>
		<g>
			<path class="st18" d="M-2492.4,414.8v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V414.8c0-1.7,1.3-3,3-3h849.6
				C-2493.8,411.8-2492.4,413.1-2492.4,414.8z"/>
			<path class="st41" d="M-2492.4,414.8v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4V414.8c0-1.7,1.3-3,3-3h849.6
				C-2493.8,411.8-2492.4,413.1-2492.4,414.8z"/>
		</g>
	</g>
	<line class="st40" x1="-2441.3" y1="397.3" x2="-2443.9" y2="397.3"/>
	<g class="st54">
		<line class="st55" x1="-3756" y1="197" x2="-3756" y2="2332"/>
		<line class="st55" x1="-3556.4" y1="196.5" x2="-3556.4" y2="2331.5"/>
		<line class="st55" x1="-3356.9" y1="196" x2="-3356.9" y2="2331"/>
		<line class="st55" x1="-3157.3" y1="195.5" x2="-3157.3" y2="2330.5"/>
		<line class="st55" x1="-2957.8" y1="195" x2="-2957.8" y2="2330"/>
		<line class="st55" x1="-2758.2" y1="194.5" x2="-2758.2" y2="2329.5"/>
		<line class="st55" x1="-2558.6" y1="194" x2="-2558.6" y2="2329"/>
		<line class="st55" x1="-2359.1" y1="193.5" x2="-2359.1" y2="2328.5"/>
		<line class="st55" x1="-2159.5" y1="193" x2="-2159.5" y2="2328"/>
	</g>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_31_" gradientUnits="userSpaceOnUse" x1="-2734.0264" y1="577.4932" x2="-2703.5" y2="577.4932">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-2734" y="573.5" class="st56" width="30.5" height="8"/>
		</g>
		<g>
			<linearGradient id="SVGID_32_" gradientUnits="userSpaceOnUse" x1="-3224.5444" y1="549.3501" x2="-3197.4526" y2="549.3501">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st57" points="-3219.4,568 -3224.5,561.9 -3209.8,549.4 -3224.5,536.8 -3219.3,530.7 -3197.5,549.4 			"/>
		</g>
		<g class="st51">
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="77400133F1DEE1C5.png"  transform="matrix(1 0 0 1 -3174 507)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-3125.1,580.4h-13.6l-23.2-51.7v51.7h-11.1V512h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V580.4z"/>
					<path class="st8" d="M-3109.5,512h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						h-11.2v27.7h-12.5V512z M-3097,522.8v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-3097z"/>
					<path class="st8" d="M-3050.2,512h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V512z"/>
					<path class="st8" d="M-2888.2,531.3c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-2888.2,531.3z"/>
					<path class="st8" d="M-2818.7,570.1v10.3h-43.7V512h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-2818.7z"/>
					<path class="st8" d="M-2798.9,512h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V512z"/>
				</g>
			</g>
		</g>
		<rect x="-3225.7" y="646.4" class="st47" width="551.7" height="304.6"/>
		<text transform="matrix(1 0 0 1 -3225.7363 661.229)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
		<g>
			<rect x="-3223.7" y="746" class="st14" width="230" height="59.2"/>
			<rect x="-3217.8" y="751.8" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -3166.8589 784.4116)" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-2616.1,248v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V248c0-1.7,1.3-3,3-3h849.6
			C-2617.4,245-2616.1,246.3-2616.1,248z"/>
		<path class="st41" d="M-2616.1,248v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V248c0-1.7,1.3-3,3-3h849.6
			C-2617.4,245-2616.1,246.3-2616.1,248z"/>
	</g>
	<g class="st51">
		<path class="st8" d="M-2492.1,415v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V415c0-1.7,1.3-3,3-3h849.6
			C-2493.4,412-2492.1,413.3-2492.1,415z"/>
		<path class="st41" d="M-2492.1,415v21.7c0,1.6-1.3,3-3,3h-849.6c-1.7,0-3-1.4-3-3V415c0-1.7,1.3-3,3-3h849.6
			C-2493.4,412-2492.1,413.3-2492.1,415z"/>
	</g>
	<g>
		<g>
			<path class="st46" d="M-3674.9,169h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V169z M-3644.2,156.1v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H-3644.2L-3644.2,156.1z M-3653.2,159.3h3.2v6.5h-3.2V159.3z M-3659.6,172.3h6.4V169h6.4v-13h-12.8V172.3z"/>
			<rect x="-3674.9" y="156.1" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-3609.9,159.2 -3609.9,165.5 -3603.8,165.5 -3603.8,168.7 -3610,168.7 -3616.3,168.7 -3616.2,156.1 
			-3603.8,156.1 -3603.8,159.2 		"/>
		<rect x="-3601.2" y="156" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-3595.3" y="162.6" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3426.3494 3761.1753)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-3574.1" y="164" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3404.9805 3741.313)" class="st46" width="2" height="8.3"/>
		<rect x="-3587.1" y="156" class="st46" width="6.4" height="12.9"/>
	</g>
	
		<linearGradient id="SVGID_33_" gradientUnits="userSpaceOnUse" x1="-3536.583" y1="1547.4449" x2="-3156.5828" y2="1547.4449" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -3997.0811 -1083.0842)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st58" points="-2583.6,2183.5 -2610.9,2562.5 -2804.4,2548.5 -2777.1,2169.5 	"/>
	<linearGradient id="SVGID_34_" gradientUnits="userSpaceOnUse" x1="-3758" y1="136.5" x2="-2158" y2="136.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st59" x1="-3758" y1="136.5" x2="-2158" y2="136.5"/>
	
		<linearGradient id="SVGID_35_" gradientUnits="userSpaceOnUse" x1="-2633.3538" y1="1593.8499" x2="-2429.3535" y2="1593.8499" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 -267.5691 -381.758)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st60" points="-2387.5,1839.5 -2589.2,1870.1 -2674.5,1308.5 -2472.8,1277.9 	"/>
	<line class="st50" x1="-2300.2" y1="1236.4" x2="-2301.9" y2="1236.4"/>
	<g>
		
			<image style="overflow:visible;" width="827" height="400" xlink:href="77400133F1DEE1CB.png"  transform="matrix(1 0 0 1 -3375 1953)">
		</image>
		<g>
			<path class="st61" d="M-2557.5,2347h-788.4c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0c1.4,0,2.6,1.2,2.6,2.6
				v362.8C-2554.9,2345.8-2556.1,2347-2557.5,2347z"/>
			<path class="st62" d="M-2557.5,2347h-788.4c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0c1.4,0,2.6,1.2,2.6,2.6
				v362.8C-2554.9,2345.8-2556.1,2347-2557.5,2347z"/>
		</g>
	</g>
	<rect x="-3318" y="2417" class="st8" width="21" height="38"/>
	
		<linearGradient id="SVGID_36_" gradientUnits="userSpaceOnUse" x1="-3521.8662" y1="1187.1632" x2="-3141.8662" y2="1187.1632" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -4133.3789 -2178.4565)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st63" points="-3219.8,1090.4 -3263.5,1467.9 -3456.2,1445.6 -3412.5,1068.1 	"/>
	<g>
		
			<image style="overflow:visible;" width="828" height="375" xlink:href="77400133F1DEE1CC.png"  transform="matrix(1 0 0 1 -3375 1006)">
		</image>
		<g>
			<path class="st61" d="M-2555.6,1374.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C-2553,1373.3-2554.2,1374.5-2555.6,1374.5z"/>
			<path class="st62" d="M-2555.6,1374.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6v337.3C-2553,1373.3-2554.2,1374.5-2555.6,1374.5z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -3072.0342 1220.585)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g id="XMLID_3_">
		<text transform="matrix(0.9755 0 0 1 -3148.5439 2199.4502)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	</g>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -6767.1777 1386.7207)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="77400133F1DEE1CF.png"  transform="matrix(1 0 0 1 -3073 1138)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -3069.9512 1169.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="77400133F1DEE1CD.png"  transform="matrix(1 0 0 1 -3142 2117)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -3138.5254 2148.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;" width="827" height="401" xlink:href="77400133F1DEE1CA.png"  transform="matrix(1 0 0 1 -3375 1486)">
		</image>
		<g>
			<path class="st61" d="M-2557.1,1880.5l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-2554.6,1879.3-2555.7,1880.5-2557.1,1880.5z"/>
			<path class="st62" d="M-2557.1,1880.5l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6l0,362.8C-2554.6,1879.3-2555.7,1880.5-2557.1,1880.5z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -3162.5447 1708.4502)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">interdum lectus.  Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">vestibulum vitae eget lacus. </tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="237" height="59" xlink:href="77400133F1DEE1C6.png"  transform="matrix(1 0 0 1 -3160 1624)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -3156.5334 1655.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<polygon class="st64" points="-2154.4,2620.3 -3761.1,2570.2 -3761.1,3012.5 -2157.5,3012.5 	"/>
	
		<linearGradient id="SVGID_37_" gradientUnits="userSpaceOnUse" x1="-4493.8354" y1="1306.126" x2="-4113.835" y2="1306.126" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 812.3953 852.0187)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st65" points="-3231.7,2445.1 -3611.3,2462.7 -3620.3,2268.9 -3240.7,2251.3 	"/>
	<line class="st41" x1="-2580.4" y1="328.8" x2="-2572" y2="337.7"/>
	<line class="st41" x1="-2580.7" y1="337.2" x2="-2571.7" y2="328.8"/>
	<line class="st41" x1="-2635.4" y1="252.8" x2="-2627" y2="261.7"/>
	<line class="st41" x1="-2635.7" y1="261.2" x2="-2626.7" y2="252.8"/>
	<line class="st41" x1="-2509.4" y1="421.8" x2="-2501" y2="430.7"/>
	<line class="st41" x1="-2509.7" y1="430.2" x2="-2500.7" y2="421.8"/>
	<path class="st19" d="M-3142.6,1188.5c5.4-5.6,8.6-13.1,8.6-21.5c0-17.1-13.9-31-31-31s-31,13.9-31,31c0,9.8,4.5,18.5,11.6,24.2
		c-2.2,5.6-8,23.3-5.2,51.8h55.6C-3134,1243-3128.7,1211.2-3142.6,1188.5z"/>
	<circle class="st18" cx="-3162.5" cy="1161.6" r="3.5"/>
	<circle class="st18" cx="-3179.5" cy="1164.5" r="3.5"/>
	<circle class="st62" cx="-3172.5" cy="1166.5" r="30.5"/>
	<path class="st66" d="M-3162,1174.8c-3.2,3.7-8.8,4.1-12.4,0.9"/>
	<path class="st62" d="M-3186.6,1190.2c0,0-9.4,18.8-6,53.8h55.6c0,0,5.6-33.4-9.7-56.2"/>
	<line class="st62" x1="-3258" y1="1122" x2="-3258" y2="1259"/>
	<line class="st62" x1="-3258" y1="1625" x2="-3258" y2="1762"/>
	<line class="st62" x1="-3258" y1="2104" x2="-3258" y2="2241"/>
	<g id="POueHo_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="POueHo_2_" xlink:href="77400133F1DEE1C4.jpg"  transform="matrix(1 0 0 1 -1869 -1148)">
		</image>
	</g>
	<g id="FkRr9g_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="FkRr9g_2_" xlink:href="77400133F1DEE1EB.jpg"  transform="matrix(1 0 0 1 -3289 -1097)">
		</image>
	</g>
	<rect x="-2114" y="158" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -2108 172.1997)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -933.0918 173.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M-2078.9,172h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V172z M-2048.2,159.1v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H-2048.2L-2048.2,159.1z M-2057.2,162.3h3.2v6.5h-3.2V162.3z M-2063.6,175.3h6.4V172h6.4v-13h-12.8V175.3z"/>
			<rect x="-2078.9" y="159.1" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-2013.9,162.2 -2013.9,168.5 -2007.8,168.5 -2007.8,171.7 -2014,171.7 -2020.3,171.7 -2020.2,159.1 
			-2007.8,159.1 -2007.8,162.2 		"/>
		<rect x="-2005.2" y="159" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-1999.3" y="165.6" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1827.3494 2168.1753)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-1978.1" y="167" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1805.9806 2148.313)" class="st46" width="2" height="8.3"/>
		<rect x="-1991.1" y="159" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_38_" gradientUnits="userSpaceOnUse" x1="-2140" y1="137.5" x2="-540" y2="137.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st67" x1="-2140" y1="137.5" x2="-540" y2="137.5"/>
	<linearGradient id="SVGID_39_" gradientUnits="userSpaceOnUse" x1="-2137" y1="1888.7506" x2="-539" y2="1888.7506">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st68" points="-539,2824 -539,953.5 -2137,953.5 -2137,2820.1 	"/>
	<line class="st50" x1="-1421.2" y1="1227.4" x2="-1422.9" y2="1227.4"/>
	<line class="st38" x1="-1837.3" y1="1250.5" x2="-1839.5" y2="1250.5"/>
	
		<linearGradient id="SVGID_40_" gradientUnits="userSpaceOnUse" x1="-3475.875" y1="3156.2397" x2="-3095.8748" y2="3156.2397" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -3997.0811 -1083.0842)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st69" points="-974.6,2238.5 -1001.9,2617.5 -1195.4,2603.5 -1168.1,2224.5 	"/>
	
		<linearGradient id="SVGID_41_" gradientUnits="userSpaceOnUse" x1="-1007.3376" y1="1896.3485" x2="-803.3374" y2="1896.3485" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 -267.5691 -381.758)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st70" points="-734.5,1894.5 -936.2,1925.1 -1021.5,1363.5 -819.8,1332.9 	"/>
	
		<linearGradient id="SVGID_42_" gradientUnits="userSpaceOnUse" x1="-3391.1201" y1="2791.7852" x2="-3011.1201" y2="2791.7852" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -4133.3789 -2178.4565)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st71" points="-1610.8,1145.4 -1654.5,1522.9 -1847.2,1500.6 -1803.5,1123.1 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-936.6,1439.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-934,1438.3-935.2,1439.5-936.6,1439.5z"/>
		</g>
		<g>
			<path class="st72" d="M-946.6,1429.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-944,1428.3-945.2,1429.5-946.6,1429.5z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -1429.0342 1271.585)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="77400133F1DEE1E9.png"  transform="matrix(1 0 0 1 -1430 1193)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -1426.8369 1224.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_43_" gradientUnits="userSpaceOnUse" x1="-2889.1377" y1="1435.938" x2="-2509.1375" y2="1435.938" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 812.3953 852.0187)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st73" points="-1622.7,2500.1 -2002.3,2517.7 -2011.3,2323.9 -1631.7,2306.3 	"/>
	<path class="st74" d="M-1502.7,1298.5h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C-1500.7,1297.6-1501.6,1298.5-1502.7,1298.5z"/>
	<rect x="-1624.3" y="1202" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE1D7.png"  transform="matrix(1 0 0 1 -1620.1442 1222.8558)">
		</image>
		<g>
			<linearGradient id="SVGID_44_" gradientUnits="userSpaceOnUse" x1="-1578.8502" y1="1239.0812" x2="-1577.1606" y2="1224.5143">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1614.7" y="1228.7" class="st76" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE1D9.png"  transform="matrix(1 0 0 1 -1619.8997 1240.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_45_" gradientUnits="userSpaceOnUse" x1="-1591.0468" y1="1253.7651" x2="-1589.7185" y2="1242.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1614.8" y="1245.1" class="st77" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE1DB.png"  transform="matrix(1 0 0 1 -1620.1566 1254.8434)">
		</image>
		<g>
			<linearGradient id="SVGID_46_" gradientUnits="userSpaceOnUse" x1="-1586.3372" y1="1270.1837" x2="-1584.8344" y2="1257.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1614.8" y="1260.5" class="st78" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE1DE.png"  transform="matrix(1 0 0 1 -1621.0878 1271.9122)">
		</image>
		<g>
			<linearGradient id="SVGID_47_" gradientUnits="userSpaceOnUse" x1="-1589.8729" y1="1286.9438" x2="-1588.4941" y2="1275.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1615.2" y="1278" class="st79" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE1DF.png"  transform="matrix(1 0 0 1 -1563.8997 1240.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_48_" gradientUnits="userSpaceOnUse" x1="-1550.9332" y1="1251.885" x2="-1550.0411" y2="1244.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1558.5" y="1245.1" class="st80" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1578" y1="1209" x2="-1538" y2="1209"/>
	<circle class="st18" cx="-1616.5" cy="1208.5" r="1.5"/>
	<circle class="st18" cx="-1610.5" cy="1208.5" r="1.5"/>
	<line class="st50" x1="-1419.2" y1="1673.4" x2="-1420.9" y2="1673.4"/>
	<line class="st38" x1="-1835.3" y1="1696.5" x2="-1837.5" y2="1696.5"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-934.6,1885.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-932,1884.3-933.2,1885.5-934.6,1885.5z"/>
		</g>
		<g>
			<path class="st72" d="M-944.6,1875.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-942,1874.3-943.2,1875.5-944.6,1875.5z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="77400133F1DEE1DA.png"  transform="matrix(1 0 0 1 -1435 1620)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -1431.6113 1651.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-1500.7,1744.5h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C-1498.7,1743.6-1499.6,1744.5-1500.7,1744.5z"/>
	<rect x="-1622.3" y="1648" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE1D5.png"  transform="matrix(1 0 0 1 -1618.1442 1668.8558)">
		</image>
		<g>
			<linearGradient id="SVGID_49_" gradientUnits="userSpaceOnUse" x1="-1576.8502" y1="1685.0812" x2="-1575.1606" y2="1670.5143">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1612.7" y="1674.7" class="st82" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE1D6.png"  transform="matrix(1 0 0 1 -1617.8997 1686.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_50_" gradientUnits="userSpaceOnUse" x1="-1589.0468" y1="1699.7651" x2="-1587.7185" y2="1688.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1612.8" y="1691.1" class="st83" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE232.png"  transform="matrix(1 0 0 1 -1618.1566 1700.8434)">
		</image>
		<g>
			<linearGradient id="SVGID_51_" gradientUnits="userSpaceOnUse" x1="-1584.3372" y1="1716.1837" x2="-1582.8344" y2="1703.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1612.8" y="1706.5" class="st84" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE231.png"  transform="matrix(1 0 0 1 -1619.0878 1717.9122)">
		</image>
		<g>
			<linearGradient id="SVGID_52_" gradientUnits="userSpaceOnUse" x1="-1587.8729" y1="1732.9438" x2="-1586.4941" y2="1721.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1613.2" y="1724" class="st85" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE236.png"  transform="matrix(1 0 0 1 -1561.8997 1686.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_53_" gradientUnits="userSpaceOnUse" x1="-1548.9332" y1="1697.885" x2="-1548.0411" y2="1690.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1556.5" y="1691.1" class="st86" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1576" y1="1655" x2="-1536" y2="1655"/>
	<circle class="st18" cx="-1614.5" cy="1654.5" r="1.5"/>
	<circle class="st18" cx="-1608.5" cy="1654.5" r="1.5"/>
	<rect x="-1434.9" y="1681" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -1434.8857 1694.2773)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="-1416.2" y1="2136.4" x2="-1417.9" y2="2136.4"/>
	<line class="st38" x1="-1832.3" y1="2159.5" x2="-1834.5" y2="2159.5"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-931.6,2348.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-929,2347.3-930.2,2348.5-931.6,2348.5z"/>
		</g>
		<g>
			<path class="st72" d="M-941.6,2338.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6v-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-939,2337.3-940.2,2338.5-941.6,2338.5z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="77400133F1DEE230.png"  transform="matrix(1 0 0 1 -1432 2083)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -1428.6113 2114.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-1497.7,2207.5h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C-1495.7,2206.6-1496.6,2207.5-1497.7,2207.5z"/>
	<rect x="-1619.3" y="2111" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE233.png"  transform="matrix(1 0 0 1 -1615.1442 2131.8557)">
		</image>
		<g>
			<linearGradient id="SVGID_54_" gradientUnits="userSpaceOnUse" x1="-1573.8502" y1="2148.0813" x2="-1572.1606" y2="2133.5144">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1609.7" y="2137.7" class="st87" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE22F.png"  transform="matrix(1 0 0 1 -1614.8997 2149.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_55_" gradientUnits="userSpaceOnUse" x1="-1586.0468" y1="2162.7651" x2="-1584.7185" y2="2151.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1609.8" y="2154.1" class="st88" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE20E.png"  transform="matrix(1 0 0 1 -1615.1566 2163.8435)">
		</image>
		<g>
			<linearGradient id="SVGID_56_" gradientUnits="userSpaceOnUse" x1="-1581.3372" y1="2179.1838" x2="-1579.8344" y2="2166.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1609.8" y="2169.5" class="st89" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE211.png"  transform="matrix(1 0 0 1 -1616.0878 2180.9124)">
		</image>
		<g>
			<linearGradient id="SVGID_57_" gradientUnits="userSpaceOnUse" x1="-1584.8729" y1="2195.9438" x2="-1583.4941" y2="2184.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1610.2" y="2187" class="st90" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE212.png"  transform="matrix(1 0 0 1 -1558.8997 2149.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_58_" gradientUnits="userSpaceOnUse" x1="-1545.9332" y1="2160.885" x2="-1545.0411" y2="2153.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1553.5" y="2154.1" class="st91" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1573" y1="2118" x2="-1533" y2="2118"/>
	<circle class="st18" cx="-1611.5" cy="2117.5" r="1.5"/>
	<circle class="st18" cx="-1605.5" cy="2117.5" r="1.5"/>
	<rect x="-1431.9" y="2144" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -1431.8857 2157.2773)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<path class="st92" d="M-781.1,365.6c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19C-780.9,365.6-781,365.6-781.1,365.6z"
		/>
	<path class="st92" d="M-774.9,393.3c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
		C-774.3,392.9-774.6,393.2-774.9,393.3z"/>
	<path class="st92" d="M-774.9,393.3c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-774.7,393.2-774.8,393.3-774.9,393.3z"/>
	<path class="st93" d="M-768.9,744.1c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C-768.7,744.2-768.8,744.1-768.9,744.1z"/>
	<path class="st93" d="M-784.6,765.2c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C-784,765.4-784.4,765.4-784.6,765.2z"/>
	<path class="st93" d="M-743.2,746.1c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_59_" gradientUnits="userSpaceOnUse" x1="798.01" y1="-41.4568" x2="-234.01" y2="1188.4568">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.7"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-517" y="196" class="st94" width="1598" height="755"/>
	<rect x="-491" y="158" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -485 172.1997)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 886.9082 173.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M-444.9,172h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V172z M-414.2,159.1v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7h3.2
				v-13H-414.2L-414.2,159.1z M-423.2,162.3h3.2v6.5h-3.2V162.3z M-429.6,175.3h6.4V172h6.4v-13h-12.8V175.3z"/>
			<rect x="-444.9" y="159.1" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-379.9,162.2 -379.9,168.5 -373.8,168.5 -373.8,171.7 -380,171.7 -386.3,171.7 -386.2,159.1 
			-373.8,159.1 -373.8,162.2 		"/>
		<rect x="-371.2" y="159" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-365.3" y="165.6" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -193.3494 534.1752)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-344.1" y="167" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -171.9806 514.3131)" class="st46" width="2" height="8.3"/>
		<rect x="-357.1" y="159" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_60_" gradientUnits="userSpaceOnUse" x1="-518" y1="136.5" x2="1082" y2="136.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st95" x1="-518" y1="136.5" x2="1082" y2="136.5"/>
	<rect x="-2139.5" y="2561.5" class="st96" width="1602" height="510"/>
	<linearGradient id="SVGID_61_" gradientUnits="userSpaceOnUse" x1="2.1464" y1="358.75" x2="3.3536" y2="358.75">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st97" x1="3" y1="359" x2="2.5" y2="358.5"/>
	<line class="st40" x1="48.9" y1="291.8" x2="46.2" y2="291.8"/>
	<line class="st40" x1="17.2" y1="319.1" x2="14.5" y2="319.1"/>
	<line class="st40" x1="213.8" y1="350.2" x2="211.1" y2="350.2"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="77400133F1DEE210.png"  transform="matrix(1 0 0 1 -108 255)">
			</image>
			<g>
				<path class="st98" d="M663.9,273.7v468.9c0,2.2-1.8,4-4,4H-88.7c-2.2,0-4-1.8-4-4V273.7c0-1.7,1.3-3,3-3h750.6
					C662.6,270.7,663.9,272,663.9,273.7z"/>
				<path class="st81" d="M663.9,273.7v468.9c0,2.2-1.8,4-4,4H-88.7c-2.2,0-4-1.8-4-4V273.7c0-1.7,1.3-3,3-3h750.6
					C662.6,270.7,663.9,272,663.9,273.7z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M663.9,274v21.7c0,1.6-1.3,3-3,3H-89.7c-1.7,0-3-1.4-3-3V274c0-1.7,1.3-3,3-3h750.6
				C662.6,271,663.9,272.3,663.9,274z"/>
			<path class="st62" d="M663.9,274v21.7c0,1.6-1.3,3-3,3H-89.7c-1.7,0-3-1.4-3-3V274c0-1.7,1.3-3,3-3h750.6
				C662.6,271,663.9,272.3,663.9,274z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="49.4" y1="366.7" x2="46.6" y2="366.7"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="77400133F1DEE20D.png"  transform="matrix(1 0 0 1 -69 311)">
			</image>
			<g>
				<path class="st98" d="M702.2,329.4v468.9c0,2.2-1.8,4-4,4H-50.4c-2.2,0-4-1.8-4-4V329.4c0-1.7,1.3-3,3-3h750.6
					C700.9,326.4,702.2,327.7,702.2,329.4z"/>
				<path class="st99" d="M702.2,329.4v468.9c0,2.2-1.8,4-4,4H-50.4c-2.2,0-4-1.8-4-4V329.4c0-1.7,1.3-3,3-3h750.6
					C700.9,326.4,702.2,327.7,702.2,329.4z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M701.9,329v21.7c0,1.6-1.3,3-3,3H-51.7c-1.7,0-3-1.4-3-3V329c0-1.7,1.3-3,3-3h750.6
				C700.6,326,701.9,327.3,701.9,329z"/>
			<path class="st62" d="M701.9,329v21.7c0,1.6-1.3,3-3,3H-51.7c-1.7,0-3-1.4-3-3V329c0-1.7,1.3-3,3-3h750.6
				C700.6,326,701.9,327.3,701.9,329z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="77400133F1DEE273.png"  transform="matrix(1 0 0 1 -35 371)">
			</image>
			<g>
				<path class="st98" d="M736.6,387.8v472.9c0,0.5-0.4,1-1,1H-19.1c-0.5,0-1-0.4-1-1V387.8c0-1.1,0.9-2,2-2h752.6
					C735.7,385.8,736.6,386.7,736.6,387.8z"/>
				<path class="st99" d="M736.6,387.8v472.9c0,0.5-0.4,1-1,1H-19.1c-0.5,0-1-0.4-1-1V387.8c0-1.1,0.9-2,2-2h752.6
					C735.7,385.8,736.6,386.7,736.6,387.8z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="451.7" y="536.8" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="80.4,532.7 76.5,528 87.7,518.4 76.5,508.8 80.4,504.1 97.2,518.4 				"/>
			</g>
			<g class="st51">
				
					<image style="overflow:visible;opacity:0.2;" width="327" height="66" xlink:href="77400133F1DEE275.png"  transform="matrix(1 0 0 1 114.4106 485.4106)">
				</image>
				<g>
					<g>
						<path class="st8" d="M152.5,542.1h-10.4l-17.7-39.6v39.6h-8.5v-52.3h11.5l16.6,37.5v-37.5h9.1c0.1,0.1,0.2,0.3,0.2,0.4
							c0,0.2-0.1,0.5-0.3,0.8c-0.2,0.3-0.4,0.9-0.5,1.7V542.1z"/>
						<path class="st8" d="M164.4,489.8h17.7c3.1,0,5.8,0.4,8,1.3c2.2,0.8,4,2,5.5,3.4c1.4,1.4,2.5,3.1,3.1,5c0.7,1.9,1,3.9,1,6
							c0,2.1-0.3,4.1-1,6c-0.6,1.9-1.7,3.5-3.1,4.9c-1.4,1.4-3.2,2.5-5.4,3.3c-2.2,0.8-4.8,1.2-7.8,1.2H174v21.2h-9.6V489.8z
							 M174,498.1V513h7.9c1.5,0,2.7-0.2,3.7-0.5c1-0.4,1.9-0.9,2.6-1.5c0.7-0.6,1.2-1.4,1.5-2.3c0.3-0.9,0.5-1.8,0.5-2.9
							c0-1.1-0.2-2.1-0.5-3.1c-0.3-0.9-0.8-1.7-1.5-2.4c-0.7-0.7-1.5-1.2-2.5-1.6c-1-0.4-2.2-0.6-3.6-0.6H174z"/>
						<path class="st8" d="M209.8,489.8h9.9l8.8,24.6l8.7-24.7h10v52.4h-8.5v-38l-7.3,19.7h-6.2l-7.1-19.7v38h-8.4V489.8z"/>
						<path class="st8" d="M333.7,504.6c-0.2-0.1-0.4-0.2-0.5-0.3c-0.1-0.2-0.1-0.4-0.2-0.6c0-0.2-0.1-0.5-0.1-0.8
							c0-0.3-0.1-0.6-0.2-1c-0.8-1.8-1.8-3.2-3.2-4.2c-1.3-1-3.1-1.6-5.1-1.6c-1.7,0-3.2,0.5-4.5,1.4c-1.3,1-2.5,2.3-3.4,4
							c-1,1.7-1.7,3.8-2.2,6.2c-0.5,2.4-0.8,5.1-0.8,8.1c0,2.9,0.3,5.5,0.8,7.9c0.6,2.4,1.3,4.5,2.4,6.3c1,1.8,2.2,3.1,3.7,4.2
							c1.4,1,3,1.5,4.7,1.5c2,0,3.8-0.6,5.3-1.9c1.5-1.3,2.9-3,4.2-5.1l7.1,4.6c-2,3.4-4.4,6-7.2,7.7c-2.8,1.7-5.9,2.6-9.2,2.6
							c-3.1,0-5.9-0.5-8.6-1.6c-2.6-1.1-4.9-2.8-6.8-5.1c-1.9-2.3-3.4-5.2-4.5-8.6c-1.1-3.4-1.6-7.4-1.6-12.1c0-3.4,0.3-6.5,0.9-9.2
							c0.6-2.7,1.4-5.1,2.4-7.2c1-2.1,2.2-3.8,3.6-5.2c1.4-1.4,2.8-2.6,4.4-3.5c1.5-0.9,3.1-1.6,4.8-2c1.7-0.4,3.3-0.6,4.8-0.6
							c2,0,3.8,0.3,5.6,0.8c1.8,0.6,3.5,1.4,5,2.4c1.5,1.1,2.9,2.3,4.1,3.8c1.2,1.5,2.2,3.1,2.9,4.9L333.7,504.6z"/>
						<path class="st8" d="M386.9,534.2v7.9h-33.4v-52.3h10.2c0.1,0.1,0.2,0.3,0.2,0.4c0,0.2-0.1,0.5-0.3,0.8
							c-0.2,0.3-0.4,0.9-0.5,1.7v41.5H386.9z"/>
						<path class="st8" d="M402,489.8h29.2v7.8h-10.2v36.8h10.6v7.7h-30.2v-7.8h10.2v-36.7H402V489.8z"/>
					</g>
				</g>
			</g>
			<rect x="75.3" y="605.4" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 75.2637 620.229)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="77.3" y="705" class="st14" width="230" height="59.2"/>
				<rect x="83.2" y="710.8" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 134.1411 743.4116)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M736.9,389v24.7c0,0,0,0,0,0H-19.6c0,0,0,0,0,0V389c0-1.7,1.3-3,3-3h750.6C735.6,386,736.9,387.3,736.9,389z
				"/>
			<path class="st62" d="M736.9,389v24.7c0,0,0,0,0,0H-19.6c0,0,0,0,0,0V389c0-1.7,1.3-3,3-3h750.6C735.6,386,736.9,387.3,736.9,389
				z"/>
		</g>
		<line class="st66" x1="-3.4" y1="395.7" x2="5" y2="404.6"/>
		<line class="st66" x1="-3.7" y1="404.3" x2="5.3" y2="395.9"/>
		<line class="st66" x1="-43.4" y1="335.7" x2="-35" y2="344.6"/>
		<line class="st66" x1="-43.7" y1="344.3" x2="-34.7" y2="335.9"/>
		<line class="st66" x1="-80.4" y1="280.7" x2="-72" y2="289.6"/>
		<line class="st66" x1="-80.7" y1="289.3" x2="-71.7" y2="280.9"/>
	</g>
	<linearGradient id="SVGID_62_" gradientUnits="userSpaceOnUse" x1="-517" y1="1888.7506" x2="1081" y2="1888.7506">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st100" points="1081,2824 1081,953.5 -517,953.5 -517,2820.1 	"/>
	<line class="st50" x1="198.8" y1="1227.4" x2="197.1" y2="1227.4"/>
	<line class="st38" x1="-217.3" y1="1250.5" x2="-219.5" y2="1250.5"/>
	
		<linearGradient id="SVGID_63_" gradientUnits="userSpaceOnUse" x1="-3359.5188" y1="4772.0557" x2="-2979.5188" y2="4772.0557" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -3997.0811 -1083.0842)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st101" points="645.4,2238.5 618.1,2617.5 424.6,2603.5 451.9,2224.5 	"/>
	
		<linearGradient id="SVGID_64_" gradientUnits="userSpaceOnUse" x1="594.3081" y1="2139.5168" x2="798.3083" y2="2139.5168" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 -267.5691 -381.758)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st102" points="885.5,1894.5 683.8,1925.1 598.5,1363.5 800.2,1332.9 	"/>
	
		<linearGradient id="SVGID_65_" gradientUnits="userSpaceOnUse" x1="-3204.4729" y1="4400.9971" x2="-2824.4729" y2="4400.9971" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -4133.3789 -2178.4565)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st103" points="9.2,1145.4 -34.5,1522.9 -227.2,1500.6 -183.5,1123.1 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M683.4,1439.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C686,1438.3,684.8,1439.5,683.4,1439.5z"/>
		</g>
		<g>
			<path class="st72" d="M673.4,1429.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C676,1428.3,674.8,1429.5,673.4,1429.5z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 180.6865 1271.585)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-21.5" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="77400133F1DEE277.png"  transform="matrix(1 0 0 1 186 1193)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 189.1631 1224.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_66_" gradientUnits="userSpaceOnUse" x1="-1270.8925" y1="1511.3213" x2="-890.8923" y2="1511.3213" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 812.3953 852.0187)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st104" points="-2.7,2500.1 -382.3,2517.7 -391.3,2323.9 -11.7,2306.3 	"/>
	<path class="st74" d="M117.3,1298.5H-2.8c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C119.3,1297.6,118.4,1298.5,117.3,1298.5z"/>
	<rect x="-4.3" y="1202" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE274.png"  transform="matrix(1 0 0 1 -0.1442 1222.8558)">
		</image>
		<g>
			<linearGradient id="SVGID_67_" gradientUnits="userSpaceOnUse" x1="41.1498" y1="1239.0812" x2="42.8394" y2="1224.5143">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="5.3" y="1228.7" class="st105" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE27C.png"  transform="matrix(1 0 0 1 0.1003 1240.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_68_" gradientUnits="userSpaceOnUse" x1="28.9533" y1="1253.7651" x2="30.2815" y2="1242.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="5.2" y="1245.1" class="st106" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE27D.png"  transform="matrix(1 0 0 1 -0.1566 1254.8434)">
		</image>
		<g>
			<linearGradient id="SVGID_69_" gradientUnits="userSpaceOnUse" x1="33.6628" y1="1270.1837" x2="35.1657" y2="1257.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="5.2" y="1260.5" class="st107" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE27F.png"  transform="matrix(1 0 0 1 -1.0878 1271.9122)">
		</image>
		<g>
			<linearGradient id="SVGID_70_" gradientUnits="userSpaceOnUse" x1="30.1271" y1="1286.9438" x2="31.5059" y2="1275.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="4.8" y="1278" class="st108" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE27B.png"  transform="matrix(1 0 0 1 56.1003 1240.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_71_" gradientUnits="userSpaceOnUse" x1="69.0668" y1="1251.885" x2="69.9589" y2="1244.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="61.5" y="1245.1" class="st109" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="42" y1="1209" x2="82" y2="1209"/>
	<circle class="st18" cx="3.5" cy="1208.5" r="1.5"/>
	<circle class="st18" cx="9.5" cy="1208.5" r="1.5"/>
	<line class="st50" x1="200.8" y1="1673.4" x2="199.1" y2="1673.4"/>
	<line class="st38" x1="-215.3" y1="1696.5" x2="-217.5" y2="1696.5"/>
	<g class="st16">
		<g>
			<path class="st14" d="M685.4,1885.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C688,1884.3,686.8,1885.5,685.4,1885.5z"/>
		</g>
		<g>
			<path class="st72" d="M675.4,1875.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C678,1874.3,676.8,1875.5,675.4,1875.5z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="77400133F1DEE276.png"  transform="matrix(1 0 0 1 185 1620)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 188.3887 1651.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M119.3,1744.5H-0.8c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20
		C121.3,1743.6,120.4,1744.5,119.3,1744.5z"/>
	<rect x="-2.3" y="1648" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE258.png"  transform="matrix(1 0 0 1 1.8558 1668.8558)">
		</image>
		<g>
			<linearGradient id="SVGID_72_" gradientUnits="userSpaceOnUse" x1="43.1498" y1="1685.0812" x2="44.8394" y2="1670.5143">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="7.3" y="1674.7" class="st110" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE25B.png"  transform="matrix(1 0 0 1 2.1003 1686.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_73_" gradientUnits="userSpaceOnUse" x1="30.9533" y1="1699.7651" x2="32.2815" y2="1688.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="7.2" y="1691.1" class="st111" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE259.png"  transform="matrix(1 0 0 1 1.8434 1700.8434)">
		</image>
		<g>
			<linearGradient id="SVGID_74_" gradientUnits="userSpaceOnUse" x1="35.6628" y1="1716.1837" x2="37.1657" y2="1703.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="7.2" y="1706.5" class="st112" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE227.png"  transform="matrix(1 0 0 1 0.9122 1717.9122)">
		</image>
		<g>
			<linearGradient id="SVGID_75_" gradientUnits="userSpaceOnUse" x1="32.1271" y1="1732.9438" x2="33.5059" y2="1721.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="6.8" y="1724" class="st113" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE228.png"  transform="matrix(1 0 0 1 58.1003 1686.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_76_" gradientUnits="userSpaceOnUse" x1="71.0668" y1="1697.885" x2="71.9589" y2="1690.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="63.5" y="1691.1" class="st114" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="44" y1="1655" x2="84" y2="1655"/>
	<circle class="st18" cx="5.5" cy="1654.5" r="1.5"/>
	<circle class="st18" cx="11.5" cy="1654.5" r="1.5"/>
	<rect x="185.1" y="1681" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 185.1143 1694.2773)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="203.8" y1="2136.4" x2="202.1" y2="2136.4"/>
	<line class="st38" x1="-212.3" y1="2159.5" x2="-214.5" y2="2159.5"/>
	<g class="st16">
		<g>
			<path class="st14" d="M688.4,2348.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C691,2347.3,689.8,2348.5,688.4,2348.5z"/>
		</g>
		<g>
			<path class="st72" d="M678.4,2338.5l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C681,2337.3,679.8,2338.5,678.4,2338.5z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="77400133F1DEE22B.png"  transform="matrix(1 0 0 1 188 2083)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 191.3887 2114.7207)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M122.3,2207.5H2.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C124.3,2206.6,123.4,2207.5,122.3,2207.5
		z"/>
	<rect x="0.7" y="2111" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="77400133F1DEE229.png"  transform="matrix(1 0 0 1 4.8558 2131.8557)">
		</image>
		<g>
			<linearGradient id="SVGID_77_" gradientUnits="userSpaceOnUse" x1="46.1498" y1="2148.0813" x2="47.8394" y2="2133.5144">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="10.3" y="2137.7" class="st115" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="77400133F1DEE22E.png"  transform="matrix(1 0 0 1 5.1003 2149.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_78_" gradientUnits="userSpaceOnUse" x1="33.9533" y1="2162.7651" x2="35.2815" y2="2151.3135">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="10.2" y="2154.1" class="st116" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="77400133F1DEE22A.png"  transform="matrix(1 0 0 1 4.8434 2163.8435)">
		</image>
		<g>
			<linearGradient id="SVGID_79_" gradientUnits="userSpaceOnUse" x1="38.6628" y1="2179.1838" x2="40.1657" y2="2166.2263">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="10.2" y="2169.5" class="st117" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="77400133F1DEE225.png"  transform="matrix(1 0 0 1 3.9122 2180.9124)">
		</image>
		<g>
			<linearGradient id="SVGID_80_" gradientUnits="userSpaceOnUse" x1="35.1271" y1="2195.9438" x2="36.5059" y2="2184.0562">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="9.8" y="2187" class="st118" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="77400133F1DEE2CF.png"  transform="matrix(1 0 0 1 61.1003 2149.1003)">
		</image>
		<g>
			<linearGradient id="SVGID_81_" gradientUnits="userSpaceOnUse" x1="74.0668" y1="2160.885" x2="74.9589" y2="2153.1936">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="66.5" y="2154.1" class="st119" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="47" y1="2118" x2="87" y2="2118"/>
	<circle class="st18" cx="8.5" cy="2117.5" r="1.5"/>
	<circle class="st18" cx="14.5" cy="2117.5" r="1.5"/>
	<rect x="188.1" y="2144" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 188.1143 2157.2773)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<rect x="-519.5" y="2561.5" class="st64" width="1602" height="444"/>
	<path class="st120" d="M-411,605"/>
	<path class="st120" d="M-393.5,622.5"/>
	<path class="st14" d="M-291.4,802.6c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1l27.4-5.5
		c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5C-291.2,802.7-291.3,802.6-291.4,802.6
		z"/>
	<path class="st14" d="M-291.8,801.8c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-300.6,829.4c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4c0.1-0.3,0.5-0.5,0.8-0.4
		s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5C-300.4,829.5-300.5,829.5-300.6,829.4z"/>
	<linearGradient id="SVGID_82_" gradientUnits="userSpaceOnUse" x1="-254.4111" y1="528.5751" x2="-224.4088" y2="528.5751">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_83_" gradientUnits="userSpaceOnUse" x1="-254.7821" y1="528.5751" x2="-224.0378" y2="528.5751">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st121" d="M-235.3,537.5c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C-235.2,537.4-235.2,537.5-235.3,537.5z"
		/>
	<linearGradient id="SVGID_84_" gradientUnits="userSpaceOnUse" x1="-254.4128" y1="544.7586" x2="-227.1738" y2="544.7586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_85_" gradientUnits="userSpaceOnUse" x1="-254.7838" y1="544.7586" x2="-226.8028" y2="544.7586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st122" d="M-227.4,554.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-227.1,554.3-227.2,554.5-227.4,554.6z"/>
	<linearGradient id="SVGID_86_" gradientUnits="userSpaceOnUse" x1="-228.0281" y1="538.0445" x2="-216.5287" y2="538.0445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_87_" gradientUnits="userSpaceOnUse" x1="-228.3991" y1="538.0445" x2="-216.1577" y2="538.0445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st123" d="M-227.4,554.6c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		s0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-227.3,554.5-227.4,554.6-227.4,554.6z"/>
	<linearGradient id="SVGID_88_" gradientUnits="userSpaceOnUse" x1="928.724" y1="777.0777" x2="965.9116" y2="777.0777">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st124" d="M939.1,788.1c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C939.3,788.2,939.2,788.1,939.1,788.1z"/>
	<linearGradient id="SVGID_89_" gradientUnits="userSpaceOnUse" x1="912.9363" y1="786.1982" x2="964.8965" y2="786.1982">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st125" d="M923.4,809.2c-0.1-0.1-0.2-0.1-0.2-0.2L913,785.1c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C924,809.4,923.6,809.4,923.4,809.2z"/>
	<linearGradient id="SVGID_90_" gradientUnits="userSpaceOnUse" x1="923.127" y1="801.051" x2="965.9141" y2="801.051">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st126" d="M964.8,790.1c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8L950.1,812c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_91_" gradientUnits="userSpaceOnUse" x1="850.5015" y1="375.0566" x2="905.0802" y2="375.0566">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st127" d="M898.9,355.8C898.9,355.8,898.9,355.8,898.9,355.8c-0.1-0.1-0.1-0.2-0.1-0.2c0,0-0.1-0.1-0.1-0.1
		c0,0,0,0-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l-26.6-8.5c-0.2-0.1-0.5,0-0.6,0.1L850.7,366c0,0,0,0,0,0
		c-0.1,0.1-0.1,0.1-0.1,0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1,0,0.2c0,0,0,0.1,0,0.1c0,0,0,0,0,0l6.2,27.7c0.1,0.2,0.2,0.4,0.4,0.5
		l26.6,8.4c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0,0,0,0,0,0l20.5-19.1c0.2-0.2,0.2-0.4,0.2-0.6L898.9,355.8z M857.9,393.7
		l-5.9-26.3l25.2,8l5.9,26.3L857.9,393.7z"/>
	<path class="st92" d="M-209.5,39.8l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C-209.4,40-209.4,39.9-209.5,39.8z M-246.3,39.8l8.9-15.4l17.6-0.1l-8.9,15.5L-246.3,39.8z"/>
	<rect x="1139" y="157" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 1145 171.1997)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<g>
		<g>
			<path class="st46" d="M1174.1,171h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V171z M1204.8,158.1v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7h3.2
				v-13H1204.8L1204.8,158.1z M1195.8,161.3h3.2v6.5h-3.2V161.3z M1189.4,174.3h6.4V171h6.4v-13h-12.8V174.3z"/>
			<rect x="1174.1" y="158.1" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="1239.1,161.2 1239.1,167.5 1245.2,167.5 1245.2,170.7 1239,170.7 1232.7,170.7 1232.8,158.1 
			1245.2,158.1 1245.2,161.2 		"/>
		<rect x="1247.8" y="158" class="st46" width="6.4" height="12.9"/>
		
			<rect x="1253.7" y="164.6" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1424.6506 -1085.8248)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="1274.9" y="166" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 1446.0194 -1105.6869)" class="st46" width="2" height="8.3"/>
		<rect x="1261.9" y="158" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_92_" gradientUnits="userSpaceOnUse" x1="1102" y1="136.5" x2="2702" y2="136.5">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st128" x1="1102" y1="136.5" x2="2702" y2="136.5"/>
	<line class="st129" x1="1103" y1="201" x2="2702" y2="201"/>
	<g>
		<path class="st14" d="M1148,246.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C1142.5,248.9,1144.9,246.5,1148,246.5z"/>
		<path class="st14" d="M1158.8,258c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C1163.4,256.2,1161.4,258,1158.8,258z M1158.8,256c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C1156.7,255.1,1157.7,256,1158.8,256z"/>
		<path class="st14" d="M1171,252.9c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2V249h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V252.9z"/>
		<path class="st14" d="M1175.5,250.8h-1V249h1v-0.4c0-2.2,1.2-3.2,3.6-3.1v1.9c-1.1,0-1.4,0.3-1.4,1.3v0.4h1.5v1.8h-1.5v7h-2.2
			V250.8z"/>
		<path class="st14" d="M1180.4,246.6c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3
			C1181,247.9,1180.4,247.3,1180.4,246.6z M1180.6,249h2.2v8.9h-2.2V249z"/>
		<path class="st14" d="M1188.3,248.8c1.4,0,2.3,0.6,2.9,1.4V249h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3h2.2
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S1186.1,248.8,1188.3,248.8z
			 M1188.9,250.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S1190.1,250.8,1188.9,250.8z"/>
		<path class="st14" d="M1203.7,257.8h-2.2v-1.1c-0.5,0.8-1.5,1.2-2.6,1.2c-2,0-3.5-1.3-3.5-3.8V249h2.2v4.9c0,1.4,0.8,2.2,1.9,2.2
			c1.2,0,1.9-0.8,1.9-2.2V249h2.2V257.8z"/>
		<path class="st14" d="M1208,257.8h-2.2V249h2.2v1.4c0.5-0.9,1.5-1.5,2.7-1.5v2.4h-0.6c-1.3,0-2.1,0.5-2.1,2.2V257.8z"/>
		<path class="st14" d="M1215.9,258c-2.5,0-4.3-1.8-4.3-4.6c0-2.8,1.8-4.6,4.3-4.6c2.5,0,4.3,1.7,4.3,4.4c0,0.3,0,0.6-0.1,0.9h-6.3
			c0.1,1.3,1,2,2.1,2c0.9,0,1.5-0.5,1.7-1.1h2.4C1219.5,256.7,1218,258,1215.9,258z M1213.8,252.6h4.1c0-1.2-0.9-1.9-2.1-1.9
			C1214.8,250.7,1213.9,251.3,1213.8,252.6z"/>
		<path class="st14" d="M1234.7,246.6v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H1234.7z"/>
		<path class="st14" d="M1240.9,253.5h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C1244.8,251.8,1243.7,253.5,1240.9,253.5z
			 M1240.8,251.7c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H1240.8z"/>
		<path class="st14" d="M1246.3,246.6h2.5l3.5,8.3l3.5-8.3h2.5v11.2h-2.2v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V246.6z"/>
	</g>
	<g>
		<path class="st14" d="M1143,291.6h2.2v6.9c0,1.5,0.8,2.3,2.2,2.3c1.4,0,2.2-0.8,2.2-2.3v-6.9h2.2v6.9c0,2.9-2.1,4.4-4.4,4.4
			c-2.4,0-4.4-1.4-4.4-4.4V291.6z"/>
		<path class="st14" d="M1157.2,303c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C1160.6,301.8,1159.3,303,1157.2,303z"/>
		<path class="st14" d="M1162.2,291.6c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3
			C1162.7,292.9,1162.2,292.3,1162.2,291.6z M1162.4,294h2.2v8.9h-2.2V294z"/>
		<path class="st14" d="M1172.8,297.9c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2V294h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V297.9z"/>
		<path class="st14" d="M1180.4,293.8c1.4,0,2.3,0.6,2.9,1.4V294h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3h2.2
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S1178.2,293.8,1180.4,293.8z
			 M1181,295.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S1182.2,295.8,1181,295.8z"/>
		<path class="st14" d="M1200.6,291.6v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H1200.6z"/>
		<path class="st14" d="M1206.8,298.5h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C1210.7,296.8,1209.6,298.5,1206.8,298.5z
			 M1206.7,296.7c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H1206.7z"/>
		<path class="st14" d="M1212.2,291.6h2.5l3.5,8.3l3.5-8.3h2.5v11.2h-2.2v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V291.6z"/>
	</g>
	<g>
		<path class="st14" d="M1148.9,335.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C1143.4,337.9,1145.7,335.5,1148.9,335.5z"/>
		<path class="st14" d="M1158,335.6v9.4h3.6v1.8h-5.8v-11.2H1158z"/>
		<path class="st14" d="M1163,335.6h2.2v11.2h-2.2V335.6z"/>
		<path class="st14" d="M1176,335.5c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C1170.5,337.9,1172.9,335.5,1176,335.5z"/>
		<path class="st14" d="M1186.8,347c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C1191.4,345.2,1189.4,347,1186.8,347z M1186.8,345c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C1184.7,344.1,1185.7,345,1186.8,345z"/>
		<path class="st14" d="M1205,341.9c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9h-2.2v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2V338h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2h-2.2V341.9z"/>
		<path class="st14" d="M1221.4,341.9c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9h-2.2v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2V338h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2h-2.2V341.9z"/>
		<path class="st14" d="M1229,337.8c1.4,0,2.3,0.7,2.9,1.4V338h2.2v8.9h-2.2v-1.3c-0.5,0.8-1.5,1.4-2.9,1.4c-2.2,0-3.9-1.8-3.9-4.6
			S1226.8,337.8,1229,337.8z M1229.6,339.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6
			S1230.8,339.8,1229.6,339.8z"/>
		<path class="st14" d="M1242.3,341.9c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2V338h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V341.9z"/>
		<path class="st14" d="M1249.9,337.8c1.1,0,2.2,0.5,2.8,1.4V335h2.2v11.8h-2.2v-1.3c-0.5,0.8-1.5,1.5-2.8,1.5c-2.2,0-4-1.8-4-4.6
			S1247.7,337.8,1249.9,337.8z M1250.4,339.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6
			S1251.6,339.8,1250.4,339.8z"/>
		<path class="st14" d="M1260.4,347c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C1263.8,345.8,1262.5,347,1260.4,347z"/>
	</g>
	<text transform="matrix(1 0 0 1 2504.9082 174.3013)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<text transform="matrix(1 0 0 1 -190.8369 231.8696)"><tspan x="0" y="0" class="st130" style="font-family:'Poppins-Medium'; font-size:12px;">These little terminal windows could be secretly </tspan><tspan x="0" y="14.4" class="st130" style="font-family:'Poppins-Medium'; font-size:12px;">dismissable, and if you close all they just reappear again</tspan></text>
	<text transform="matrix(1 0 0 1 579.1631 543.8696)" class="st130" style="font-family:'Poppins-Medium'; font-size:12px;">&lt;----- imagine this is blinking </text>
	<text transform="matrix(1 0 0 1 192.1631 1370.8696)" class="st130" style="font-family:'Poppins-Medium'; font-size:12px;">Hmm I should probably put some CTAs in these sections</text>
	<g>
		<rect x="-110.9" y="2724" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 -110.8857 2737.2773)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod Lorem ipsum </tspan><tspan x="0" y="27" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">dolor sit amet, tetuer adipiscing elit, sed diam nonummy nibmod </tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 1141.8115 392)" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">set access level on published packages</text>
	
		<text transform="matrix(0.9997 -2.420000e-02 2.420000e-02 0.9997 1143.1205 376.649)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">access</text>
	
		<text transform="matrix(1 0 0 1 1142.8115 420.9722)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">add user</text>
	<g>
		<text transform="matrix(1 0 0 1 1142.8115 513.9722)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bin</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 1142.8115 560.9722)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bugs</text>
	</g>
	<rect x="1730" y="891" class="st132" width="64" height="27"/>
	<rect x="1886" y="890" class="st132" width="64" height="27"/>
	<g>
		<text transform="matrix(1 0 0 1 1142.8115 608.9722)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">build</text>
	</g>
	<text transform="matrix(1 0 0 1 1142.8115 652.9722)" class="st51"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bundle</tspan><tspan x="0" y="39.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">cache </tspan><tspan x="0" y="53" class="st131" style="font-family:'MyriadPro-Regular'; font-size:11px;">manipulates packages cache</tspan><tspan x="0" y="86.6" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">ci </tspan><tspan x="0" y="98.6" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">install a project with a clean slate</tspan><tspan x="0" y="132.2" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">config</tspan><tspan x="0" y="144.2" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">manage npm configuration files</tspan><tspan x="0" y="177.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dedupe</tspan><tspan x="0" y="189.8" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">reduce duplication</tspan><tspan x="0" y="223.4" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">deprecate</tspan><tspan x="0" y="235.4" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">deprecate a version of a package</tspan><tspan x="0" y="269" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dist-tag</tspan><tspan x="0" y="281" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">modify package distribution tags</tspan></text>
	<text transform="matrix(1 0 0 1 1142.8115 438)" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">add a registry user account</text>
	<g>
		<text transform="matrix(1 0 0 1 1142.8115 467.7544)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">audit</text>
		<text transform="matrix(1 0 0 1 1141.8115 482.8867)" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">run a security audit</text>
	</g>
	<text transform="matrix(1 0 0 1 1141.8115 528)" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">display npm bin folder</text>
	<rect x="1633" y="712" class="st132" width="96" height="25"/>
	<text transform="matrix(1 0 0 1 1143.8115 622)" class="st131" style="font-family:'MyriadPro-Regular'; font-size:11px;">build a package</text>
	<text transform="matrix(1 0 0 1 1142.8115 665)" class="st131" style="font-family:'MyriadPro-Regular'; font-size:11px;">removed</text>
	<rect x="1751" y="748" class="st132" width="49" height="21"/>
	<rect x="2249" y="712" class="st132" width="125" height="26"/>
	<text transform="matrix(1 0 0 1 1143.8115 575)" class="st131" style="font-family:'Poppins-Regular'; font-size:10px;">bugs for a package in a web browser maybe</text>
	<g>
		<path class="st14" d="M1276,256c-0.3,0-0.5-0.1-0.7-0.3l-6-6c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l5.3,5.3l5.3-5.3
			c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-6,6C1276.5,255.9,1276.3,256,1276,256z"/>
	</g>
	<polyline class="st133" points="1272,344 1278,338 1284,344 	"/>
	<g>
		<path class="st14" d="M1242,301.1c-0.3,0-0.6-0.1-0.8-0.3l-6-6c-0.4-0.4-0.4-1.2,0-1.6c0.4-0.4,1.2-0.4,1.6,0l5.2,5.2l5.2-5.2
			c0.4-0.4,1.2-0.4,1.6,0c0.4,0.4,0.4,1.2,0,1.6l-6,6C1242.6,301,1242.3,301.1,1242,301.1z"/>
	</g>
	<rect x="1133" y="542" class="st134" width="282" height="45"/>
	<text transform="matrix(1 0 0 1 1600.9707 286.0469)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-SemiBold'; font-size:42px;">    npm-bugs</tspan><tspan x="0" y="40" class="st98" style="font-family:'Poppins-Regular'; font-size:24px;">Bugs for a package in a web browser maybe</tspan></text>
	<text transform="matrix(1 0 0 1 1602.7861 408.293)" class="st135" style="font-family:'Poppins-Medium'; font-size:24px;">Synopsis</text>
	<text transform="matrix(1 0 0 1 1602.7861 655.293)" class="st135" style="font-family:'Poppins-Medium'; font-size:24px;">Description</text>
	<g>
		<rect x="1601.3" y="684" class="st47" width="894.4" height="310.2"/>
		<text transform="matrix(1 0 0 1 1601.2539 695.8242)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">This command tries to guess at the likely location of a package’s bug tracker URL, and then tries to open it using </tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">the</tspan><tspan x="26" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> --browser</tspan><tspan x="122" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> config param. If no package name is provided, it will search for a</tspan><tspan x="643.2" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> package.json</tspan><tspan x="768" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> in the current </tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">folder and use the  </tspan><tspan x="153.9" y="68" class="st98" style="font-family:'AndaleMono'; font-size:16px;">name</tspan><tspan x="192.3" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">  property.</tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 1602.7861 829.293)" class="st135" style="font-family:'Poppins-Medium'; font-size:24px;">Configuration</text>
	<text transform="matrix(1 0 0 1 1602.7861 872.293)" class="st136" style="font-family:'Poppins-Medium'; font-size:17px;">browser</text>
	<linearGradient id="SVGID_93_" gradientUnits="userSpaceOnUse" x1="-896.5436" y1="2766.468" x2="-1773.4564" y2="3811.532">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-2134" y="3069" class="st137" width="1598" height="440"/>
	<text transform="matrix(1 0 0 1 1603.7861 1001.293)" class="st136" style="font-family:'Poppins-Medium'; font-size:17px;">registry</text>
	<g>
		<text transform="matrix(1 0 0 1 1603.7861 1160.293)" class="st135" style="font-family:'Poppins-Medium'; font-size:24px;">See Also</text>
	</g>
	<g>
		<rect x="1619.3" y="897.2" class="st47" width="754.9" height="125.6"/>
		<text transform="matrix(1 0 0 1 1619.2998 909.084)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: OS X:</tspan><tspan x="104.6" y="0" class="st98" style="font-family:'Inconsolata-Bold'; font-size:16px;"> </tspan><tspan x="108" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;open&quot;,</tspan><tspan x="175.2" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> Windows: </tspan><tspan x="259.5" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;start&quot;</tspan><tspan x="326.7" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">, Others:  </tspan><tspan x="398.9" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;xdg-open&quot;</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: String</tspan></text>
	</g>
	<circle class="st98" cx="1609" cy="903" r="4"/>
	<circle class="st98" cx="1609" cy="938" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 1619.2998 1033.7588)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: https://registry.npmjs.org/</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: url</tspan></text>
	</g>
	<rect x="2022" y="893" class="st132" width="94" height="25"/>
	<circle class="st98" cx="1608" cy="1027" r="4"/>
	<circle class="st98" cx="1608" cy="1062" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 1608.1631 1207.9082)"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-docs</tspan><tspan x="0" y="29" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-view</tspan><tspan x="0" y="58" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-publish</tspan><tspan x="0" y="87" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-registry</tspan><tspan x="0" y="116" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="145" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="174" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npmrc</tspan><tspan x="0" y="203" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">package.json</tspan></text>
	</g>
	<line class="st2" x1="1600" y1="1175" x2="2447.5" y2="1175"/>
	<path class="st98" d="M2367.6,586h-760.3c-1.9,0-3.4-1.5-3.4-3.4V436.4c0-1.9,1.5-3.4,3.4-3.4h760.3c1.9,0,3.4,1.5,3.4,3.4v146.3
		C2371,584.5,2369.5,586,2367.6,586z"/>
	<text transform="matrix(1 0 0 1 1630.2207 492.5747)"><tspan x="0" y="0" class="st8" style="font-family:'AndaleMono'; font-size:30px;">npm bugs [&lt;pkgname&gt;]</tspan><tspan x="0" y="60" class="st8" style="font-family:'AndaleMono'; font-size:30px;">aliases: issues</tspan></text>
	<rect x="2100" y="1566" class="st132" width="247" height="30"/>
	<text transform="matrix(1 0 0 1 1633.667 1551.9297)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">        Found a typo?</tspan><tspan x="147.4" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;"> Let us know!</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">The current stable version of npm is here. To upgrade run:  </tspan><tspan x="468.1" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;">npm install npm@latest -g</tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">To report bugs or submit feature requests for the docs, please post </tspan><tspan x="537" y="68" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here</tspan><tspan x="573.8" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">. </tspan><tspan x="0" y="102" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Submit npm issues</tspan><tspan x="151.9" y="102" style="font-family:'Poppins-Regular'; font-size:16px;"> </tspan><tspan x="156.2" y="102" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here.</tspan></text>
	<rect x="1102" y="201" class="st138" width="330" height="1207"/>
	<linearGradient id="SVGID_94_" gradientUnits="userSpaceOnUse" x1="1608.9974" y1="269.3333" x2="1628.4839" y2="269.3333">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_95_" gradientUnits="userSpaceOnUse" x1="1608.7565" y1="269.3333" x2="1628.7249" y2="269.3333">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st139" d="M1621.4,275.1c0,0-0.1,0-0.1,0l-12-1.2c-0.2,0-0.3-0.1-0.3-0.3c0,0,0-0.1,0-0.1l6.9-9.9
		c0.1-0.1,0.2-0.1,0.3-0.1l12,1.2c0.2,0,0.3,0.1,0.3,0.3c0,0,0,0.1,0,0.1l-6.9,9.9C1621.5,275.1,1621.4,275.1,1621.4,275.1z"/>
	<linearGradient id="SVGID_96_" gradientUnits="userSpaceOnUse" x1="1608.9963" y1="279.8445" x2="1626.688" y2="279.8445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_97_" gradientUnits="userSpaceOnUse" x1="1608.7554" y1="279.8445" x2="1626.929" y2="279.8445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st140" d="M1626.5,286.2c0,0-0.1,0-0.1,0l-12-1.1c-0.1,0-0.2-0.1-0.2-0.2l-5.1-11.1c-0.1-0.1,0-0.3,0.1-0.4
		c0.1-0.1,0.3,0,0.4,0.1l5.1,11l11.4,1.1L1621,275c-0.1-0.1,0-0.3,0.1-0.4c0.1-0.1,0.3,0,0.4,0.1l5.1,11.1
		C1626.7,286,1626.7,286.2,1626.5,286.2z"/>
	<linearGradient id="SVGID_98_" gradientUnits="userSpaceOnUse" x1="1626.1332" y1="275.4837" x2="1633.6021" y2="275.4837">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_99_" gradientUnits="userSpaceOnUse" x1="1625.8922" y1="275.4837" x2="1633.843" y2="275.4837">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st141" d="M1626.5,286.2c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.2-0.3-0.1-0.4l6.8-9.8l-5-11c-0.1-0.1,0-0.3,0.1-0.4
		s0.3,0,0.4,0.1l5.1,11.1c0,0.1,0,0.2,0,0.3l-6.9,9.9C1626.6,286.2,1626.6,286.2,1626.5,286.2z"/>
	<path class="st120" d="M-1533,3205"/>
	<path class="st120" d="M-1915.5,3050.5"/>
	<path class="st142" d="M-2011.4,3318.6c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C-2011.2,3318.7-2011.3,3318.6-2011.4,3318.6z"/>
	<path class="st14" d="M-2011.8,3317.8c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-2020.6,3345.4c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
		C-2020.4,3345.5-2020.5,3345.5-2020.6,3345.4z"/>
	<linearGradient id="SVGID_100_" gradientUnits="userSpaceOnUse" x1="-1519.4111" y1="3128.575" x2="-1489.4088" y2="3128.575">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_101_" gradientUnits="userSpaceOnUse" x1="-1519.7821" y1="3128.575" x2="-1489.0377" y2="3128.575">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st143" d="M-1500.3,3137.5c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C-1500.2,3137.4-1500.2,3137.5-1500.3,3137.5z"/>
	<linearGradient id="SVGID_102_" gradientUnits="userSpaceOnUse" x1="-1519.4128" y1="3144.7585" x2="-1492.1738" y2="3144.7585">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st144" d="M-1492.4,3154.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6s0.5,0,0.6,0.2l7.9,17.1
		C-1492.1,3154.3-1492.2,3154.5-1492.4,3154.6z"/>
	<linearGradient id="SVGID_103_" gradientUnits="userSpaceOnUse" x1="-1493.0281" y1="3138.0444" x2="-1481.5287" y2="3138.0444">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st145" d="M-1492.4,3154.6c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-1492.3,3154.5-1492.4,3154.6-1492.4,3154.6z"/>
	<path class="st120" d="M-661.5,2749"/>
	<path class="st92" d="M-721.1,3182.6c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19C-720.9,3182.6-721,3182.6-721.1,3182.6
		z"/>
	<path class="st92" d="M-714.9,3210.3c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
		C-714.3,3209.9-714.6,3210.2-714.9,3210.3z"/>
	<path class="st92" d="M-714.9,3210.3c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-714.7,3210.2-714.8,3210.3-714.9,3210.3z"/>
	<path class="st93" d="M-1077.9,3384.1c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C-1077.7,3384.2-1077.8,3384.1-1077.9,3384.1
		z"/>
	<path class="st93" d="M-1093.6,3405.2c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C-1093,3405.4-1093.4,3405.4-1093.6,3405.2z"/>
	<path class="st93" d="M-1052.2,3386.1c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<g>
		<rect x="-1800.9" y="2738" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 -1776.0029 2755.7178)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy</tspan><tspan x="-25.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="-20.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nibh</tspan><tspan x="32.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="37.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">euismod</tspan><tspan x="142.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="148.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem</tspan><tspan x="222.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="227.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">ipsum</tspan><tspan x="302.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="307.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">dolor</tspan><tspan x="369.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="374.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sit</tspan><tspan x="401.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="407" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">amet,</tspan><tspan x="476.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="481.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">tetuer</tspan><tspan x="552.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="558.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">adipiscing</tspan><tspan x="683.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="688.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">elit,</tspan><tspan x="728.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="733.8" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sed</tspan><tspan x="777.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="782.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">diam</tspan><tspan x="845.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="851" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nonum</tspan><tspan x="937.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">-</tspan><tspan x="393.3" y="100" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">my nibmod </tspan></text>
	</g>
	<g>
		<rect x="-1330.3" y="2917.8" class="st14" width="23.3" height="6.1"/>
	</g>
	<linearGradient id="SVGID_104_" gradientUnits="userSpaceOnUse" x1="-1689.8535" y1="344.75" x2="-1688.6465" y2="344.75">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st146" x1="-1689" y1="345" x2="-1689.5" y2="344.5"/>
	<line class="st40" x1="-1643.1" y1="277.8" x2="-1645.8" y2="277.8"/>
	<line class="st40" x1="-1674.8" y1="305.1" x2="-1677.5" y2="305.1"/>
	<line class="st40" x1="-1478.2" y1="336.2" x2="-1480.9" y2="336.2"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="77400133F1DEE2CD.png"  transform="matrix(1 0 0 1 -1800 241)">
			</image>
			<g>
				<path class="st98" d="M-1028.1,259.7v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4V259.7c0-1.7,1.3-3,3-3h750.6
					C-1029.4,256.7-1028.1,258-1028.1,259.7z"/>
				<path class="st81" d="M-1028.1,259.7v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4V259.7c0-1.7,1.3-3,3-3h750.6
					C-1029.4,256.7-1028.1,258-1028.1,259.7z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-1028.1,260v21.7c0,1.6-1.4,3-3,3h-750.6c-1.7,0-3-1.4-3-3V260c0-1.7,1.3-3,3-3h750.6
				C-1029.4,257-1028.1,258.3-1028.1,260z"/>
			<path class="st62" d="M-1028.1,260v21.7c0,1.6-1.4,3-3,3h-750.6c-1.7,0-3-1.4-3-3V260c0-1.7,1.3-3,3-3h750.6
				C-1029.4,257-1028.1,258.3-1028.1,260z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="-1642.6" y1="352.7" x2="-1645.4" y2="352.7"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="77400133F1DEE2C3.png"  transform="matrix(1 0 0 1 -1761 297)">
			</image>
			<g>
				<path class="st98" d="M-989.8,315.4v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4V315.4c0-1.7,1.3-3,3-3h750.6
					C-991.1,312.4-989.8,313.7-989.8,315.4z"/>
				<path class="st99" d="M-989.8,315.4v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4V315.4c0-1.7,1.3-3,3-3h750.6
					C-991.1,312.4-989.8,313.7-989.8,315.4z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-990.1,315v21.7c0,1.6-1.3,3-3,3h-750.6c-1.7,0-3-1.4-3-3V315c0-1.7,1.3-3,3-3h750.6
				C-991.4,312-990.1,313.3-990.1,315z"/>
			<path class="st62" d="M-990.1,315v21.7c0,1.6-1.3,3-3,3h-750.6c-1.7,0-3-1.4-3-3V315c0-1.7,1.3-3,3-3h750.6
				C-991.4,312-990.1,313.3-990.1,315z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="77400133F1DEE2C5.png"  transform="matrix(1 0 0 1 -1727 357)">
			</image>
			<g>
				<path class="st98" d="M-955.4,373.8v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1V373.8c0-1.1,0.9-2,2-2h752.6
					C-956.3,371.8-955.4,372.7-955.4,373.8z"/>
				<path class="st99" d="M-955.4,373.8v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1V373.8c0-1.1,0.9-2,2-2h752.6
					C-956.3,371.8-955.4,372.7-955.4,373.8z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="-1221.3" y="524.8" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="-1611.6,518.7 -1615.5,514 -1604.3,504.4 -1615.5,494.8 -1611.6,490.1 -1594.8,504.4 				"/>
			</g>
			<rect x="-1616.7" y="591.4" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 -1616.7363 606.229)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="-1614.7" y="691" class="st14" width="230" height="59.2"/>
				<rect x="-1608.8" y="696.8" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 -1557.8589 729.4116)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M-955.1,375v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0V375c0-1.7,1.3-3,3-3h750.6
				C-956.4,372-955.1,373.3-955.1,375z"/>
			<path class="st62" d="M-955.1,375v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0V375c0-1.7,1.3-3,3-3h750.6
				C-956.4,372-955.1,373.3-955.1,375z"/>
		</g>
		<line class="st66" x1="-1695.4" y1="381.7" x2="-1687" y2="390.6"/>
		<line class="st66" x1="-1695.7" y1="390.3" x2="-1686.7" y2="381.9"/>
		<line class="st66" x1="-1735.4" y1="321.7" x2="-1727" y2="330.6"/>
		<line class="st66" x1="-1735.7" y1="330.3" x2="-1726.7" y2="321.9"/>
		<line class="st66" x1="-1772.4" y1="266.7" x2="-1764" y2="275.6"/>
		<line class="st66" x1="-1772.7" y1="275.3" x2="-1763.7" y2="266.9"/>
	</g>
	<path class="st120" d="M-2068,620"/>
	<path class="st120" d="M-2050.5,637.5"/>
	<path class="st14" d="M-1948.4,817.6c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C-1948.2,817.7-1948.3,817.6-1948.4,817.6z"/>
	<path class="st14" d="M-1948.8,816.8c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-1957.6,844.4c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5C-1957.4,844.5-1957.5,844.5-1957.6,844.4z
		"/>
	<linearGradient id="SVGID_105_" gradientUnits="userSpaceOnUse" x1="-1911.4111" y1="543.5751" x2="-1881.4088" y2="543.5751">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_106_" gradientUnits="userSpaceOnUse" x1="-1911.7821" y1="543.5751" x2="-1881.0377" y2="543.5751">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st147" d="M-1892.3,552.5c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C-1892.2,552.4-1892.2,552.5-1892.3,552.5
		z"/>
	<linearGradient id="SVGID_107_" gradientUnits="userSpaceOnUse" x1="-1911.4128" y1="559.7586" x2="-1884.1738" y2="559.7586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_108_" gradientUnits="userSpaceOnUse" x1="-1911.7838" y1="559.7586" x2="-1883.8027" y2="559.7586">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st148" d="M-1884.4,569.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-1884.1,569.3-1884.2,569.5-1884.4,569.6z"/>
	<linearGradient id="SVGID_109_" gradientUnits="userSpaceOnUse" x1="-1885.0281" y1="553.0445" x2="-1873.5287" y2="553.0445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_110_" gradientUnits="userSpaceOnUse" x1="-1885.399" y1="553.0445" x2="-1873.1577" y2="553.0445">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st149" d="M-1884.4,569.6c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-1884.3,569.5-1884.4,569.6-1884.4,569.6z"/>
	<path class="st92" d="M-2070.3,286.8c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.2,0,0.5-0.2,0.6c-0.1,0-0.1,0.1-0.2,0.1l-18.6,0.1
		c-0.2,0-0.3-0.1-0.4-0.2l-9.2-16.2c-0.1-0.2,0-0.5,0.2-0.6c0.1,0,0.1-0.1,0.2-0.1l18.6-0.1
		C-2070.4,286.7-2070.4,286.8-2070.3,286.8z"/>
	<path class="st92" d="M-2070.1,287.4c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.2,0.4-0.3,0.6-0.2
		c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.1,0.1,0.3,0,0.4l-9.4,16.4c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6"/>
	<path class="st92" d="M-2060.9,270.5c0.1,0.1,0.2,0.2,0.2,0.4c0,0.2-0.2,0.4-0.4,0.4l-18.4,0.1l-9.3,16.1c-0.1,0.2-0.4,0.3-0.6,0.2
		s-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.1,0.2-0.2,0.4-0.2l18.6-0.1C-2061,270.4-2060.9,270.4-2060.9,270.5z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="356" height="93" xlink:href="77400133F1DEE2C7.png"  transform="matrix(1 0 0 1 -1583.5894 461.4106)">
		</image>
		<g>
			<g>
				<path class="st8" d="M-1582.4,530.1v-45.8h10.8v4.7c1.5-1.7,3.3-3.1,5.4-4.1c2.1-1,4.2-1.6,6.3-1.6c2.2,0,4.3,0.3,6.1,1
					c1.8,0.7,3.4,1.8,4.7,3.3c1.3,1.5,2.3,3.5,3,5.9c0.7,2.4,1.1,5.3,1.1,8.7v27.7h-10.5v-27.6c0-1.8-0.2-3.3-0.5-4.6
					c-0.3-1.3-0.8-2.3-1.3-3.1c-0.6-0.8-1.2-1.4-2-1.8c-0.8-0.4-1.6-0.6-2.6-0.6c-1.3,0-2.6,0.3-3.8,0.8c-1.2,0.6-2.2,1.3-3.1,2.3
					c-0.9,1-1.5,2.2-2,3.6c-0.5,1.4-0.7,2.9-0.7,4.6v26.5H-1582.4z"/>
				<path class="st8" d="M-1531.3,484.3h10.3v4.7c0.7-1,1.5-1.9,2.4-2.6c0.9-0.7,1.8-1.3,2.7-1.8c0.9-0.5,1.9-0.8,2.8-1
					c1-0.2,1.9-0.3,2.8-0.3c2.6,0,5.1,0.5,7.4,1.6c2.3,1,4.3,2.6,6,4.7s3.1,4.7,4.1,7.8c1,3.1,1.5,6.8,1.5,11.1
					c0,3.9-0.5,7.2-1.5,10.1c-1,2.9-2.4,5.3-4.1,7.2c-1.7,1.9-3.7,3.4-6,4.4s-4.7,1.5-7.2,1.5c-1.8,0-3.7-0.4-5.6-1.2
					c-1.9-0.8-3.5-1.9-4.7-3.3v19.4h-10.7L-1531.3,484.3z M-1520.5,506.8c0,2.8,0.2,5.2,0.6,7.3c0.4,2,1,3.7,1.7,4.9
					c0.7,1.2,1.6,2.2,2.7,2.7c1.1,0.6,2.3,0.9,3.6,0.9c1.1,0,2.2-0.3,3.3-0.8c1.1-0.5,2.2-1.4,3.2-2.5c1-1.1,1.7-2.6,2.3-4.5
					c0.6-1.9,0.9-4.2,0.9-6.9c0-5-0.9-8.8-2.6-11.5c-1.7-2.7-4.2-4-7.5-4c-1.7,0-3.1,0.4-4.2,1.2c-1.1,0.8-1.9,1.8-2.6,3.2
					c-0.6,1.3-1.1,2.9-1.3,4.6C-1520.4,503.1-1520.5,504.9-1520.5,506.8z"/>
				<path class="st8" d="M-1481.7,530.1v-46.3h9.2v2.8c1.4-1.5,2.8-2.6,4.2-3.3c1.4-0.7,2.7-1,4-1c0.6,0,1.2,0.1,1.9,0.3
					c0.7,0.2,1.3,0.5,2,0.9c0.6,0.4,1.3,1,1.9,1.7c0.6,0.7,1.1,1.6,1.6,2.6c1.1-1.8,2.5-3.1,4.1-4.1c1.7-0.9,3.3-1.4,5-1.4
					c1.7,0,3.1,0.3,4.3,0.9c1.1,0.6,2,1.5,2.7,2.6c0.7,1.2,1.2,2.6,1.5,4.4c0.3,1.7,0.4,3.7,0.4,6v33.8h-9.9v-32
					c0-2.5-0.2-4.4-0.6-5.5c-0.4-1.1-1.2-1.7-2.2-1.7c-2.4,0-3.6,3-3.6,9v30.1h-9.9v-31.5c0-1.6-0.1-2.9-0.2-3.9s-0.4-1.7-0.7-2.3
					c-0.3-0.6-0.6-0.9-0.9-1.1c-0.3-0.2-0.6-0.3-1-0.3c-0.6,0-1.1,0.1-1.6,0.4c-0.5,0.3-0.9,0.7-1.2,1.4c-0.3,0.7-0.6,1.6-0.8,2.7
					c-0.2,1.1-0.3,2.6-0.3,4.3v30.4H-1481.7z"/>
				<path class="st8" d="M-1344.2,500.4c-0.3-0.1-0.4-0.2-0.6-0.4c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.2-0.1-0.4-0.2-0.7
					c-0.1-0.3-0.1-0.6-0.2-0.8c-0.9-1.5-2.2-2.8-3.8-3.8c-1.6-1-3.6-1.6-6-1.6c-1.6,0-3.1,0.4-4.5,1.1c-1.4,0.7-2.7,1.7-3.8,3
					c-1.1,1.3-1.9,2.8-2.5,4.6c-0.6,1.8-0.9,3.7-0.9,5.9c0,2.2,0.3,4.2,0.9,6c0.6,1.8,1.4,3.4,2.4,4.8c1.1,1.4,2.3,2.5,3.7,3.2
					c1.4,0.8,3,1.2,4.8,1.2c0.9,0,1.8-0.1,2.7-0.3c0.9-0.2,1.9-0.5,2.9-1c1-0.5,1.9-1.1,2.9-1.9c0.9-0.8,1.8-1.7,2.7-2.8l6.2,7.4
					c-2.6,2.9-5.3,5-8.3,6.2c-3,1.3-6.2,1.9-9.6,1.9c-3.3,0-6.2-0.6-9-1.8c-2.7-1.2-5.1-2.9-7-5c-1.9-2.1-3.5-4.7-4.6-7.6
					c-1.1-2.9-1.7-6.1-1.7-9.6c0-3.4,0.5-6.6,1.6-9.6c1.1-3,2.6-5.5,4.6-7.7c2-2.2,4.4-3.9,7.2-5.1c2.8-1.3,5.8-1.9,9.2-1.9
					c1.8,0,3.6,0.2,5.4,0.6c1.8,0.4,3.4,0.9,4.9,1.7c1.5,0.7,2.9,1.6,4.2,2.7c1.3,1.1,2.4,2.4,3.3,3.8L-1344.2,500.4z"/>
				<path class="st8" d="M-1322.9,463.9h22.4v57.7h12.2v8.5h-35.2v-8.5h12.2v-49.1h-11.7V463.9z"/>
				<path class="st8" d="M-1268.5,484.3h20.9v37.3h9.9v8.5h-31.3v-8.5h10.8v-28.7h-10.3V484.3z M-1252.8,463.8
					c0.9,0,1.8,0.2,2.6,0.5c0.8,0.3,1.5,0.8,2.2,1.4c0.6,0.6,1.1,1.3,1.4,2c0.3,0.8,0.5,1.6,0.5,2.4c0,0.9-0.2,1.7-0.5,2.5
					c-0.4,0.8-0.8,1.5-1.4,2c-0.6,0.6-1.3,1-2.2,1.3c-0.8,0.3-1.7,0.5-2.6,0.5c-0.9,0-1.8-0.2-2.6-0.5c-0.8-0.3-1.5-0.8-2.2-1.3
					c-0.6-0.6-1.1-1.2-1.4-2s-0.5-1.6-0.5-2.5c0-0.8,0.1-1.5,0.4-2.3c0.3-0.7,0.7-1.4,1.2-2c0.5-0.6,1.2-1.1,2.1-1.5
					C-1254.9,464-1253.9,463.8-1252.8,463.8z"/>
			</g>
		</g>
	</g>
	<path class="st150" d="M1569.9,1955.1"/>
	<path class="st151" d="M1202.5,2029.2c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C1202.6,2029.2,1202.5,2029.2,1202.5,2029.2z
		"/>
	<path class="st14" d="M1202.2,2028.7c0.1-0.2,0.3-0.3,0.5-0.2s0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M1196.9,2045.3c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		s0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C1197,2045.3,1197,2045.3,1196.9,2045.3z"/>
	<linearGradient id="SVGID_111_" gradientUnits="userSpaceOnUse" x1="1578.0706" y1="1948.8536" x2="1596.0978" y2="1948.8536">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_112_" gradientUnits="userSpaceOnUse" x1="1577.8477" y1="1948.8536" x2="1596.3207" y2="1948.8536">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st152" d="M1589.5,1954.2c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C1589.6,1954.2,1589.6,1954.2,1589.5,1954.2z"/>
	<linearGradient id="SVGID_113_" gradientUnits="userSpaceOnUse" x1="1578.0696" y1="1958.5778" x2="1594.4364" y2="1958.5778">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st153" d="M1594.3,1964.5c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3
		C1594.5,1964.3,1594.4,1964.4,1594.3,1964.5z"/>
	<linearGradient id="SVGID_114_" gradientUnits="userSpaceOnUse" x1="1593.9231" y1="1954.5435" x2="1600.8326" y2="1954.5435">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st154" d="M1594.3,1964.5c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C1594.4,1964.4,1594.3,1964.5,1594.3,1964.5z"/>
	<path class="st92" d="M2570.8,2055.5c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4
		C2570.9,2055.5,2570.8,2055.5,2570.8,2055.5z"/>
	<path class="st92" d="M2574.5,2072.1c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C2574.8,2071.9,2574.7,2072.1,2574.5,2072.1z"/>
	<path class="st92" d="M2574.5,2072.1c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C2574.6,2072.1,2574.5,2072.1,2574.5,2072.1z"/>
	<path class="st93" d="M2321.2,1978.5c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C2321.3,1978.6,2321.2,1978.5,2321.2,1978.5z"
		/>
	<path class="st93" d="M2311.7,1991.2c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		s0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C2312.1,1991.3,2311.9,1991.3,2311.7,1991.2z"/>
	<path class="st93" d="M2336.6,1979.7c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1
		l-15.5-1.7c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<text transform="matrix(1 0 0 1 1738.54 2055.5986)" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Some footer text or something here </text>
	<path class="st92" d="M-392.5,285.8l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C-392.4,286-392.4,285.9-392.5,285.8z M-429.3,285.8l8.9-15.4l17.6-0.1l-8.9,15.5L-429.3,285.8
		z"/>
	<linearGradient id="SVGID_115_" gradientUnits="userSpaceOnUse" x1="-166.5916" y1="30" x2="-110.4084" y2="30">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st155" d="M-110.4,24.5c0-0.1,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1-0.1-0.1c0,0,0,0,0,0l-18.2-21.2
		c-0.2-0.2-0.4-0.3-0.6-0.2l-27.4,5.5c0,0,0,0,0,0c-0.1,0-0.2,0.1-0.2,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1
		c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0l-9.3,26.9c-0.1,0.2,0,0.5,0.1,0.6l18.3,21.1c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0,0.2,0,0.3,0
		c0,0,0,0,0,0l27.4-5.5c0.2,0,0.4-0.2,0.5-0.4l9.2-26.8c0,0,0-0.1,0-0.1C-110.4,24.5-110.4,24.5-110.4,24.5z M-120.8,50.7l-26,5.2
		l8.7-25.4l26-5.2L-120.8,50.7z"/>
	<linearGradient id="SVGID_116_" gradientUnits="userSpaceOnUse" x1="-64.4846" y1="27" x2="-11.5153" y2="27">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st156" d="M-11.5,30c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0,0,0,0,0,0l-10.1-24
		c-0.1-0.2-0.3-0.3-0.5-0.4L-48,2.4c0,0,0,0,0,0c-0.1,0-0.2,0-0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0-0.1,0-0.1,0.1
		c0,0,0,0,0,0l-15.8,21.2c-0.1,0.2-0.2,0.4-0.1,0.6l10.2,23.9c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.2,0.1c0,0,0,0,0,0l25.9,2.9
		c0.2,0,0.4-0.1,0.5-0.2l15.7-21.1C-11.6,30.2-11.6,30.2-11.5,30C-11.6,30.1-11.6,30.1-11.5,30z M-16.4,30l2.1,0.2l-0.2,0.1
		C-15,30.2-15.6,30.1-16.4,30z M-48.2,4.2l9.6,22.7l-14.9,20.1l-9.6-22.6L-48.2,4.2z"/>
	<g class="st51">
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="65" xlink:href="77400133F1DEE2C2.png"  transform="matrix(1 0 0 1 -5 -5)">
		</image>
		<g>
			<linearGradient id="SVGID_117_" gradientUnits="userSpaceOnUse" x1="0.9599" y1="26.5" x2="51.0401" y2="26.5">
				<stop  offset="0" style="stop-color:#913FFF"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<path class="st157" d="M45.4,8.9C45.3,8.8,45.3,8.8,45.4,8.9c0-0.1-0.1-0.1-0.1-0.2c0,0-0.1-0.1-0.1-0.1c0,0,0,0-0.1-0.1
				c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0L20.5,0.6c-0.2-0.1-0.4,0-0.6,0.1L1.1,18.2c0,0,0,0,0,0C1.1,18.3,1,18.4,1,18.4c0,0,0,0,0,0
				c0,0,0,0,0,0c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0l5.7,25.5c0,0.2,0.2,0.4,0.4,0.4l24.4,7.8c0.1,0,0.2,0,0.3,0
				c0.1,0,0.2-0.1,0.2-0.1c0,0,0,0,0,0l18.8-17.5c0.2-0.1,0.2-0.4,0.2-0.6L45.4,8.9z M7.7,43.6L2.3,19.5l23.1,7.4L30.9,51L7.7,43.6z
				"/>
		</g>
	</g>
	<linearGradient id="SVGID_118_" gradientUnits="userSpaceOnUse" x1="-380.942" y1="31.1228" x2="-353.7031" y2="31.1228">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_119_" gradientUnits="userSpaceOnUse" x1="-381.3131" y1="31.1228" x2="-353.3321" y2="31.1228">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st158" d="M-354,41c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-353.6,40.6-353.7,40.9-354,41z"/>
	<linearGradient id="SVGID_120_" gradientUnits="userSpaceOnUse" x1="-354.5573" y1="24.4087" x2="-343.058" y2="24.4087">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_121_" gradientUnits="userSpaceOnUse" x1="-354.9283" y1="24.4087" x2="-342.6869" y2="24.4087">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st159" d="M-354,41c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-353.8,40.9-353.9,40.9-354,41z"/>
	<linearGradient id="SVGID_122_" gradientUnits="userSpaceOnUse" x1="-380.9404" y1="14.9392" x2="-350.9381" y2="14.9392">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_123_" gradientUnits="userSpaceOnUse" x1="-381.3114" y1="14.9392" x2="-350.567" y2="14.9392">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st160" d="M-361.8,23.9c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		C-370.1,6-370,6-369.8,6l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C-361.7,23.8-361.8,23.8-361.8,23.9z"/>
	<g>
		<path class="st120" d="M-1538.9,3708.2"/>
		<path class="st142" d="M-2017.3,3821.8c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
			l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
			C-2017.1,3821.8-2017.2,3821.8-2017.3,3821.8z"/>
		<path class="st14" d="M-2017.7,3820.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
			c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8"/>
		<path class="st14" d="M-2026.5,3848.6c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
			c0.1-0.3,0.5-0.5,0.8-0.4s0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
			C-2026.3,3848.6-2026.4,3848.6-2026.5,3848.6z"/>
		<linearGradient id="SVGID_124_" gradientUnits="userSpaceOnUse" x1="-1525.2891" y1="3631.7312" x2="-1495.2867" y2="3631.7312">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="0.3721" style="stop-color:#FB8719"/>
			<stop  offset="0.5095" style="stop-color:#FA8420"/>
			<stop  offset="0.608" style="stop-color:#F9802C"/>
			<stop  offset="0.6881" style="stop-color:#F7793D"/>
			<stop  offset="0.7568" style="stop-color:#F47053"/>
			<stop  offset="0.8177" style="stop-color:#F1656E"/>
			<stop  offset="0.8729" style="stop-color:#ED578F"/>
			<stop  offset="0.9237" style="stop-color:#E948B5"/>
			<stop  offset="0.9691" style="stop-color:#E437DE"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<linearGradient id="SVGID_125_" gradientUnits="userSpaceOnUse" x1="-1525.6602" y1="3631.7312" x2="-1494.9158" y2="3631.7312">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st161" d="M-1506.2,3640.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
			c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
			C-1506.1,3640.6-1506.1,3640.6-1506.2,3640.6z"/>
		<linearGradient id="SVGID_126_" gradientUnits="userSpaceOnUse" x1="-1525.2908" y1="3647.9148" x2="-1498.0518" y2="3647.9148">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st162" d="M-1498.3,3657.8c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
			s0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6s0.5,0,0.6,0.2l7.9,17.1
			C-1498,3657.4-1498.1,3657.7-1498.3,3657.8z"/>
		<linearGradient id="SVGID_127_" gradientUnits="userSpaceOnUse" x1="-1498.906" y1="3641.2007" x2="-1487.4066" y2="3641.2007">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st163" d="M-1498.3,3657.8c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9
			c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2
			C-1498.2,3657.7-1498.2,3657.7-1498.3,3657.8z"/>
		<path class="st92" d="M-727,3685.8c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
			c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19
			C-726.8,3685.7-726.9,3685.8-727,3685.8z"/>
		<path class="st92" d="M-720.8,3713.5c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
			c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
			C-720.2,3713-720.4,3713.4-720.8,3713.5z"/>
		<path class="st92" d="M-720.8,3713.5c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
			c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-720.6,3713.4-720.7,3713.4-720.8,3713.5z"/>
		<path class="st93" d="M-1083.8,3887.2c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
			c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9
			C-1083.6,3887.3-1083.7,3887.3-1083.8,3887.2z"/>
		<path class="st93" d="M-1099.5,3908.4c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
			c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1
			c0.3,0.2,25.3,4,25.1,4.3l-40.7,17.7C-1098.9,3908.5-1099.2,3908.6-1099.5,3908.4z"/>
		<path class="st93" d="M-1058,3889.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
			l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	</g>
	<linearGradient id="SVGID_128_" gradientUnits="userSpaceOnUse" x1="1425.5712" y1="2057.9954" x2="1458.6107" y2="2057.9954">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st164" d="M1454.9,2046.4C1454.9,2046.3,1454.8,2046.3,1454.9,2046.4c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L1454.9,2046.4z M1430,2069.3l-3.6-15.9l15.3,4.9l3.6,15.9L1430,2069.3z"/>
	<linearGradient id="SVGID_129_" gradientUnits="userSpaceOnUse" x1="1978.929" y1="1980.3079" x2="2000.6652" y2="1980.3079">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st165" d="M2000.7,1978.2C2000.7,1978.2,2000.7,1978.1,2000.7,1978.2C2000.7,1978.1,2000.7,1978.1,2000.7,1978.2
		c0-0.1,0-0.1,0-0.1c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.6,2.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		l10.6-2.1c0.1,0,0.2-0.1,0.2-0.2L2000.7,1978.2C2000.7,1978.2,2000.7,1978.2,2000.7,1978.2
		C2000.7,1978.2,2000.7,1978.2,2000.7,1978.2z M1996.7,1988.3l-10,2l3.4-9.8l10-2L1996.7,1988.3z"/>
	<linearGradient id="SVGID_130_" gradientUnits="userSpaceOnUse" x1="2580.5369" y1="1947.5522" x2="2600.9146" y2="1947.5522">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st166" d="M2598.6,1940.4C2598.6,1940.4,2598.6,1940.4,2598.6,1940.4C2598.6,1940.3,2598.6,1940.3,2598.6,1940.4
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L2598.6,1940.4z M2583.3,1954.5l-2.2-9.8l9.4,3
		l2.2,9.8L2583.3,1954.5z"/>
	<path class="st150" d="M1576.4,2220.9"/>
	<path class="st151" d="M1209,2294.9c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C1209.1,2294.9,1209.1,2294.9,1209,2294.9z"
		/>
	<path class="st14" d="M1208.8,2294.4c0.1-0.2,0.3-0.3,0.5-0.2s0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M1203.4,2311c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C1203.6,2311,1203.5,2311,1203.4,2311z"/>
	<linearGradient id="SVGID_131_" gradientUnits="userSpaceOnUse" x1="1584.6115" y1="2214.593" x2="1602.6387" y2="2214.593">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_132_" gradientUnits="userSpaceOnUse" x1="1584.3885" y1="2214.593" x2="1602.8616" y2="2214.593">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st167" d="M1596.1,2219.9c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C1596.2,2219.9,1596.1,2219.9,1596.1,2219.9z"/>
	<linearGradient id="SVGID_133_" gradientUnits="userSpaceOnUse" x1="1584.6105" y1="2224.3169" x2="1600.9773" y2="2224.3169">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st168" d="M1600.8,2230.2c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3
		C1601,2230,1601,2230.2,1600.8,2230.2z"/>
	<linearGradient id="SVGID_134_" gradientUnits="userSpaceOnUse" x1="1600.464" y1="2220.2827" x2="1607.3735" y2="2220.2827">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st169" d="M1600.8,2230.2c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		s0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C1600.9,2230.2,1600.9,2230.2,1600.8,2230.2z"/>
	<path class="st92" d="M2577.3,2321.2c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4
		C2577.4,2321.2,2577.4,2321.2,2577.3,2321.2z"/>
	<path class="st92" d="M2581,2337.9c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C2581.3,2337.6,2581.2,2337.8,2581,2337.9z"/>
	<path class="st92" d="M2581,2337.9c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C2581.1,2337.8,2581.1,2337.9,2581,2337.9z"/>
	<path class="st93" d="M2327.7,2244.2c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C2327.9,2244.3,2327.8,2244.3,2327.7,2244.2z"
		/>
	<path class="st93" d="M2318.3,2256.9c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		c0.2,0.1,0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C2318.7,2257,2318.4,2257.1,2318.3,2256.9z"/>
	<path class="st93" d="M2343.2,2245.5c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1
		l-15.5-1.7c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<linearGradient id="SVGID_135_" gradientUnits="userSpaceOnUse" x1="1432.1119" y1="2323.7346" x2="1465.1516" y2="2323.7346">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st170" d="M1461.4,2312.1C1461.4,2312.1,1461.4,2312.1,1461.4,2312.1c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L1461.4,2312.1z M1436.6,2335l-3.6-15.9l15.3,4.9l3.6,15.9L1436.6,2335z"/>
	<linearGradient id="SVGID_136_" gradientUnits="userSpaceOnUse" x1="1985.4697" y1="2246.0471" x2="2007.2061" y2="2246.0471">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st171" d="M2007.2,2243.9C2007.2,2243.9,2007.2,2243.9,2007.2,2243.9C2007.2,2243.8,2007.2,2243.8,2007.2,2243.9
		c0-0.1,0-0.1,0-0.1c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.6,2.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0
		l10.6-2.1c0.1,0,0.2-0.1,0.2-0.2L2007.2,2243.9C2007.2,2244,2007.2,2244,2007.2,2243.9C2007.2,2243.9,2007.2,2243.9,2007.2,2243.9z
		 M2003.2,2254.1l-10,2l3.4-9.8l10-2L2003.2,2254.1z"/>
	<linearGradient id="SVGID_137_" gradientUnits="userSpaceOnUse" x1="2587.0776" y1="2213.2915" x2="2607.4556" y2="2213.2915">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st172" d="M2605.1,2206.1C2605.1,2206.1,2605.1,2206.1,2605.1,2206.1C2605.1,2206.1,2605.1,2206.1,2605.1,2206.1
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L2605.1,2206.1z M2589.8,2220.3l-2.2-9.8l9.4,3
		l2.2,9.8L2589.8,2220.3z"/>
	<linearGradient id="SVGID_138_" gradientUnits="userSpaceOnUse" x1="-1123.5303" y1="3693.8013" x2="-1094.0958" y2="3693.8013">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st173" d="M-1094.1,3690.9C-1094.1,3690.9-1094.1,3690.9-1094.1,3690.9c0-0.1,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0,0,0,0,0,0
		l-9.5-11.1c-0.1-0.1-0.2-0.1-0.3-0.1l-14.4,2.9c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0
		c0,0,0,0,0,0l-4.8,14.1c0,0.1,0,0.2,0.1,0.3l9.6,11.1c0,0,0.1,0.1,0.1,0.1c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0l14.4-2.9
		c0.1,0,0.2-0.1,0.3-0.2l4.8-14C-1094.1,3691-1094.1,3691-1094.1,3690.9C-1094.1,3690.9-1094.1,3690.9-1094.1,3690.9z
		 M-1099.5,3704.6l-13.6,2.7l4.6-13.3l13.6-2.7L-1099.5,3704.6z"/>
	<linearGradient id="SVGID_139_" gradientUnits="userSpaceOnUse" x1="-1745.7698" y1="3831.8257" x2="-1712.7301" y2="3831.8257">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st174" d="M-1716.5,3820.2C-1716.5,3820.2-1716.5,3820.1-1716.5,3820.2c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L-1716.5,3820.2z M-1741.3,3843.1l-3.6-15.9l15.3,4.9l3.6,15.9L-1741.3,3843.1z"/>
	<g>
		<path class="st14" d="M1178.2,63.1c-0.3,0-0.6-0.1-0.8-0.3l-6-6c-0.4-0.4-0.4-1.2,0-1.6c0.4-0.4,1.2-0.4,1.6,0l5.2,5.2l5.2-5.2
			c0.4-0.4,1.2-0.4,1.6,0c0.4,0.4,0.4,1.2,0,1.6l-6,6C1178.8,63,1178.5,63.1,1178.2,63.1z"/>
	</g>
	<g>
		<path class="st14" d="M1198,54.9c-0.3,0-0.6,0.1-0.8,0.3l-6,6c-0.4,0.4-0.4,1.2,0,1.6c0.4,0.4,1.2,0.4,1.6,0l5.2-5.2l5.2,5.2
			c0.4,0.4,1.2,0.4,1.6,0c0.4-0.4,0.4-1.2,0-1.6l-6-6C1198.6,55,1198.3,54.9,1198,54.9z"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_140_" gradientUnits="userSpaceOnUse" x1="2471.3171" y1="42.6483" x2="2506.3171" y2="42.6483" gradientTransform="matrix(0.6981 0.716 -0.716 0.6981 780.5552 -1768.416)">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st175" d="M2497,55.7l-21.6-22.2c-0.8-0.8-0.8-2.1,0-2.8v0c0.8-0.8,2.1-0.8,2.8,0l21.6,22.2c0.8,0.8,0.8,2.1,0,2.8v0
			C2499,56.5,2497.7,56.5,2497,55.7z"/>
	</g>
	<g>
		
			<linearGradient id="SVGID_141_" gradientUnits="userSpaceOnUse" x1="2490.2456" y1="66.6084" x2="2525.2456" y2="66.6084" gradientTransform="matrix(0.7239 -0.6899 -0.6899 -0.7239 718.0428 1821.4435)">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st176" d="M2497.4,31.1L2475,52.5c-0.8,0.8-0.8,2-0.1,2.8v0c0.8,0.8,2,0.8,2.8,0.1l22.4-21.4c0.8-0.8,0.8-2,0.1-2.8v0
			C2499.5,30.4,2498.2,30.3,2497.4,31.1z"/>
	</g>
</g>
<g id="Layer_2">
</g>
</svg>
