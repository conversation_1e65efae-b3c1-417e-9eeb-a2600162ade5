{"version": 3, "file": "prefer-includes.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-includes.ts"], "names": [], "mappings": ";;;;;AACA,+CAA8C;AAC9C,qCAA+D;AAC/D,4DAA4B;AAC5B,kCAAwD;AAExD,kBAAe,iBAAU,CAAC;IACxB,IAAI,EAAE,iBAAiB;IACvB,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,cAAc,EAAE,kCAAkC;YAClD,oBAAoB,EAClB,uDAAuD;SAC1D;QACD,MAAM,EAAE,EAAE;KACX;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QACvC,MAAM,QAAQ,GAAG,wBAAiB,CAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAEhD,SAAS,QAAQ,CAAC,IAAmB,EAAE,KAAa;YAClD,MAAM,SAAS,GAAG,6BAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,SAAS,KAAK,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;QACzD,CAAC;QAED,SAAS,eAAe,CAAC,IAA+B;YACtD,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBACrB,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI,CAAC;gBACV,KAAK,GAAG;oBACN,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClC,KAAK,IAAI;oBACP,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjC;oBACE,OAAO,KAAK,CAAC;aAChB;QACH,CAAC;QACD,SAAS,eAAe,CAAC,IAA+B;YACtD,QAAQ,IAAI,CAAC,QAAQ,EAAE;gBACrB,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClC,KAAK,GAAG;oBACN,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjC;oBACE,OAAO,KAAK,CAAC;aAChB;QACH,CAAC;QAED,SAAS,iBAAiB,CACxB,KAAqB,EACrB,KAAqB;YAErB,IAAI,CAAC,oBAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE;gBAC1D,OAAO,KAAK,CAAC;aACd;YAED,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;YACjC,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;YACjC,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE;gBACrC,OAAO,KAAK,CAAC;aACd;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE;gBACvC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE1B,6CAA6C;gBAC7C,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,EAAE;oBACzC,OAAO,KAAK,CAAC;iBACd;aACF;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,SAAS,GAAG,6BAAc,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,YAAY,MAAM,CAAC,EAAE;gBAC7D,OAAO,IAAI,CAAC;aACb;YAED,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,4BAAkB,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/D,IACE,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;gBACjC,KAAK,CAAC,UAAU;gBAChB,KAAK,CAAC,MAAM,EACZ;gBACA,OAAO,IAAI,CAAC;aACb;YAED,6CAA6C;YAC7C,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE;gBAC7C,OAAO,IAAI,CAAC;aACb;YAED,aAAa;YACb,OAAO,MAAM,CAAC,aAAa,CACzB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAyB,CAAC,KAAK,CAAC,CACpD,CAAC;QACJ,CAAC;QAED,OAAO;YACL,2GAA2G,CACzG,IAA+B;gBAE/B,yDAAyD;gBACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAiC,CAAC;gBACxD,MAAM,WAAW,GAAG,QAAQ,CAAC,MAAmC,CAAC;gBACjE,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;gBAC9C,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE;oBAC9C,OAAO;iBACR;gBAED,sCAAsC;gBACtC,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;gBACjE,MAAM,mBAAmB,GAAG,KAAK,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;gBAC9D,IACE,mBAAmB,IAAI,IAAI;oBAC3B,mBAAmB,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC,EAC7C;oBACA,OAAO;iBACR;gBAED,uEAAuE;gBACvE,gDAAgD;gBAChD,KAAK,MAAM,oBAAoB,IAAI,mBAAmB,CAAC,YAAY,EAAE;oBACnE,MAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC;oBAC7C,MAAM,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAC/C,MAAM,oBAAoB,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;oBAC1D,IACE,oBAAoB,IAAI,IAAI;wBAC5B,CAAC,oBAAoB,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAC3D,iBAAiB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAC5D,EACD;wBACA,OAAO;qBACR;iBACF;gBAED,aAAa;gBACb,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,WAAW;oBACjB,SAAS,EAAE,gBAAgB;oBAC3B,CAAC,GAAG,CAAC,KAAK;wBACR,IAAI,QAAQ,EAAE;4BACZ,MAAM,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;yBAC7C;wBACD,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;wBACnD,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrE,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;YAED,kBAAkB;YAClB,gFAAgF,CAC9E,IAA+B;gBAE/B,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAiC,CAAC;gBACxD,MAAM,IAAI,GACR,QAAQ,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;gBACpE,IAAI,IAAI,IAAI,IAAI,EAAE;oBAChB,OAAO;iBACR;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,sBAAsB;oBACjC,CAAC,GAAG,CAAC,KAAK;wBACR,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM,UAAU,GACd,OAAO,CAAC,IAAI,KAAK,SAAS;4BAC1B,OAAO,CAAC,IAAI,KAAK,iBAAiB;4BAClC,OAAO,CAAC,IAAI,KAAK,YAAY;4BAC7B,OAAO,CAAC,IAAI,KAAK,kBAAkB;4BACnC,OAAO,CAAC,IAAI,KAAK,gBAAgB,CAAC;wBAEpC,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,IAAI,UAAU,EAAE;4BACd,MAAM,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;4BAC3C,MAAM,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;yBAC3C;wBACD,MAAM,KAAK,CAAC,eAAe,CACzB,OAAO,EACP,aAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CACpC,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}