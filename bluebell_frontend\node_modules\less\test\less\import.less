/** comment at the top**/
@import url(/absolute/something.css) screen and (color) and (max-width: 600px);

@import (optional) "file-does-not-exist.does-not-exist";

@var: 100px;
@import url("//ha.com/file.css") (min-width:@var);

#import-test {
  .mixin;
  width: 10px;
  height: (@a + 10%);
}
@import "import/import-test-e" screen and (max-width: 600px);

@import url("import/import-test-a.less");

@import (less, multiple) "import/import-test-d.css" screen and (max-width: 601px);

@import (multiple) "import/import-test-e" screen and (max-width: 602px);

@import (less, multiple) url("import/import-test-d.css") screen and (max-width: 603px);

@media print {
  @import (multiple) "import/import-test-e";
}

@charset "UTF-8"; // climb on top #2126

