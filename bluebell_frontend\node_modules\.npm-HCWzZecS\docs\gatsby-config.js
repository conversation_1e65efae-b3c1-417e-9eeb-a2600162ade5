const IS_STATIC = process.env.GATSBY_IS_STATIC

const OPTS = {
  siteMetadata: {
    title: 'npm cli documentation',
    description: 'Documentation for the npm cli.',
    author: '@gatsbyjs'
  },
  plugins: [
    'gatsby-plugin-root-import',
    'gatsby-plugin-react-helmet',
    'gatsby-plugin-catch-links',
    'gatsby-plugin-styled-components',
    {
      resolve: 'gatsby-source-filesystem',
      options: {
        name: 'src',
        path: `${__dirname}/content/`
      }
    },
    {
      resolve: 'gatsby-plugin-no-sourcemaps'
    },
    'gatsby-plugin-sharp',
    {
      resolve: 'gatsby-plugin-manifest',
      options: {
        name: 'gatsby-starter-default',
        short_name: 'starter',
        start_url: '/',
        background_color: '#663399',
        theme_color: '#663399',
        display: 'minimal-ui',
        icon: 'src/images/npm-icon.png' // This path is relative to the root of the site.
      }
    },
    {
      resolve: 'gatsby-plugin-prefetch-google-fonts',
      options: {
        fonts: [
          {
            family: 'Poppins',
            subsets: ['latin'],
            variants: ['300', '400', '500']
          },
          {
            family: 'Inconsolata',
            subsets: ['latin'],
            variants: ['400', '700']
          }
        ]
      }
    },
    {
      resolve: 'gatsby-transformer-remark',
      options: {
        // CommonMark mode (default: true)
        commonmark: true,
        // Footnotes mode (default: true)
        footnotes: true,
        // Pedantic mode (default: true)
        pedantic: true,
        // GitHub Flavored Markdown mode (default: true)
        gfm: true,
        // Plugins configs
        plugins: [{
          resolve: 'gatsby-remark-autolink-headers',
          options: {
            offsetY: '100',
            icon: '<svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg>',
            className: 'header-link-class',
            maintainCase: false,
            removeAccents: true
          }
        },
        {
          resolve: 'gatsby-remark-prismjs',
          options: {
            classPrefix: 'language-',
            inlineCodeMarker: null,
            aliases: {},
            showLineNumbers: false,
            noInlineHighlight: false
          }
        }]
      }
    }
  ]
}

const STATIC_OPTS = Object.assign({}, OPTS, {
  pathPrefix: '__GATSBY_IPFS_PATH_PREFIX__',
  plugins: OPTS.plugins.concat(['gatsby-plugin-ipfs'])
})

module.exports = IS_STATIC ? STATIC_OPTS : OPTS
