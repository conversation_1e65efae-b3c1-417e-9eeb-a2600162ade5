<?xml version="1.0" encoding="utf-8"?>
<!-- Generator: Adobe Illustrator 21.1.0, SVG Export Plug-In . SVG Version: 6.00 Build 0)  -->
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
	 viewBox="0 0 1599 192" style="enable-background:new 0 0 1599 192;" xml:space="preserve">
<style type="text/css">
	.st0{opacity:0.3;fill:url(#SVGID_1_);}
	.st1{opacity:0.3;fill:url(#SVGID_2_);}
	.st2{opacity:0.1;fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st3{opacity:0.3;fill:#E8D9D9;}
	.st4{opacity:0.5;fill:url(#SVGID_3_);}
	.st5{opacity:0.3;fill:url(#SVGID_4_);}
	.st6{opacity:0.3;fill:url(#SVGID_5_);}
	.st7{fill:#F6D2C9;}
	.st8{fill:#FFFFFF;}
	.st9{fill:#FF2EDD;}
	.st10{fill:none;stroke:url(#SVGID_6_);stroke-width:3;stroke-miterlimit:10;}
	.st11{fill:none;stroke:#B3B3B3;stroke-width:0.75;stroke-miterlimit:10;}
	.st12{fill:none;stroke:url(#SVGID_7_);stroke-miterlimit:10;}
	.st13{fill:none;stroke:url(#SVGID_8_);stroke-width:3;stroke-miterlimit:10;}
	.st14{fill:#FB3B49;}
	.st15{fill:url(#SVGID_9_);}
	.st16{opacity:0.7;}
	.st17{fill:url(#SVGID_10_);}
	.st18{fill:#333333;}
	.st19{opacity:0.2;fill:#FB3B49;}
	.st20{opacity:0.3;fill:url(#SVGID_11_);}
	.st21{fill:none;stroke:url(#SVGID_12_);stroke-width:3;stroke-miterlimit:10;}
	.st22{fill:url(#SVGID_13_);}
	.st23{fill:url(#SVGID_14_);}
	.st24{fill:none;stroke:url(#SVGID_15_);stroke-width:10.069;stroke-miterlimit:10;}
	.st25{fill:none;stroke:url(#SVGID_16_);stroke-width:10.069;stroke-miterlimit:10;}
	.st26{fill:none;stroke:url(#SVGID_17_);stroke-width:3;stroke-miterlimit:10;}
	.st27{clip-path:url(#XMLID_6_);}
	.st28{opacity:0.3;fill:url(#SVGID_18_);}
	.st29{fill:none;stroke:url(#SVGID_19_);stroke-width:3;stroke-miterlimit:10;}
	.st30{fill:url(#SVGID_20_);}
	.st31{fill:url(#SVGID_21_);}
	.st32{fill:none;stroke:url(#SVGID_22_);stroke-width:3;stroke-miterlimit:10;}
	.st33{opacity:0.8;}
	.st34{fill-rule:evenodd;clip-rule:evenodd;fill:#FFFFFF;}
	.st35{fill:#7C2EDD;}
	.st36{fill:none;stroke:url(#SVGID_23_);stroke-width:3;stroke-miterlimit:10;}
	.st37{fill:none;stroke:url(#SVGID_24_);stroke-width:3;stroke-miterlimit:10;}
	.st38{fill:none;stroke:#B3B3B3;stroke-miterlimit:10;}
	.st39{fill:none;stroke:#B3B3B3;stroke-width:1.1228;stroke-miterlimit:10;}
	.st40{fill:none;stroke:#B3B3B3;stroke-width:1.2168;stroke-miterlimit:10;}
	.st41{fill:none;stroke:#333333;stroke-miterlimit:10;}
	.st42{fill:url(#SVGID_25_);}
	.st43{fill:url(#SVGID_26_);}
	.st44{fill:url(#SVGID_27_);}
	.st45{fill:url(#SVGID_28_);}
	.st46{fill:#231F20;}
	.st47{fill:none;}
	.st48{opacity:0.6;fill:url(#SVGID_29_);}
	.st49{fill:none;stroke:url(#SVGID_30_);stroke-miterlimit:10;}
	.st50{fill:none;stroke:#B3B3B3;stroke-width:0.7877;stroke-miterlimit:10;}
	.st51{opacity:0.9;}
	.st52{opacity:0.1;}
	.st53{fill:none;stroke:#808080;stroke-miterlimit:10;}
	.st54{opacity:5.000000e-02;}
	.st55{fill:none;stroke:#FF00FF;stroke-miterlimit:10;}
	.st56{fill:url(#SVGID_31_);}
	.st57{fill:url(#SVGID_32_);}
	.st58{opacity:0.19;fill:url(#SVGID_33_);}
	.st59{fill:none;stroke:url(#SVGID_34_);stroke-width:3;stroke-miterlimit:10;}
	.st60{opacity:0.19;fill:url(#SVGID_35_);}
	.st61{opacity:0.5;fill:#FFFFFF;}
	.st62{fill:none;stroke:#333333;stroke-width:2;stroke-miterlimit:10;}
	.st63{opacity:0.19;fill:url(#SVGID_36_);}
	.st64{fill:#333333;stroke:#333333;stroke-miterlimit:10;}
	.st65{opacity:0.19;fill:url(#SVGID_37_);}
	.st66{fill:none;stroke:#333333;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st67{fill:none;stroke:url(#SVGID_38_);stroke-width:3;stroke-miterlimit:10;}
	.st68{opacity:0.6;fill:url(#SVGID_39_);}
	.st69{opacity:0.4;fill:url(#SVGID_40_);}
	.st70{opacity:0.4;fill:url(#SVGID_41_);}
	.st71{opacity:0.4;fill:url(#SVGID_42_);}
	.st72{fill:#F2F2F2;}
	.st73{opacity:0.4;fill:url(#SVGID_43_);}
	.st74{fill:#413844;stroke:#223839;stroke-miterlimit:10;}
	
		.st75{fill:#FFFFFF;fill-opacity:0.5;stroke:#223839;stroke-width:1.802;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st76{fill:url(#SVGID_44_);}
	.st77{fill:url(#SVGID_45_);}
	.st78{fill:url(#SVGID_46_);}
	.st79{fill:url(#SVGID_47_);}
	.st80{fill:url(#SVGID_48_);}
	.st81{fill:none;stroke:#223839;stroke-width:2;stroke-miterlimit:10;}
	.st82{fill:url(#SVGID_49_);}
	.st83{fill:url(#SVGID_50_);}
	.st84{fill:url(#SVGID_51_);}
	.st85{fill:url(#SVGID_52_);}
	.st86{fill:url(#SVGID_53_);}
	.st87{fill:url(#SVGID_54_);}
	.st88{fill:url(#SVGID_55_);}
	.st89{fill:url(#SVGID_56_);}
	.st90{fill:url(#SVGID_57_);}
	.st91{fill:url(#SVGID_58_);}
	.st92{fill:#FF00FF;}
	.st93{fill:#7457D9;}
	.st94{opacity:0.3;fill:url(#SVGID_59_);}
	.st95{fill:none;stroke:url(#SVGID_60_);stroke-width:3;stroke-miterlimit:10;}
	.st96{fill:#333333;stroke:#333333;stroke-width:1.0718;stroke-miterlimit:10;}
	.st97{fill:none;stroke:url(#SVGID_61_);stroke-miterlimit:10;}
	.st98{fill:#413844;}
	.st99{fill:none;stroke:#223839;stroke-miterlimit:10;}
	.st100{opacity:0.6;fill:url(#SVGID_62_);}
	.st101{opacity:0.4;fill:url(#SVGID_63_);}
	.st102{opacity:0.4;fill:url(#SVGID_64_);}
	.st103{opacity:0.4;fill:url(#SVGID_65_);}
	.st104{opacity:0.4;fill:url(#SVGID_66_);}
	.st105{fill:url(#SVGID_67_);}
	.st106{fill:url(#SVGID_68_);}
	.st107{fill:url(#SVGID_69_);}
	.st108{fill:url(#SVGID_70_);}
	.st109{fill:url(#SVGID_71_);}
	.st110{fill:url(#SVGID_72_);}
	.st111{fill:url(#SVGID_73_);}
	.st112{fill:url(#SVGID_74_);}
	.st113{fill:url(#SVGID_75_);}
	.st114{fill:url(#SVGID_76_);}
	.st115{fill:url(#SVGID_77_);}
	.st116{fill:url(#SVGID_78_);}
	.st117{fill:url(#SVGID_79_);}
	.st118{fill:url(#SVGID_80_);}
	.st119{fill:url(#SVGID_81_);}
	.st120{fill:none;stroke:#FF00FF;stroke-miterlimit:10;stroke-dasharray:40,2;}
	.st121{fill:url(#SVGID_82_);stroke:url(#SVGID_83_);stroke-width:0.742;stroke-miterlimit:10;}
	.st122{fill:url(#SVGID_84_);stroke:url(#SVGID_85_);stroke-width:0.742;stroke-miterlimit:10;}
	.st123{fill:url(#SVGID_86_);stroke:url(#SVGID_87_);stroke-width:0.742;stroke-miterlimit:10;}
	.st124{fill:url(#SVGID_88_);}
	.st125{fill:url(#SVGID_89_);}
	.st126{fill:url(#SVGID_90_);}
	.st127{opacity:0.9;fill:url(#SVGID_91_);}
	.st128{fill:none;stroke:url(#SVGID_92_);stroke-width:3;stroke-miterlimit:10;}
	.st129{fill:none;stroke:url(#SVGID_93_);stroke-width:3;stroke-miterlimit:10;}
	.st130{opacity:0.1;fill:none;stroke:#4D4D4D;stroke-miterlimit:10;}
	.st131{fill:#ED1C24;}
	.st132{fill:#666666;}
	.st133{opacity:0.2;fill:#D4BEB8;}
	.st134{fill:none;stroke:#FB3B49;stroke-width:2;stroke-linecap:round;stroke-linejoin:round;stroke-miterlimit:10;}
	.st135{opacity:8.000000e-02;fill:#CC33FF;}
	.st136{fill:#CC33FF;}
	.st137{fill:#AF2AF7;}
	.st138{opacity:0.3;fill:url(#SVGID_94_);}
	.st139{fill:none;stroke:#F2F2F2;stroke-miterlimit:10;}
	.st140{fill:url(#SVGID_95_);stroke:url(#SVGID_96_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st141{fill:url(#SVGID_97_);stroke:url(#SVGID_98_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st142{fill:url(#SVGID_99_);stroke:url(#SVGID_100_);stroke-width:0.4819;stroke-miterlimit:10;}
	.st143{fill:none;stroke:#FB3B49;stroke-miterlimit:10;}
	.st144{fill:url(#SVGID_101_);stroke:url(#SVGID_102_);stroke-width:0.742;stroke-miterlimit:10;}
	.st145{fill:url(#SVGID_103_);}
	.st146{fill:url(#SVGID_104_);}
	.st147{fill:none;stroke:url(#SVGID_105_);stroke-miterlimit:10;}
	.st148{fill:url(#SVGID_106_);stroke:url(#SVGID_107_);stroke-width:0.742;stroke-miterlimit:10;}
	.st149{fill:url(#SVGID_108_);stroke:url(#SVGID_109_);stroke-width:0.742;stroke-miterlimit:10;}
	.st150{fill:url(#SVGID_110_);stroke:url(#SVGID_111_);stroke-width:0.742;stroke-miterlimit:10;}
	.st151{fill:none;stroke:#FF00FF;stroke-width:0.6009;stroke-miterlimit:10;stroke-dasharray:24.0344,1.2017;}
	.st152{fill:none;stroke:#FB3B49;stroke-width:0.6009;stroke-miterlimit:10;}
	.st153{fill:url(#SVGID_112_);stroke:url(#SVGID_113_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st154{fill:url(#SVGID_114_);}
	.st155{fill:url(#SVGID_115_);}
	.st156{fill:url(#SVGID_116_);}
	.st157{fill:url(#SVGID_117_);}
	.st158{opacity:0.9;fill:url(#SVGID_118_);}
	.st159{fill:url(#SVGID_119_);stroke:url(#SVGID_120_);stroke-width:0.742;stroke-miterlimit:10;}
	.st160{fill:url(#SVGID_121_);stroke:url(#SVGID_122_);stroke-width:0.742;stroke-miterlimit:10;}
	.st161{fill:url(#SVGID_123_);stroke:url(#SVGID_124_);stroke-width:0.742;stroke-miterlimit:10;}
	.st162{fill:url(#SVGID_125_);stroke:url(#SVGID_126_);stroke-width:0.742;stroke-miterlimit:10;}
	.st163{fill:url(#SVGID_127_);}
	.st164{fill:url(#SVGID_128_);}
	.st165{opacity:0.9;fill:url(#SVGID_129_);}
	.st166{fill:url(#SVGID_130_);}
	.st167{opacity:0.9;fill:url(#SVGID_131_);}
	.st168{fill:url(#SVGID_132_);stroke:url(#SVGID_133_);stroke-width:0.4458;stroke-miterlimit:10;}
	.st169{fill:url(#SVGID_134_);}
	.st170{fill:url(#SVGID_135_);}
	.st171{opacity:0.9;fill:url(#SVGID_136_);}
	.st172{fill:url(#SVGID_137_);}
	.st173{opacity:0.9;fill:url(#SVGID_138_);}
</style>
<g id="Layer_1">
	<linearGradient id="SVGID_1_" gradientUnits="userSpaceOnUse" x1="1187.985" y1="-614.4257" x2="412.015" y2="310.3394">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.5"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="1" y="-269.5" class="st0" width="1598" height="235"/>
	<linearGradient id="SVGID_2_" gradientUnits="userSpaceOnUse" x1="-1922.99" y1="-2216" x2="-2955.01" y2="-986.0863">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-3238" y="-1978.5" class="st1" width="1598" height="755"/>
	<path class="st2" d="M1289.4-445.5l-790.9,0c-1.4,0-2.6-1.2-2.6-2.6l0-239.8c0-1.4,1.2-2.6,2.6-2.6l790.9,0c1.4,0,2.6,1.2,2.6,2.6
		l0,239.8C1292-446.7,1290.8-445.5,1289.4-445.5z"/>
	<rect x="-4861" y="-1232.5" class="st3" width="1598" height="1797"/>
	<linearGradient id="SVGID_3_" gradientUnits="userSpaceOnUse" x1="-6479" y1="-1610.5431" x2="-4881" y2="-1610.5431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st4" points="-4881,-1234.5 -4881,-1986.5 -6479,-1986.5 -6479,-1236.1 	"/>
	<linearGradient id="SVGID_4_" gradientUnits="userSpaceOnUse" x1="-8099" y1="-1708.6636" x2="-6501" y2="-1708.6636">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<polygon class="st5" points="-6501,-1435.3 -6501,-1987.5 -8099,-1987.5 -8099,-1429.8 	"/>
	<linearGradient id="SVGID_5_" gradientUnits="userSpaceOnUse" x1="-8140.6743" y1="-4028.3975" x2="-6461.3257" y2="-3609.689">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<rect x="-8099" y="-4195.5" class="st6" width="1596" height="753"/>
	<g>
		<g>
			<rect x="-9088.6" y="-1436.2" class="st7" width="318" height="1481"/>
		</g>
		<g>
			<rect x="-9096" y="-1443.5" class="st8" width="318" height="1481"/>
		</g>
	</g>
	<rect x="-8054" y="-4234.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -8048 -4220.3433)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -7088.0918 -4219.2417)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_6_" gradientUnits="userSpaceOnUse" x1="-8100" y1="-4253.543" x2="-6501" y2="-4253.543">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st10" x1="-8100" y1="-4253.5" x2="-6501" y2="-4253.5"/>
	<line class="st11" x1="-6500.5" y1="-4197.5" x2="-8100" y2="-4197.5"/>
	<linearGradient id="SVGID_7_" gradientUnits="userSpaceOnUse" x1="-6104.8535" y1="-1810.7931" x2="-6103.6465" y2="-1810.7931">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st12" x1="-6104" y1="-1810.5" x2="-6104.5" y2="-1811"/>
	<linearGradient id="SVGID_8_" gradientUnits="userSpaceOnUse" x1="-6724" y1="-4212.543" x2="-6689.5" y2="-4212.543">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st13" x1="-6724" y1="-4212.5" x2="-6689.5" y2="-4212.5"/>
	<rect x="-7403" y="-3734.5" class="st14" width="276" height="71"/>
	<g>
		<linearGradient id="SVGID_9_" gradientUnits="userSpaceOnUse" x1="-7526.5444" y1="-4047.1931" x2="-7499.4526" y2="-4047.1931">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st15" points="-7521.4,-4028.5 -7526.5,-4034.6 -7511.8,-4047.2 -7526.5,-4059.8 -7521.3,-4065.9 -7499.5,-4047.1 
					"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_10_" gradientUnits="userSpaceOnUse" x1="-7036.0264" y1="-4019.05" x2="-7005.5" y2="-4019.05">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-7036" y="-4023.1" class="st17" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="1618" height="1337" xlink:href="5FF73A65D6BEC3DE.png"  transform="matrix(1 0 0 1 -8110 -1427.5431)">
		</image>
		<g>
			<rect x="-8101" y="-1413.5" class="st18" width="1600" height="1319"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="5FF73A65D6BEC3E2.png"  transform="matrix(1 0 0 1 -7479 -4092.5432)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-7427.1-4016.2h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-4016.2z"/>
				<path class="st18" d="M-7411.5-4084.6h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-4084.6z M-7399-4073.7v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-7399z"/>
				<path class="st18" d="M-7352.2-4084.6h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-4084.6
					z"/>
				<path class="st18" d="M-7190.2-4065.2c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-7190.2-4065.2z"/>
				<path class="st18" d="M-7120.7-4026.5v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-7120.7z"/>
				<path class="st18" d="M-7100.9-4084.6h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-4084.6z"/>
			</g>
		</g>
	</g>
	<rect x="-7633.6" y="-3930.1" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -7633.584 -3907.9143)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">The</tspan><tspan x="61" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="81.8" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">intelligent</tspan><tspan x="255.3" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="276.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">package</tspan><tspan x="424.1" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="444.9" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">manager</tspan><tspan x="600.4" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="621.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">for</tspan><tspan x="668.2" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:13;"> </tspan><tspan x="689" y="0" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">the </tspan><tspan x="0" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Node</tspan><tspan x="87.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="101.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Javascript</tspan><tspan x="282.2" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="296.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Platform.</tspan><tspan x="452.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="466.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Install</tspan><tspan x="572.3" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="586.6" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">stuff</tspan><tspan x="664.1" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:7;"> </tspan><tspan x="678.5" y="43" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">and </tspan><tspan x="275.1" y="86" class="st18" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">get coding!</tspan></text>
	<rect x="-7396" y="-3727.5" class="st19" width="276" height="71"/>
	
		<text transform="matrix(1 0 0 1 -7353.0112 -3690.1316)" style="font-family:'Poppins-SemiBold'; font-size:30px; letter-spacing:1;">Read Docs</text>
	<path class="st18" d="M-6496-3507c18.3,18.3-25.9-40-51.8-40c-25.9,0-25.9,40-51.8,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40
		s-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40
		c-25.9,0-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40c-25.9,0-25.9-40-51.7-40s-25.9,40-51.7,40s-25.9-40-51.7-40
		s-25.9,40-51.7,40s-25.9-40-51.7-40v1283.5h1603.5C-6496.5-2263.5-6498.4-3509.4-6496-3507z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="5FF73A65D6BEC3E5.png"  transform="matrix(1 0 0 1 -7860 -3397.5432)">
		</image>
		<g>
			<circle class="st8" cx="-7687" cy="-3224.5" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="5FF73A65D6BEC3E6.png"  transform="matrix(1 0 0 1 -7867 -2698.5432)">
		</image>
		<g>
			<circle class="st8" cx="-7694" cy="-2525.5" r="128"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="346" height="346" xlink:href="5FF73A65D6BEC3E4.png"  transform="matrix(1 0 0 1 -7044 -2973.5432)">
		</image>
		<g>
			<circle class="st8" cx="-6871" cy="-2800.5" r="128"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -7278.6538 -1736.4655)"><tspan x="0" y="0" style="font-family:'MyriadPro-Regular'; font-size:30px; letter-spacing:1;">❤</tspan><tspan x="16.8" y="0" style="font-family:'MonotypeSorts'; font-size:30px; letter-spacing:1;">,</tspan></text>
	<linearGradient id="SVGID_11_" gradientUnits="userSpaceOnUse" x1="-6479" y1="-3883.9431" x2="-4883" y2="-3883.9431">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st20" d="M-4883-4199.5v630.9c-21-2.9-22.7-23.8-46.8-23.8c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2
		s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2s-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2s-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		s-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2
		c-25.9,0-25.9-24.2-51.8-24.2c-25.9,0-25.9,24.2-51.8,24.2c-24.5,0-25.8-21.6-47.8-24v-607.2H-4883z"/>
	<g>
		
			<image style="overflow:visible;" width="1608" height="1247" xlink:href="5FF73A65D6BEC3E3.png"  transform="matrix(1 0 0 1 -6487 -3578.5432)">
		</image>
		<g>
			<path class="st18" d="M-4883-3546.7v1211.1h-1596v-1234.8c22,2.4,23.3,24,47.8,24c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2s25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2
				s25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2s25.9,24.2,51.8,24.2
				c25.9,0,25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2s25.9-24.2,51.8-24.2c25.9,0,25.9,24.2,51.8,24.2
				c25.9,0,25.9-24.2,51.8-24.2s25.9,24.2,51.8,24.2c25.9,0,25.9-24.2,51.8-24.2C-4905.7-3570.5-4904-3549.6-4883-3546.7z"/>
		</g>
	</g>
	<rect x="-6434" y="-4232.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -6428 -4218.3433)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -5468.0918 -4223.2417)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-4880.5" y1="-4199.5" x2="-6480" y2="-4199.5"/>
	<linearGradient id="SVGID_12_" gradientUnits="userSpaceOnUse" x1="-5104" y1="-4216.543" x2="-5069.5" y2="-4216.543">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st21" x1="-5104" y1="-4216.5" x2="-5069.5" y2="-4216.5"/>
	<rect x="-5810.8" y="-3811.5" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_13_" gradientUnits="userSpaceOnUse" x1="-5948.5444" y1="-4016.1931" x2="-5921.4526" y2="-4016.1931">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st22" points="-5943.4,-3997.5 -5948.5,-4003.6 -5933.8,-4016.2 -5948.5,-4028.8 -5943.3,-4034.9 -5921.5,-4016.1 
					"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_14_" gradientUnits="userSpaceOnUse" x1="-5458.0264" y1="-3988.05" x2="-5427.5" y2="-3988.05">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-5458" y="-3992.1" class="st23" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="5FF73A65D6BEC3ED.png"  transform="matrix(1 0 0 1 -5901 -4061.5432)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-5849.1-3985.2h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-3985.2z"/>
				<path class="st18" d="M-5833.5-4053.6h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-4053.6z M-5821-4042.7v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-5821z"/>
				<path class="st18" d="M-5774.2-4053.6h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-4053.6
					z"/>
				<path class="st18" d="M-5612.2-4034.2c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-5612.2-4034.2z"/>
				<path class="st18" d="M-5542.7-3995.5v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-5542.7z"/>
				<path class="st18" d="M-5522.9-4053.6h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-4053.6z"/>
			</g>
		</g>
	</g>
	<rect x="-6055.6" y="-3921.1" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -5908.5601 -3906.3142)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-5805" y="-3805.7" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -5754.0112 -3773.1316)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="5FF73A65D6BEC3EE.png"  transform="matrix(1 0 0 1 -6305.6226 -3473.1655)">
		</image>
		<g>
			<ellipse class="st8" cx="-6151.2" cy="-3321.1" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_15_" gradientUnits="userSpaceOnUse" x1="-4883.7075" y1="-3524.4832" x2="-4878" y2="-3524.4832">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st24" d="M-4878-3524.3c-1.8,0-3.5-0.1-5-0.3"/>
	<linearGradient id="SVGID_16_" gradientUnits="userSpaceOnUse" x1="-5673" y1="-989.4235" x2="-5668.4438" y2="-989.4235">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<path class="st25" d="M-5669-989.3c-1.3-0.1-2.6-0.2-4-0.2"/>
	<linearGradient id="SVGID_17_" gradientUnits="userSpaceOnUse" x1="-6479" y1="-4253.043" x2="-4880" y2="-4253.043">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st26" x1="-6479" y1="-4253" x2="-4880" y2="-4253"/>
	
		<text transform="matrix(1 0 0 1 -5958.1777 -3354.8225)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g>
		
			<text transform="matrix(1 0 0 1 -6250.1777 -3012.8225)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -5945.1777 -2607.8225)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.7;" width="309" height="304" xlink:href="5FF73A65D6BEC3EC.png"  transform="matrix(1 0 0 1 -6315.6226 -2745.1655)">
		</image>
		<g>
			<ellipse class="st8" cx="-6161.6" cy="-2593.2" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<text transform="matrix(1 0 0 1 -5959.5654 -3307.093)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -6251.5654 -2965.093)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed accumsan vehicula diam vel auctor. Suspendisse</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;"> id interdum lectus. Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-SemiBold'; font-size:25px;">vestibulum vitae eget lacus. </tspan></text>
	<g>
		<defs>
			<text id="XMLID_1_" transform="matrix(1 0 0 1 -5949.5654 -2557.093)"><tspan x="0" y="0" style="font-family:'Poppins-SemiBold'; font-size:25px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" style="font-family:'Poppins-SemiBold'; font-size:25px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
		</defs>
		<clipPath id="XMLID_6_">
			<use xlink:href="#XMLID_1_"  style="overflow:visible;"/>
		</clipPath>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.4;" width="247" height="242" xlink:href="DEBB70B809924F61.png"  transform="matrix(1 0 0 1 -5526.0918 -2896.5808)">
			</image>
			<g>
				<ellipse class="st8" cx="-5409.5" cy="-2778.2" rx="113.9" ry="111.5"/>
			</g>
		</g>
		<g class="st27">
			
				<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="DEBB70B809924F67.png"  transform="matrix(1 0 0 1 -5417.9448 -3063.2302)">
			</image>
			<g>
				<ellipse class="st8" cx="-5263.5" cy="-2911.2" rx="113.9" ry="111.5"/>
			</g>
		</g>
	</g>
	<linearGradient id="SVGID_18_" gradientUnits="userSpaceOnUse" x1="-5362" y1="-2955.0432" x2="-5076" y2="-2955.0432">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st28" cx="-5219" cy="-2955" r="143"/>
	<circle class="st8" cx="-5219" cy="-2955" r="134"/>
	<rect x="-8054" y="-2020.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -8048 -2006.3434)" class="st9" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -7088.0918 -2011.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<line class="st11" x1="-6500.5" y1="-1987.5" x2="-8100" y2="-1987.5"/>
	<linearGradient id="SVGID_19_" gradientUnits="userSpaceOnUse" x1="-6724" y1="-2004.5431" x2="-6689.5" y2="-2004.5431">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st29" x1="-6724" y1="-2004.5" x2="-6689.5" y2="-2004.5"/>
	<rect x="-7430.8" y="-1599.5" class="st14" width="230" height="59.2"/>
	<g>
		<linearGradient id="SVGID_20_" gradientUnits="userSpaceOnUse" x1="-7568.5444" y1="-1804.193" x2="-7541.4526" y2="-1804.193">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<polygon class="st30" points="-7563.4,-1785.5 -7568.5,-1791.6 -7553.8,-1804.2 -7568.5,-1816.8 -7563.3,-1822.9 -7541.5,-1804.1 
					"/>
	</g>
	<g class="st16">
		<linearGradient id="SVGID_21_" gradientUnits="userSpaceOnUse" x1="-7078.0264" y1="-1776.0499" x2="-7047.5" y2="-1776.0499">
			<stop  offset="0" style="stop-color:#F15A24"/>
			<stop  offset="1" style="stop-color:#FF00FF"/>
		</linearGradient>
		<rect x="-7078" y="-1780" class="st31" width="30.5" height="8"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="431" height="91" xlink:href="DEBB70B809924F60.png"  transform="matrix(1 0 0 1 -7521 -1849.5431)">
		</image>
		<g>
			<g>
				<path class="st18" d="M-7469.1-1773.2h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
					c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-1773.2z"/>
				<path class="st18" d="M-7453.5-1841.6h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
					c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
					h-11.2v27.7h-12.5V-1841.6z M-7441-1830.7v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
					c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-7441z"/>
				<path class="st18" d="M-7394.2-1841.6h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-1841.6
					z"/>
				<path class="st18" d="M-7232.2-1822.2c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
					c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
					c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
					c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
					c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
					c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
					c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-7232.2-1822.2z"/>
				<path class="st18" d="M-7162.7-1783.5v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
					c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-7162.7z"/>
				<path class="st18" d="M-7142.9-1841.6h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-1841.6z"/>
			</g>
		</g>
	</g>
	<rect x="-7675.6" y="-1709.1" class="st47" width="742.6" height="304.6"/>
	<text transform="matrix(1 0 0 1 -7528.5601 -1694.3141)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">The intelligent package manager for the </tspan><tspan x="-75.6" y="31" class="st18" style="font-family:'Poppins-Regular'; font-size:20px; letter-spacing:1;">Node Javascript Platform. Install stuff and get coding!</tspan></text>
	<rect x="-7425" y="-1593.7" class="st19" width="230" height="59.2"/>
	
		<text transform="matrix(1 0 0 1 -7374.0112 -1561.1315)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="DEBB70B809924F63.png"  transform="matrix(1 0 0 1 -7914.6226 -1250.1655)">
		</image>
		<g>
			<ellipse class="st8" cx="-7760.2" cy="-1098.1" rx="113.9" ry="111.5"/>
		</g>
	</g>
	<linearGradient id="SVGID_22_" gradientUnits="userSpaceOnUse" x1="-8099" y1="-2041.0431" x2="-6500" y2="-2041.0431">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st32" x1="-8099" y1="-2041" x2="-6500" y2="-2041"/>
	
		<text transform="matrix(1 0 0 1 -7567.1777 -1144.8224)" style="opacity:0.8;fill:#FFFFFF; font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Super Cool</text>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -7859.1777 -789.8224)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<text transform="matrix(1 0 0 1 -7554.1777 -384.8224)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Ultra Fast</text>
	</g>
	<text transform="matrix(1 0 0 1 -7568.5654 -1097.0929)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Nunc malesuada suscipit enim at feugiat. Duis id mauris</tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">lectus. Donec a sagittis lectus.</tspan></text>
	<text transform="matrix(1 0 0 1 -7860.5654 -742.093)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">interdum lectus.  Phasellus sed tortor sed dui rutrum vestibulum vitae </tspan><tspan x="0" y="72" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">eget lacus. </tspan></text>
	<text id="XMLID_2_" transform="matrix(1 0 0 1 -7558.5654 -334.0929)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="36" class="st8" style="font-family:'Poppins-Regular'; font-size:20px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	<circle class="st18" cx="-6145" cy="-976.5" r="143"/>
	<g class="st33">
		<path class="st8" d="M-6768.8-677.5H-6890V-790h121.2V-677.5z M-6777.5-755.4h-103.8v69.2h103.8V-755.4z M-6777.5-764.1v-17.3
			h-103.8v17.3H-6777.5z"/>
		<circle class="st34" cx="-6873.4" cy="-772.7" r="3.6"/>
		<circle class="st34" cx="-6861.9" cy="-772.7" r="3.6"/>
		<circle class="st34" cx="-6850.3" cy="-772.7" r="3.6"/>
		<path class="st8" d="M-6834.3-721l-20.8,21.9l-6.3-6l15.2-16l-15.2-16.3l6.3-5.9C-6855.1-743.2-6834.3-721-6834.3-721z"/>
		<path class="st8" d="M-6829.4-707.8h30.3v8.7h-30.3V-707.8z"/>
	</g>
	
		<text transform="matrix(1 0 0 1 -7556.1777 -1302.8224)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use NPM CLI?</text>
	<rect x="-6432" y="-2021.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -6426 -2007.3434)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -5466.0918 -2008.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">npm Enterprise    Products    Solutions     Resources</tspan><tspan x="351.5" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:4;">	</tspan><tspan x="360" y="0" style="font-family:'Poppins-Regular'; font-size:14px;"> Docs    Support</tspan></text>
	<linearGradient id="SVGID_23_" gradientUnits="userSpaceOnUse" x1="-6480" y1="-2041.0431" x2="-4880" y2="-2041.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st36" x1="-6480" y1="-2041" x2="-4880" y2="-2041"/>
	<linearGradient id="SVGID_24_" gradientUnits="userSpaceOnUse" x1="-5102" y1="-2001.5431" x2="-5067.5" y2="-2001.5431">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st37" x1="-5102" y1="-2001.5" x2="-5067.5" y2="-2001.5"/>
	<line class="st11" x1="-4878.5" y1="-1985.5" x2="-6478" y2="-1985.5"/>
	<circle class="st8" cx="-6145" cy="-976.5" r="125"/>
	<g class="st16">
		
			<image style="overflow:visible;opacity:0.5;" width="309" height="304" xlink:href="DEBB70B809924F7F.png"  transform="matrix(1 0 0 1 -6991.8604 -885.184)">
		</image>
		<g>
			<ellipse class="st8" cx="-6837.4" cy="-733.2" rx="113.9" ry="111.5"/>
		</g>
	</g>
	
		<text transform="matrix(1 0 0 1 -5833.1777 -1053.8224)" class="st18" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Why use this?</text>
	<line class="st38" x1="-5573.5" y1="-922" x2="-5575.6" y2="-922"/>
	<line class="st38" x1="-6168.3" y1="-922" x2="-6170.5" y2="-922"/>
	<line class="st39" x1="-5146" y1="-641.6" x2="-5148.4" y2="-641.6"/>
	<line class="st40" x1="-6111.6" y1="-1897.9" x2="-6114.4" y2="-1897.9"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="DEBB70B809924F62.png"  transform="matrix(1 0 0 1 -6164.3643 -1955.9076)">
		</image>
		<g>
			<path class="st18" d="M-5271.8-1915.2v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5273.1-1918.2-5271.8-1916.8-5271.8-1915.2z"/>
			<path class="st41" d="M-5271.8-1915.2v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5273.1-1918.2-5271.8-1916.8-5271.8-1915.2z"/>
		</g>
	</g>
	<line class="st40" x1="-5262" y1="-1901.8" x2="-5264.5" y2="-1901.8"/>
	<line class="st40" x1="-6058.1" y1="-1822.7" x2="-6060.8" y2="-1822.7"/>
	<line class="st40" x1="-6089.8" y1="-1850.5" x2="-6092.5" y2="-1850.5"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="DEBB70B809924F65.png"  transform="matrix(1 0 0 1 -6132.3643 -1900.9076)">
		</image>
		<g>
			<path class="st18" d="M-5239.1-1860.9v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5240.4-1863.9-5239.1-1862.5-5239.1-1860.9z"/>
			<path class="st41" d="M-5239.1-1860.9v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5240.4-1863.9-5239.1-1862.5-5239.1-1860.9z"/>
		</g>
	</g>
	<g>
		<path class="st8" d="M-5239.1-1860.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5240.4-1863.6-5239.1-1862.2-5239.1-1860.6z"/>
		<path class="st41" d="M-5239.1-1860.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5240.4-1863.6-5239.1-1862.2-5239.1-1860.6z"/>
	</g>
	<line class="st40" x1="-5229.1" y1="-1843.4" x2="-5231.7" y2="-1843.4"/>
	<line class="st40" x1="-5893.2" y1="-1764.3" x2="-5895.9" y2="-1764.3"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="DEBB70B809924F66.png"  transform="matrix(1 0 0 1 -6098.3643 -1841.9076)">
		</image>
		<g>
			<path class="st18" d="M-5205.4-1801.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5206.8-1804.7-5205.4-1803.4-5205.4-1801.7z"/>
			<path class="st41" d="M-5205.4-1801.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-5206.8-1804.7-5205.4-1803.4-5205.4-1801.7z"/>
		</g>
	</g>
	<line class="st40" x1="-5185.3" y1="-1786.2" x2="-5187.9" y2="-1786.2"/>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_25_" gradientUnits="userSpaceOnUse" x1="-5415.0264" y1="-1650.0499" x2="-5384.5" y2="-1650.0499">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-5415" y="-1654" class="st42" width="30.5" height="8"/>
		</g>
		<g>
			<linearGradient id="SVGID_26_" gradientUnits="userSpaceOnUse" x1="-5905.5444" y1="-1678.193" x2="-5878.4526" y2="-1678.193">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st43" points="-5900.4,-1659.5 -5905.5,-1665.6 -5890.8,-1678.2 -5905.5,-1690.8 -5900.3,-1696.9 
				-5878.5,-1678.1 			"/>
		</g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="DEBB70B809924F64.png"  transform="matrix(1 0 0 1 -5855 -1720.5431)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-5806.1-1647.2h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-1647.2z"/>
					<path class="st8" d="M-5790.5-1715.6h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						h-11.2v27.7h-12.5V-1715.6z M-5778-1704.7v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-5778z"/>
					<path class="st8" d="M-5731.2-1715.6h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-1715.6
						z"/>
					<path class="st8" d="M-5569.2-1696.2c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-5569.2-1696.2z"/>
					<path class="st8" d="M-5499.7-1657.5v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-5499.7z"/>
					<path class="st8" d="M-5479.9-1715.6h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-1715.6z"/>
				</g>
			</g>
		</g>
		<rect x="-5883.7" y="-1581.1" class="st47" width="489.6" height="304.6"/>
		<text transform="matrix(1 0 0 1 -5883.7363 -1566.3141)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="54" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="167.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="181.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="279.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="293.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="396.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="409.9" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="440.6" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:8;"> </tspan><tspan x="454.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the </tspan><tspan x="0" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node</tspan><tspan x="57.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="67.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript</tspan><tspan x="186.4" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="196.3" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Platform.</tspan><tspan x="298" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="307.9" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Install</tspan><tspan x="376.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="386.8" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">stuff</tspan><tspan x="437.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:4;"> </tspan><tspan x="447.1" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">and </tspan><tspan x="181.2" y="62" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">get coding!</tspan></text>
		<g>
			<rect x="-5752.7" y="-1448.5" class="st14" width="230" height="59.2"/>
			<rect x="-5746.8" y="-1442.7" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -5695.8589 -1410.1315)" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g>
		<path class="st8" d="M-5272.1-1915.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5273.4-1918.6-5272.1-1917.2-5272.1-1915.6z"/>
		<path class="st41" d="M-5272.1-1915.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5273.4-1918.6-5272.1-1917.2-5272.1-1915.6z"/>
	</g>
	<g>
		<path class="st8" d="M-5205.1-1801.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5206.4-1804.6-5205.1-1803.2-5205.1-1801.6z"/>
		<path class="st41" d="M-5205.1-1801.6v21.7c0,1.7-1.4,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-5206.4-1804.6-5205.1-1803.2-5205.1-1801.6z"/>
	</g>
	<linearGradient id="SVGID_27_" gradientUnits="userSpaceOnUse" x1="-5431" y1="-741.5432" x2="-5145" y2="-741.5432">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st44" cx="-5288" cy="-741.5" r="143"/>
	<circle class="st8" cx="-5288" cy="-741.5" r="125"/>
	<linearGradient id="SVGID_28_" gradientUnits="userSpaceOnUse" x1="-6146" y1="-404.5431" x2="-5860" y2="-404.5431">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<circle class="st45" cx="-6003" cy="-404.5" r="143"/>
	<circle class="st8" cx="-6003" cy="-404.5" r="125"/>
	<g>
		<g>
			<path class="st46" d="M-6396.9-2007.5h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-2007.5z M-6366.2-2020.5v13h6.4v-9.7h3.2v9.7h3.2v-9.7
				h3.2v9.7h3.2v-13H-6366.2L-6366.2-2020.5z M-6375.2-2017.2h3.2v6.5h-3.2V-2017.2z M-6381.6-2004.3h6.4v-3.2h6.4v-13h-12.8
				V-2004.3z"/>
			<rect x="-6396.9" y="-2020.5" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-6331.9,-2017.3 -6331.9,-2011 -6325.8,-2011 -6325.8,-2007.9 -6332,-2007.9 -6338.3,-2007.9 
			-6338.2,-2020.5 -6325.8,-2020.5 -6325.8,-2017.4 		"/>
		<rect x="-6323.2" y="-2020.5" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-6317.3" y="-2013.9" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -8324.8926 4306.6318)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-6296.1" y="-2012.5" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -8303.5234 4286.77)" class="st46" width="2" height="8.3"/>
		<rect x="-6309.1" y="-2020.5" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_29_" gradientUnits="userSpaceOnUse" x1="-4859" y1="-1607.0431" x2="-3261" y2="-1607.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st48" points="-3261,-1227.5 -3261,-1986.5 -4859,-1986.5 -4859,-1229.1 	"/>
	<linearGradient id="SVGID_30_" gradientUnits="userSpaceOnUse" x1="-4462.8535" y1="-1804.7931" x2="-4461.6465" y2="-1804.7931">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st49" x1="-4462" y1="-1804.5" x2="-4462.5" y2="-1805"/>
	<rect x="-4812" y="-2022.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -4806 -2008.3434)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -3653.0918 -2007.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<line class="st11" x1="-3258.5" y1="-1986.5" x2="-4858" y2="-1986.5"/>
	<line class="st50" x1="-4132.2" y1="-1005.2" x2="-4133.9" y2="-1005.2"/>
	<line class="st38" x1="-4548.3" y1="-982" x2="-4550.5" y2="-982"/>
	<line class="st39" x1="-3526" y1="-701.6" x2="-3528.4" y2="-701.6"/>
	<line class="st40" x1="-4469.6" y1="-1891.9" x2="-4472.4" y2="-1891.9"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="930" height="551" xlink:href="DEBB70B809924F6C.png"  transform="matrix(1 0 0 1 -4610.3643 -1969.9076)">
		</image>
		<g>
			<path class="st18" d="M-3717.8-1929.2v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3719.1-1932.2-3717.8-1930.8-3717.8-1929.2z"/>
			<path class="st41" d="M-3717.8-1929.2v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3719.1-1932.2-3717.8-1930.8-3717.8-1929.2z"/>
		</g>
	</g>
	<line class="st40" x1="-3620" y1="-1895.8" x2="-3622.5" y2="-1895.8"/>
	<line class="st40" x1="-4416.1" y1="-1816.7" x2="-4418.8" y2="-1816.7"/>
	<line class="st40" x1="-4447.8" y1="-1844.5" x2="-4450.5" y2="-1844.5"/>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="DEBB70B809924F84.png"  transform="matrix(1 0 0 1 -4556.3643 -1894.9076)">
		</image>
		<g>
			<path class="st18" d="M-3663.1-1854.9v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3664.4-1857.9-3663.1-1856.5-3663.1-1854.9z"/>
			<path class="st41" d="M-3663.1-1854.9v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3664.4-1857.9-3663.1-1856.5-3663.1-1854.9z"/>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-3663.1-1854.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3664.4-1857.6-3663.1-1856.2-3663.1-1854.6z"/>
		<path class="st41" d="M-3663.1-1854.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3664.4-1857.6-3663.1-1856.2-3663.1-1854.6z"/>
	</g>
	<line class="st40" x1="-3587.1" y1="-1837.4" x2="-3589.7" y2="-1837.4"/>
	<line class="st40" x1="-4251.2" y1="-1758.3" x2="-4253.9" y2="-1758.3"/>
	<g class="st52">
		<line class="st53" x1="-4859.5" y1="-1986.2" x2="-3258.5" y2="-1986.2"/>
		<line class="st53" x1="-4859.4" y1="-1796.2" x2="-3258.4" y2="-1796.2"/>
		<line class="st53" x1="-4859.2" y1="-1606.2" x2="-3258.2" y2="-1606.2"/>
		<line class="st53" x1="-4859.1" y1="-1416.1" x2="-3258.1" y2="-1416.1"/>
		<line class="st53" x1="-4859" y1="-1226.1" x2="-3258" y2="-1226.1"/>
		<line class="st53" x1="-4858.8" y1="-1036" x2="-3257.8" y2="-1036"/>
		<line class="st53" x1="-4858.7" y1="-846" x2="-3257.7" y2="-846"/>
		<line class="st53" x1="-4858.5" y1="-655.9" x2="-3257.5" y2="-655.9"/>
		<line class="st53" x1="-4858.4" y1="-465.9" x2="-3257.4" y2="-465.9"/>
		<line class="st53" x1="-4858.3" y1="-275.8" x2="-3257.3" y2="-275.8"/>
		<line class="st53" x1="-4858.1" y1="-85.8" x2="-3257.1" y2="-85.8"/>
		<line class="st53" x1="-4858" y1="104.3" x2="-3257" y2="104.3"/>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.5;" width="931" height="551" xlink:href="DEBB70B809924F87.png"  transform="matrix(1 0 0 1 -4487.3643 -1802.9076)">
		</image>
		<g>
			<path class="st18" d="M-3594.4-1762.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3595.8-1765.7-3594.4-1764.4-3594.4-1762.7z"/>
			<path class="st41" d="M-3594.4-1762.7v468.9c0,2.2-1.8,4-4,4h-847.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h849.6
				C-3595.8-1765.7-3594.4-1764.4-3594.4-1762.7z"/>
		</g>
	</g>
	<line class="st40" x1="-3543.3" y1="-1780.2" x2="-3545.9" y2="-1780.2"/>
	<g class="st54">
		<line class="st55" x1="-4858" y1="-1980.5" x2="-4858" y2="154.5"/>
		<line class="st55" x1="-4658.4" y1="-1981" x2="-4658.4" y2="154"/>
		<line class="st55" x1="-4458.9" y1="-1981.5" x2="-4458.9" y2="153.5"/>
		<line class="st55" x1="-4259.3" y1="-1982" x2="-4259.3" y2="153"/>
		<line class="st55" x1="-4059.8" y1="-1982.5" x2="-4059.8" y2="152.5"/>
		<line class="st55" x1="-3860.2" y1="-1983" x2="-3860.2" y2="152"/>
		<line class="st55" x1="-3660.6" y1="-1983.5" x2="-3660.6" y2="151.5"/>
		<line class="st55" x1="-3461.1" y1="-1984" x2="-3461.1" y2="151"/>
		<line class="st55" x1="-3261.5" y1="-1984.5" x2="-3261.5" y2="150.5"/>
	</g>
	<g>
		<g class="st16">
			<linearGradient id="SVGID_31_" gradientUnits="userSpaceOnUse" x1="-3836.0264" y1="-1600.0499" x2="-3805.5" y2="-1600.0499">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<rect x="-3836" y="-1604" class="st56" width="30.5" height="8"/>
		</g>
		<g>
			<linearGradient id="SVGID_32_" gradientUnits="userSpaceOnUse" x1="-4326.5444" y1="-1628.193" x2="-4299.4526" y2="-1628.193">
				<stop  offset="0" style="stop-color:#F15A24"/>
				<stop  offset="1" style="stop-color:#FF00FF"/>
			</linearGradient>
			<polygon class="st57" points="-4321.4,-1609.5 -4326.5,-1615.6 -4311.8,-1628.2 -4326.5,-1640.8 -4321.3,-1646.9 
				-4299.5,-1628.1 			"/>
		</g>
		<g class="st51">
			
				<image style="overflow:visible;opacity:0.2;" width="425" height="85" xlink:href="DEBB70B809924F85.png"  transform="matrix(1 0 0 1 -4276 -1670.5431)">
			</image>
			<g>
				<g>
					<path class="st8" d="M-4227.1-1597.2h-13.6l-23.2-51.7v51.7h-11.1v-68.4h15l21.7,49v-49h11.9c0.1,0.1,0.2,0.3,0.2,0.5
						c0,0.3-0.1,0.6-0.4,1c-0.3,0.4-0.5,1.2-0.6,2.3V-1597.2z"/>
					<path class="st8" d="M-4211.5-1665.6h23.2c4.1,0,7.6,0.5,10.5,1.6c2.9,1.1,5.3,2.6,7.1,4.4c1.9,1.9,3.2,4,4.1,6.5
						c0.9,2.5,1.3,5.1,1.3,7.9c0,2.8-0.4,5.4-1.3,7.9c-0.8,2.5-2.2,4.6-4,6.4c-1.8,1.8-4.2,3.3-7,4.3c-2.9,1.1-6.3,1.6-10.2,1.6
						h-11.2v27.7h-12.5V-1665.6z M-4199-1654.7v19.4h10.3c1.9,0,3.5-0.2,4.9-0.7c1.4-0.5,2.5-1.1,3.3-2c0.9-0.8,1.5-1.8,1.9-3
						c0.4-1.2,0.6-2.4,0.6-3.7c0-1.5-0.2-2.8-0.7-4c-0.4-1.2-1.1-2.3-2-3.1s-2-1.6-3.3-2.1c-1.3-0.5-2.9-0.8-4.7-0.8H-4199z"/>
					<path class="st8" d="M-4152.2-1665.6h13l11.5,32.2l11.4-32.3h13.1v68.5h-11.1v-49.6l-9.6,25.8h-8.1l-9.2-25.8v49.6h-11V-1665.6
						z"/>
					<path class="st8" d="M-3990.2-1646.2c-0.3-0.1-0.5-0.2-0.6-0.4c-0.1-0.2-0.2-0.5-0.2-0.8c0-0.3-0.1-0.6-0.1-1
						c0-0.4-0.1-0.8-0.3-1.3c-1-2.3-2.4-4.2-4.2-5.5c-1.8-1.4-4-2-6.7-2c-2.2,0-4.2,0.6-5.9,1.9c-1.8,1.2-3.3,3-4.5,5.2
						c-1.2,2.2-2.2,4.9-2.9,8.1c-0.7,3.2-1,6.7-1,10.6c0,3.7,0.4,7.2,1.1,10.4c0.7,3.2,1.8,5.9,3.1,8.2c1.3,2.3,2.9,4.1,4.8,5.4
						c1.9,1.3,3.9,2,6.1,2c2.6,0,5-0.8,7-2.5c2-1.6,3.8-3.9,5.4-6.6l9.3,6c-2.6,4.5-5.7,7.8-9.4,10s-7.7,3.3-12,3.3
						c-4,0-7.8-0.7-11.2-2.1c-3.4-1.4-6.4-3.6-8.9-6.6c-2.5-3-4.5-6.7-5.9-11.2c-1.4-4.5-2.1-9.7-2.1-15.8c0-4.5,0.4-8.5,1.2-12
						c0.8-3.5,1.8-6.7,3.1-9.4c1.3-2.7,2.9-5,4.7-6.9c1.8-1.9,3.7-3.4,5.7-4.6c2-1.2,4.1-2.1,6.3-2.6c2.2-0.5,4.3-0.8,6.3-0.8
						c2.6,0,5,0.4,7.4,1.1c2.3,0.7,4.5,1.8,6.5,3.2c2,1.4,3.8,3,5.3,4.9c1.5,1.9,2.8,4,3.8,6.4L-3990.2-1646.2z"/>
					<path class="st8" d="M-3920.7-1607.5v10.3h-43.7v-68.4h13.4c0.1,0.1,0.2,0.3,0.2,0.5c0,0.3-0.1,0.6-0.4,1
						c-0.3,0.4-0.5,1.2-0.6,2.3v54.2H-3920.7z"/>
					<path class="st8" d="M-3900.9-1665.6h38.2v10.2h-13.3v48.1h13.8v10.1h-39.4v-10.2h13.3v-48h-12.6V-1665.6z"/>
				</g>
			</g>
		</g>
		<rect x="-4327.7" y="-1531.1" class="st47" width="551.7" height="304.6"/>
		<text transform="matrix(1 0 0 1 -4327.7363 -1516.3141)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
		<g>
			<rect x="-4325.7" y="-1431.5" class="st14" width="230" height="59.2"/>
			<rect x="-4319.8" y="-1425.7" class="st19" width="230" height="59.2"/>
			
				<text transform="matrix(1 0 0 1 -4268.8589 -1393.1315)" class="st8" style="font-family:'Poppins-SemiBold'; font-size:20px; letter-spacing:1;">Read Docs</text>
		</g>
	</g>
	<g class="st51">
		<path class="st8" d="M-3718.1-1929.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3719.4-1932.6-3718.1-1931.2-3718.1-1929.6z"/>
		<path class="st41" d="M-3718.1-1929.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3719.4-1932.6-3718.1-1931.2-3718.1-1929.6z"/>
	</g>
	<g class="st51">
		<path class="st8" d="M-3594.1-1762.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3595.4-1765.6-3594.1-1764.2-3594.1-1762.6z"/>
		<path class="st41" d="M-3594.1-1762.6v21.7c0,1.7-1.3,3-3,3h-849.6c-1.6,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h849.6
			C-3595.4-1765.6-3594.1-1764.2-3594.1-1762.6z"/>
	</g>
	<g>
		<g>
			<path class="st46" d="M-4776.9-2008.5h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-2008.5z M-4746.2-2021.5v13h6.4v-9.7h3.2v9.7h3.2v-9.7
				h3.2v9.7h3.2v-13H-4746.2L-4746.2-2021.5z M-4755.2-2018.2h3.2v6.5h-3.2V-2018.2z M-4761.6-2005.3h6.4v-3.2h6.4v-13h-12.8
				V-2005.3z"/>
			<rect x="-4776.9" y="-2021.5" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-4711.9,-2018.3 -4711.9,-2012 -4705.8,-2012 -4705.8,-2008.9 -4712,-2008.9 -4718.3,-2008.9 
			-4718.2,-2021.5 -4705.8,-2021.5 -4705.8,-2018.4 		"/>
		<rect x="-4703.2" y="-2021.5" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-4697.3" y="-2014.9" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -6705.8926 2685.6321)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-4676.1" y="-2013.5" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -6684.5234 2665.77)" class="st46" width="2" height="8.3"/>
		<rect x="-4689.1" y="-2021.5" class="st46" width="6.4" height="12.9"/>
	</g>
	
		<linearGradient id="SVGID_33_" gradientUnits="userSpaceOnUse" x1="-4638.583" y1="-630.0982" x2="-4258.5825" y2="-630.0982" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -2848.011 -4203.3799)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st58" points="-3685.6,5.9 -3712.9,384.9 -3906.4,371 -3879.1,-8 	"/>
	<linearGradient id="SVGID_34_" gradientUnits="userSpaceOnUse" x1="-4860" y1="-2041.0431" x2="-3260" y2="-2041.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st59" x1="-4860" y1="-2041" x2="-3260" y2="-2041"/>
	
		<linearGradient id="SVGID_35_" gradientUnits="userSpaceOnUse" x1="-3735.3538" y1="-583.6933" x2="-3531.3535" y2="-583.6933" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 46.8033 -571.8436)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st60" points="-3489.5,-338.1 -3691.2,-307.5 -3776.5,-869 -3574.8,-899.6 	"/>
	<line class="st50" x1="-3402.2" y1="-941.2" x2="-3403.9" y2="-941.2"/>
	<g>
		
			<image style="overflow:visible;" width="827" height="400" xlink:href="DEBB70B809924F8B.png"  transform="matrix(1 0 0 1 -4477 -224.5431)">
		</image>
		<g>
			<path class="st61" d="M-3659.5,169.5l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6l0-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6v362.8C-3656.9,168.3-3658.1,169.5-3659.5,169.5z"/>
			<path class="st62" d="M-3659.5,169.5l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6l0-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0
				c1.4,0,2.6,1.2,2.6,2.6v362.8C-3656.9,168.3-3658.1,169.5-3659.5,169.5z"/>
		</g>
	</g>
	<rect x="-4420" y="239.5" class="st8" width="21" height="38"/>
	
		<linearGradient id="SVGID_36_" gradientUnits="userSpaceOnUse" x1="-4623.8662" y1="-990.3799" x2="-4243.8662" y2="-990.3799" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -2945.3704 -5199.7769)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st63" points="-4321.8,-1087.1 -4365.5,-709.6 -4558.2,-732 -4514.5,-1109.5 	"/>
	<g>
		
			<image style="overflow:visible;" width="828" height="375" xlink:href="DEBB70B809924F8C.png"  transform="matrix(1 0 0 1 -4477 -1171.5431)">
		</image>
		<g>
			<path class="st61" d="M-3657.6-803l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-1143c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				v337.3C-3655-804.2-3656.2-803-3657.6-803z"/>
			<path class="st62" d="M-3657.6-803l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-1143c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				v337.3C-3655-804.2-3656.2-803-3657.6-803z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -4174.0342 -956.9582)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g id="XMLID_3_">
		<text transform="matrix(0.9755 0 0 1 -4250.5439 21.9071)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetur adipiscing elit. </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed tempus sapien nibh, et vehicula ipsum cursus non. </tspan></text>
	</g>
	<g class="st33">
		
			<text transform="matrix(1 0 0 1 -7869.1777 -790.8224)" class="st8" style="font-family:'Poppins-BoldItalic'; font-size:50px; letter-spacing:3;">Easy to Use</text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="DEBB70B809924F8F.png"  transform="matrix(1 0 0 1 -4175 -1039.5431)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -4171.9512 -1007.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="DEBB70B809924F8D.png"  transform="matrix(1 0 0 1 -4244 -60.5431)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -4240.5254 -28.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;" width="827" height="401" xlink:href="DEBB70B809924F8A.png"  transform="matrix(1 0 0 1 -4477 -691.5432)">
		</image>
		<g>
			<path class="st61" d="M-3659.1-297l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0c1.4,0,2.6,1.2,2.6,2.6
				l0,362.8C-3656.6-298.2-3657.7-297-3659.1-297z"/>
			<path class="st62" d="M-3659.1-297l-788.4,0c-1.4,0-2.6-1.2-2.6-2.6v-362.8c0-1.4,1.2-2.6,2.6-2.6l788.4,0c1.4,0,2.6,1.2,2.6,2.6
				l0,362.8C-3656.6-298.2-3657.7-297-3659.1-297z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -4264.5449 -469.0929)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Sed accumsan vehicula diam vel auctor. Suspendisse id </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">interdum lectus.  Phasellus sed tortor sed dui rutrum </tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">vestibulum vitae eget lacus. </tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="237" height="59" xlink:href="DEBB70B809924F86.png"  transform="matrix(1 0 0 1 -4262 -553.5432)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -4258.5332 -521.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	<polygon class="st64" points="-3256.4,442.8 -4863.1,392.6 -4863.1,835 -3259.5,835 	"/>
	
		<linearGradient id="SVGID_37_" gradientUnits="userSpaceOnUse" x1="-5595.8354" y1="-871.4171" x2="-5215.835" y2="-871.4171" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 912.5291 798.3806)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st65" points="-4333.7,267.5 -4713.3,285.2 -4722.3,91.4 -4342.7,73.7 	"/>
	<line class="st41" x1="-3682.4" y1="-1848.7" x2="-3674" y2="-1839.8"/>
	<line class="st41" x1="-3682.7" y1="-1840.3" x2="-3673.7" y2="-1848.7"/>
	<line class="st41" x1="-3737.4" y1="-1924.7" x2="-3729" y2="-1915.8"/>
	<line class="st41" x1="-3737.7" y1="-1916.3" x2="-3728.7" y2="-1924.7"/>
	<line class="st41" x1="-3611.4" y1="-1755.7" x2="-3603" y2="-1746.8"/>
	<line class="st41" x1="-3611.7" y1="-1747.3" x2="-3602.7" y2="-1755.7"/>
	<path class="st19" d="M-4244.6-989.1c5.4-5.6,8.6-13.1,8.6-21.5c0-17.1-13.9-31-31-31c-17.1,0-31,13.9-31,31
		c0,9.8,4.5,18.5,11.6,24.2c-2.2,5.6-8,23.3-5.2,51.8h55.6C-4236-934.5-4230.7-966.4-4244.6-989.1z"/>
	<circle class="st18" cx="-4264.5" cy="-1015.9" r="3.5"/>
	<circle class="st18" cx="-4281.5" cy="-1013.1" r="3.5"/>
	<circle class="st62" cx="-4274.5" cy="-1011" r="30.5"/>
	<path class="st66" d="M-4264-1002.8c-3.2,3.7-8.8,4.1-12.4,0.9"/>
	<path class="st62" d="M-4288.6-987.3c0,0-9.4,18.8-6,53.8h55.6c0,0,5.6-33.4-9.7-56.2"/>
	<line class="st62" x1="-4360" y1="-1055.5" x2="-4360" y2="-918.5"/>
	<line class="st62" x1="-4360" y1="-552.5" x2="-4360" y2="-415.5"/>
	<line class="st62" x1="-4360" y1="-73.5" x2="-4360" y2="63.5"/>
	<g id="POueHo_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="POueHo_2_" xlink:href="DEBB70B809924F84.jpg"  transform="matrix(1 0 0 1 -2971 -3325.5432)">
		</image>
	</g>
	<g id="FkRr9g_1_">
		
			<image style="overflow:visible;" width="800" height="600" id="FkRr9g_2_" xlink:href="DEBB70B809924FAB.jpg"  transform="matrix(1 0 0 1 -4391 -3274.5432)">
		</image>
	</g>
	<rect x="-3216" y="-2019.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -3210 -2005.3434)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -2035.0918 -2004.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Configuring NPM</tspan><tspan x="116" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:24;">	</tspan><tspan x="144" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">Using NPM</tspan><tspan x="216.4" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:31;">	</tspan><tspan x="252" y="0" style="font-family:'Poppins-Regular'; font-size:14px;">CLI Commands</tspan><tspan x="359.8" y="0" style="font-family:'Poppins-Regular'; font-size:14px; letter-spacing:-3;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M-3180.9-2005.5h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-2005.5z M-3150.2-2018.5v13h6.4v-9.7h3.2v9.7h3.2v-9.7
				h3.2v9.7h3.2v-13H-3150.2L-3150.2-2018.5z M-3159.2-2015.2h3.2v6.5h-3.2V-2015.2z M-3165.6-2002.3h6.4v-3.2h6.4v-13h-12.8
				V-2002.3z"/>
			<rect x="-3180.9" y="-2018.5" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-3115.9,-2015.3 -3115.9,-2009 -3109.8,-2009 -3109.8,-2005.9 -3116,-2005.9 -3122.3,-2005.9 
			-3122.2,-2018.5 -3109.8,-2018.5 -3109.8,-2015.4 		"/>
		<rect x="-3107.2" y="-2018.5" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-3101.3" y="-2011.9" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -5106.8926 1092.6321)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-3080.1" y="-2010.5" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -5085.5234 1072.77)" class="st46" width="2" height="8.3"/>
		<rect x="-3093.1" y="-2018.5" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_38_" gradientUnits="userSpaceOnUse" x1="-3242" y1="-2040.0431" x2="-1642" y2="-2040.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st67" x1="-3242" y1="-2040" x2="-1642" y2="-2040"/>
	<linearGradient id="SVGID_39_" gradientUnits="userSpaceOnUse" x1="-3239" y1="-288.7924" x2="-1641" y2="-288.7924">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st68" points="-1641,646.5 -1641,-1224 -3239,-1224 -3239,642.5 	"/>
	<line class="st50" x1="-2523.2" y1="-950.2" x2="-2524.9" y2="-950.2"/>
	<line class="st38" x1="-2939.3" y1="-927" x2="-2941.5" y2="-927"/>
	
		<linearGradient id="SVGID_40_" gradientUnits="userSpaceOnUse" x1="-4577.875" y1="978.6965" x2="-4197.875" y2="978.6965" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -2848.011 -4203.3799)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st69" points="-2076.6,60.9 -2103.9,439.9 -2297.4,426 -2270.1,47 	"/>
	
		<linearGradient id="SVGID_41_" gradientUnits="userSpaceOnUse" x1="-2109.3376" y1="-281.1946" x2="-1905.3374" y2="-281.1946" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 46.8033 -571.8436)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st70" points="-1836.5,-283.1 -2038.2,-252.5 -2123.5,-814 -1921.8,-844.6 	"/>
	
		<linearGradient id="SVGID_42_" gradientUnits="userSpaceOnUse" x1="-4493.1201" y1="614.242" x2="-4113.1201" y2="614.242" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -2945.3704 -5199.7769)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st71" points="-2712.8,-1032.1 -2756.5,-654.6 -2949.2,-677 -2905.5,-1054.5 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-2038.6-738l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2036-739.2-2037.2-738-2038.6-738z"/>
		</g>
		<g>
			<path class="st72" d="M-2048.6-748l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2046-749.2-2047.2-748-2048.6-748z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -2531.0342 -905.9582)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-20.3" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="DEBB70B809924FA9.png"  transform="matrix(1 0 0 1 -2532 -984.5432)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -2528.8369 -952.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_43_" gradientUnits="userSpaceOnUse" x1="-3991.1377" y1="-741.6052" x2="-3611.1375" y2="-741.6052" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 912.5291 798.3806)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st73" points="-2724.7,322.5 -3104.3,340.2 -3113.3,146.4 -2733.7,128.7 	"/>
	<path class="st74" d="M-2604.7-879h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-2602.7-879.9-2603.6-879-2604.7-879z
		"/>
	<rect x="-2726.3" y="-975.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809924F97.png"  transform="matrix(1 0 0 1 -2722.1443 -954.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_44_" gradientUnits="userSpaceOnUse" x1="-2680.8501" y1="-938.4619" x2="-2679.1606" y2="-953.0289">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2716.7" y="-948.9" class="st76" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B809924F99.png"  transform="matrix(1 0 0 1 -2721.8997 -937.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_45_" gradientUnits="userSpaceOnUse" x1="-2693.0466" y1="-923.778" x2="-2691.7185" y2="-935.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2716.8" y="-932.5" class="st77" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B809924F9B.png"  transform="matrix(1 0 0 1 -2722.1565 -922.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_46_" gradientUnits="userSpaceOnUse" x1="-2688.3372" y1="-907.3594" x2="-2686.8342" y2="-920.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2716.8" y="-917" class="st78" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B809924F9E.png"  transform="matrix(1 0 0 1 -2723.0876 -905.6309)">
		</image>
		<g>
			<linearGradient id="SVGID_47_" gradientUnits="userSpaceOnUse" x1="-2691.8728" y1="-890.5993" x2="-2690.4941" y2="-902.4869">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2717.2" y="-899.6" class="st79" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B809924F9F.png"  transform="matrix(1 0 0 1 -2665.8997 -937.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_48_" gradientUnits="userSpaceOnUse" x1="-2652.9333" y1="-925.6581" x2="-2652.041" y2="-933.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2660.5" y="-932.5" class="st80" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-2680" y1="-968.5" x2="-2640" y2="-968.5"/>
	<circle class="st18" cx="-2718.5" cy="-969" r="1.5"/>
	<circle class="st18" cx="-2712.5" cy="-969" r="1.5"/>
	<line class="st50" x1="-2521.2" y1="-504.2" x2="-2522.9" y2="-504.2"/>
	<line class="st38" x1="-2937.3" y1="-481" x2="-2939.5" y2="-481"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-2036.6-292l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2034-293.2-2035.2-292-2036.6-292z"/>
		</g>
		<g>
			<path class="st72" d="M-2046.6-302l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2044-303.2-2045.2-302-2046.6-302z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="DEBB70B809924F9A.png"  transform="matrix(1 0 0 1 -2537 -557.5432)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -2533.6113 -525.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-2602.7-433h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-2600.7-433.9-2601.6-433-2602.7-433z
		"/>
	<rect x="-2724.3" y="-529.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809924F95.png"  transform="matrix(1 0 0 1 -2720.1443 -508.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_49_" gradientUnits="userSpaceOnUse" x1="-2678.8501" y1="-492.4619" x2="-2677.1606" y2="-507.0288">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2714.7" y="-502.9" class="st82" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B809924F96.png"  transform="matrix(1 0 0 1 -2719.8997 -491.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_50_" gradientUnits="userSpaceOnUse" x1="-2691.0466" y1="-477.778" x2="-2689.7185" y2="-489.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2714.8" y="-486.5" class="st83" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B809924FF2.png"  transform="matrix(1 0 0 1 -2720.1565 -476.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_51_" gradientUnits="userSpaceOnUse" x1="-2686.3372" y1="-461.3594" x2="-2684.8342" y2="-474.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2714.8" y="-471" class="st84" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B809924FF1.png"  transform="matrix(1 0 0 1 -2721.0876 -459.6309)">
		</image>
		<g>
			<linearGradient id="SVGID_52_" gradientUnits="userSpaceOnUse" x1="-2689.8728" y1="-444.5993" x2="-2688.4941" y2="-456.4869">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2715.2" y="-453.6" class="st85" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B809924FF6.png"  transform="matrix(1 0 0 1 -2663.8997 -491.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_53_" gradientUnits="userSpaceOnUse" x1="-2650.9333" y1="-479.6581" x2="-2650.041" y2="-487.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2658.5" y="-486.5" class="st86" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-2678" y1="-522.5" x2="-2638" y2="-522.5"/>
	<circle class="st18" cx="-2716.5" cy="-523" r="1.5"/>
	<circle class="st18" cx="-2710.5" cy="-523" r="1.5"/>
	<rect x="-2536.9" y="-496.6" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -2536.8857 -483.2658)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="-2518.2" y1="-41.2" x2="-2519.9" y2="-41.2"/>
	<line class="st38" x1="-2934.3" y1="-18" x2="-2936.5" y2="-18"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-2033.6,171l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2031,169.8-2032.2,171-2033.6,171z"/>
		</g>
		<g>
			<path class="st72" d="M-2043.6,161l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6l0-337.3c0-1.4,1.2-2.6,2.6-2.6l789.8,0
				c1.4,0,2.6,1.2,2.6,2.6l0,337.3C-2041,159.8-2042.2,161-2043.6,161z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="DEBB70B809924FF0.png"  transform="matrix(1 0 0 1 -2534 -94.5431)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -2530.6113 -62.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-2599.7,30h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-2597.7,29.1-2598.6,30-2599.7,30z"/>
	<rect x="-2721.3" y="-66.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809924FF3.png"  transform="matrix(1 0 0 1 -2717.1443 -45.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_54_" gradientUnits="userSpaceOnUse" x1="-2675.8501" y1="-29.4619" x2="-2674.1606" y2="-44.0288">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2711.7" y="-39.9" class="st87" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B809924FEF.png"  transform="matrix(1 0 0 1 -2716.8997 -28.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_55_" gradientUnits="userSpaceOnUse" x1="-2688.0466" y1="-14.778" x2="-2686.7185" y2="-26.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2711.8" y="-23.5" class="st88" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B809924FCE.png"  transform="matrix(1 0 0 1 -2717.1565 -13.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_56_" gradientUnits="userSpaceOnUse" x1="-2683.3372" y1="1.6406" x2="-2681.8342" y2="-11.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2711.8" y="-8" class="st89" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B809924FD1.png"  transform="matrix(1 0 0 1 -2718.0876 3.3691)">
		</image>
		<g>
			<linearGradient id="SVGID_57_" gradientUnits="userSpaceOnUse" x1="-2686.8728" y1="18.4007" x2="-2685.4941" y2="6.5131">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2712.2" y="9.4" class="st90" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B809924FD2.png"  transform="matrix(1 0 0 1 -2660.8997 -28.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_58_" gradientUnits="userSpaceOnUse" x1="-2647.9333" y1="-16.6581" x2="-2647.041" y2="-24.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-2655.5" y="-23.5" class="st91" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-2675" y1="-59.5" x2="-2635" y2="-59.5"/>
	<circle class="st18" cx="-2713.5" cy="-60" r="1.5"/>
	<circle class="st18" cx="-2707.5" cy="-60" r="1.5"/>
	<rect x="-2533.9" y="-33.6" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -2533.8857 -20.2658)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<path class="st92" d="M-1883.1-1811.9c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19
		C-1882.9-1812-1883-1811.9-1883.1-1811.9z"/>
	<path class="st92" d="M-1876.9-1784.2c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
		C-1876.3-1784.7-1876.6-1784.3-1876.9-1784.2z"/>
	<path class="st92" d="M-1876.9-1784.2c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-1876.7-1784.3-1876.8-1784.3-1876.9-1784.2z"/>
	<path class="st93" d="M-1870.9-1433.5c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C-1870.7-1433.4-1870.8-1433.4-1870.9-1433.5
		z"/>
	<path class="st93" d="M-1886.6-1412.3c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C-1886-1412.2-1886.4-1412.1-1886.6-1412.3z"/>
	<path class="st93" d="M-1845.2-1431.4c0.2-0.3,0.6-0.3,0.8-0.1s0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2l-25.9-2.9
		c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_59_" gradientUnits="userSpaceOnUse" x1="-303.99" y1="-2219" x2="-1336.01" y2="-989.0863">
		<stop  offset="0" style="stop-color:#D4BEB8;stop-opacity:0.7"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-1619" y="-1981.5" class="st94" width="1598" height="755"/>
	<rect x="-1593" y="-2019.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 -1587 -2005.3434)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<text transform="matrix(1 0 0 1 -215.0918 -2004.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<g>
		<g>
			<path class="st46" d="M-1546.9-2005.5h6.4v-9.7h3.2v9.7h3.2v-13h-12.8V-2005.5z M-1516.2-2018.5v13h6.4v-9.7h3.2v9.7h3.2v-9.7
				h3.2v9.7h3.2v-13H-1516.2L-1516.2-2018.5z M-1525.2-2015.2h3.2v6.5h-3.2V-2015.2z M-1531.6-2002.3h6.4v-3.2h6.4v-13h-12.8
				V-2002.3z"/>
			<rect x="-1546.9" y="-2018.5" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="-1481.9,-2015.3 -1481.9,-2009 -1475.8,-2009 -1475.8,-2005.9 -1482,-2005.9 -1488.3,-2005.9 
			-1488.2,-2018.5 -1475.8,-2018.5 -1475.8,-2015.4 		"/>
		<rect x="-1473.2" y="-2018.5" class="st46" width="6.4" height="12.9"/>
		
			<rect x="-1467.3" y="-2011.9" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3472.8926 -541.368)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="-1446.1" y="-2010.5" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -3451.5237 -561.23)" class="st46" width="2" height="8.3"/>
		<rect x="-1459.1" y="-2018.5" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_60_" gradientUnits="userSpaceOnUse" x1="-1620" y1="-2041.0431" x2="-20" y2="-2041.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st95" x1="-1620" y1="-2041" x2="-20" y2="-2041"/>
	<rect x="-3241.5" y="384" class="st96" width="1602" height="510"/>
	<linearGradient id="SVGID_61_" gradientUnits="userSpaceOnUse" x1="-1099.8535" y1="-1818.7931" x2="-1098.6465" y2="-1818.7931">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st97" x1="-1099" y1="-1818.5" x2="-1099.5" y2="-1819"/>
	<line class="st40" x1="-1053.1" y1="-1885.7" x2="-1055.8" y2="-1885.7"/>
	<line class="st40" x1="-1084.8" y1="-1858.5" x2="-1087.5" y2="-1858.5"/>
	<line class="st40" x1="-888.2" y1="-1827.3" x2="-890.9" y2="-1827.3"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="DEBB70B809924FD0.png"  transform="matrix(1 0 0 1 -1210 -1922.5431)">
			</image>
			<g>
				<path class="st98" d="M-438.1-1903.9v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-439.4-1906.9-438.1-1905.5-438.1-1903.9z"/>
				<path class="st81" d="M-438.1-1903.9v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-439.4-1906.9-438.1-1905.5-438.1-1903.9z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-438.1-1903.6v21.7c0,1.7-1.4,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-439.4-1906.6-438.1-1905.2-438.1-1903.6z"/>
			<path class="st62" d="M-438.1-1903.6v21.7c0,1.7-1.4,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-439.4-1906.6-438.1-1905.2-438.1-1903.6z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="-1052.6" y1="-1810.9" x2="-1055.4" y2="-1810.9"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="DEBB70B809924FCD.png"  transform="matrix(1 0 0 1 -1171 -1866.5431)">
			</image>
			<g>
				<path class="st98" d="M-399.8-1848.2v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-401.1-1851.2-399.8-1849.8-399.8-1848.2z"/>
				<path class="st99" d="M-399.8-1848.2v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-401.1-1851.2-399.8-1849.8-399.8-1848.2z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-400.1-1848.6v21.7c0,1.7-1.4,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-401.4-1851.6-400.1-1850.2-400.1-1848.6z"/>
			<path class="st62" d="M-400.1-1848.6v21.7c0,1.7-1.4,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-401.4-1851.6-400.1-1850.2-400.1-1848.6z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="DEBB70B809925033.png"  transform="matrix(1 0 0 1 -1137 -1806.5431)">
			</image>
			<g>
				<path class="st98" d="M-365.4-1789.7v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C-366.3-1791.7-365.4-1790.8-365.4-1789.7z"/>
				<path class="st99" d="M-365.4-1789.7v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C-366.3-1791.7-365.4-1790.8-365.4-1789.7z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="-650.3" y="-1640.7" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="-1021.6,-1644.9 -1025.5,-1649.6 -1014.3,-1659.1 -1025.5,-1668.8 -1021.6,-1673.4 
					-1004.8,-1659.1 				"/>
			</g>
			<g class="st51">
				
					<image style="overflow:visible;opacity:0.2;" width="327" height="66" xlink:href="DEBB70B809925035.png"  transform="matrix(1 0 0 1 -987.5894 -1692.1324)">
				</image>
				<g>
					<g>
						<path class="st8" d="M-949.5-1635.4h-10.4l-17.7-39.6v39.6h-8.5v-52.3h11.5l16.6,37.5v-37.5h9.1c0.1,0.1,0.2,0.3,0.2,0.4
							c0,0.2-0.1,0.5-0.3,0.8c-0.2,0.3-0.4,0.9-0.5,1.7V-1635.4z"/>
						<path class="st8" d="M-937.6-1687.8h17.7c3.1,0,5.8,0.4,8,1.3c2.2,0.8,4,2,5.5,3.4c1.4,1.4,2.5,3.1,3.1,5c0.7,1.9,1,3.9,1,6
							c0,2.1-0.3,4.1-1,6c-0.6,1.9-1.7,3.5-3.1,4.9c-1.4,1.4-3.2,2.5-5.4,3.3c-2.2,0.8-4.8,1.2-7.8,1.2h-8.6v21.2h-9.6V-1687.8z
							 M-928-1679.4v14.9h7.9c1.5,0,2.7-0.2,3.7-0.5c1-0.4,1.9-0.9,2.6-1.5c0.7-0.6,1.2-1.4,1.5-2.3c0.3-0.9,0.5-1.8,0.5-2.9
							c0-1.1-0.2-2.1-0.5-3.1c-0.3-0.9-0.8-1.7-1.5-2.4c-0.7-0.7-1.5-1.2-2.5-1.6c-1-0.4-2.2-0.6-3.6-0.6H-928z"/>
						<path class="st8" d="M-892.2-1687.8h9.9l8.8,24.6l8.7-24.7h10v52.4h-8.5v-38l-7.3,19.7h-6.2l-7.1-19.7v38h-8.4V-1687.8z"/>
						<path class="st8" d="M-768.3-1673c-0.2-0.1-0.4-0.2-0.5-0.3c-0.1-0.2-0.1-0.4-0.2-0.6c0-0.2-0.1-0.5-0.1-0.8
							c0-0.3-0.1-0.6-0.2-1c-0.8-1.8-1.8-3.2-3.2-4.2c-1.3-1-3.1-1.6-5.1-1.6c-1.7,0-3.2,0.5-4.5,1.4c-1.3,1-2.5,2.3-3.4,4
							c-1,1.7-1.7,3.8-2.2,6.2c-0.5,2.4-0.8,5.1-0.8,8.1c0,2.9,0.3,5.5,0.8,7.9c0.6,2.4,1.3,4.5,2.4,6.3c1,1.8,2.2,3.1,3.7,4.2
							c1.4,1,3,1.5,4.7,1.5c2,0,3.8-0.6,5.3-1.9c1.5-1.3,2.9-3,4.2-5.1l7.1,4.6c-2,3.4-4.4,6-7.2,7.7c-2.8,1.7-5.9,2.6-9.2,2.6
							c-3.1,0-5.9-0.5-8.6-1.6c-2.6-1.1-4.9-2.8-6.8-5.1c-1.9-2.3-3.4-5.2-4.5-8.6c-1.1-3.4-1.6-7.4-1.6-12.1c0-3.4,0.3-6.5,0.9-9.2
							c0.6-2.7,1.4-5.1,2.4-7.2c1-2.1,2.2-3.8,3.6-5.2c1.4-1.4,2.8-2.6,4.4-3.5c1.5-0.9,3.1-1.6,4.8-2c1.7-0.4,3.3-0.6,4.8-0.6
							c2,0,3.8,0.3,5.6,0.8c1.8,0.6,3.5,1.4,5,2.4c1.5,1.1,2.9,2.3,4.1,3.8c1.2,1.5,2.2,3.1,2.9,4.9L-768.3-1673z"/>
						<path class="st8" d="M-715.1-1643.3v7.9h-33.4v-52.3h10.2c0.1,0.1,0.2,0.3,0.2,0.4c0,0.2-0.1,0.5-0.3,0.8
							c-0.2,0.3-0.4,0.9-0.5,1.7v41.5H-715.1z"/>
						<path class="st8" d="M-700-1687.8h29.2v7.8h-10.2v36.8h10.6v7.7h-30.2v-7.8h10.2v-36.7h-9.7V-1687.8z"/>
					</g>
				</g>
			</g>
			<rect x="-1026.7" y="-1572.1" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 -1026.7363 -1557.3141)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="-1024.7" y="-1472.5" class="st14" width="230" height="59.2"/>
				<rect x="-1018.8" y="-1466.7" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 -967.8589 -1434.1315)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M-365.1-1788.6v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C-366.4-1791.6-365.1-1790.2-365.1-1788.6z"/>
			<path class="st62" d="M-365.1-1788.6v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C-366.4-1791.6-365.1-1790.2-365.1-1788.6z"/>
		</g>
		<line class="st66" x1="-1105.4" y1="-1781.9" x2="-1097" y2="-1772.9"/>
		<line class="st66" x1="-1105.7" y1="-1773.2" x2="-1096.7" y2="-1781.6"/>
		<line class="st66" x1="-1145.4" y1="-1841.9" x2="-1137" y2="-1832.9"/>
		<line class="st66" x1="-1145.7" y1="-1833.2" x2="-1136.7" y2="-1841.6"/>
		<line class="st66" x1="-1182.4" y1="-1896.9" x2="-1174" y2="-1887.9"/>
		<line class="st66" x1="-1182.7" y1="-1888.2" x2="-1173.7" y2="-1896.6"/>
	</g>
	<linearGradient id="SVGID_62_" gradientUnits="userSpaceOnUse" x1="-1619" y1="-288.7924" x2="-21" y2="-288.7924">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.1444" style="stop-color:#FF4B01"/>
		<stop  offset="0.7119" style="stop-color:#C12127"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st100" points="-21,646.5 -21,-1224 -1619,-1224 -1619,642.5 	"/>
	<line class="st50" x1="-903.2" y1="-950.2" x2="-904.9" y2="-950.2"/>
	<line class="st38" x1="-1319.3" y1="-927" x2="-1321.5" y2="-927"/>
	
		<linearGradient id="SVGID_63_" gradientUnits="userSpaceOnUse" x1="-4461.519" y1="2594.5125" x2="-4081.5188" y2="2594.5125" gradientTransform="matrix(7.182470e-02 -0.9974 0.9974 7.182470e-02 -2848.011 -4203.3799)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st101" points="-456.6,60.9 -483.9,439.9 -677.4,426 -650.1,47 	"/>
	
		<linearGradient id="SVGID_64_" gradientUnits="userSpaceOnUse" x1="-507.6919" y1="-38.0262" x2="-303.6916" y2="-38.0262" gradientTransform="matrix(0.9887 -0.1501 0.1501 0.9887 46.8033 -571.8436)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st102" points="-216.5,-283.1 -418.2,-252.5 -503.5,-814 -301.8,-844.6 	"/>
	
		<linearGradient id="SVGID_65_" gradientUnits="userSpaceOnUse" x1="-4306.4731" y1="2223.4539" x2="-3926.4729" y2="2223.4539" gradientTransform="matrix(0.1152 -0.9933 0.9933 0.1152 -2945.3704 -5199.7769)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st103" points="-1092.8,-1032.1 -1136.5,-654.6 -1329.2,-677 -1285.5,-1054.5 	"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-418.6-738l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-1078c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-416-739.2-417.2-738-418.6-738z"/>
		</g>
		<g>
			<path class="st72" d="M-428.6-748l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-1088c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-426-749.2-427.2-748-428.6-748z"/>
		</g>
	</g>
	<g>
		<text transform="matrix(0.9755 0 0 1 -921.3135 -905.9582)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">Nunc malesuada suscipit enim at feugiat. </tspan><tspan x="-21.5" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:19px;">    Duis id mauris lectus. Donec a sagittis lectus.</tspan></text>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="236" height="59" xlink:href="DEBB70B809925037.png"  transform="matrix(1 0 0 1 -916 -984.5432)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -912.8369 -952.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Easy to Use</text>
		</g>
	</g>
	
		<linearGradient id="SVGID_66_" gradientUnits="userSpaceOnUse" x1="-2372.8923" y1="-666.2218" x2="-1992.8923" y2="-666.2218" gradientTransform="matrix(0.9989 -4.653295e-02 4.653295e-02 0.9989 912.5291 798.3806)">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<polygon class="st104" points="-1104.7,322.5 -1484.3,340.2 -1493.3,146.4 -1113.7,128.7 	"/>
	<path class="st74" d="M-984.7-879h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-982.7-879.9-983.6-879-984.7-879z"/>
	<rect x="-1106.3" y="-975.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809925034.png"  transform="matrix(1 0 0 1 -1102.1442 -954.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_67_" gradientUnits="userSpaceOnUse" x1="-1060.8502" y1="-938.4619" x2="-1059.1606" y2="-953.0289">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1096.7" y="-948.9" class="st105" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B80992503C.png"  transform="matrix(1 0 0 1 -1101.8997 -937.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_68_" gradientUnits="userSpaceOnUse" x1="-1073.0468" y1="-923.778" x2="-1071.7185" y2="-935.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1096.8" y="-932.5" class="st106" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B80992503D.png"  transform="matrix(1 0 0 1 -1102.1566 -922.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_69_" gradientUnits="userSpaceOnUse" x1="-1068.3372" y1="-907.3594" x2="-1066.8344" y2="-920.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1096.8" y="-917" class="st107" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B80992503F.png"  transform="matrix(1 0 0 1 -1103.0878 -905.6309)">
		</image>
		<g>
			<linearGradient id="SVGID_70_" gradientUnits="userSpaceOnUse" x1="-1071.8729" y1="-890.5993" x2="-1070.4941" y2="-902.4869">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1097.2" y="-899.6" class="st108" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B80992503B.png"  transform="matrix(1 0 0 1 -1045.8997 -937.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_71_" gradientUnits="userSpaceOnUse" x1="-1032.9332" y1="-925.6581" x2="-1032.0411" y2="-933.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1040.5" y="-932.5" class="st109" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1060" y1="-968.5" x2="-1020" y2="-968.5"/>
	<circle class="st18" cx="-1098.5" cy="-969" r="1.5"/>
	<circle class="st18" cx="-1092.5" cy="-969" r="1.5"/>
	<line class="st50" x1="-901.2" y1="-504.2" x2="-902.9" y2="-504.2"/>
	<line class="st38" x1="-1317.3" y1="-481" x2="-1319.5" y2="-481"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-416.6-292l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-632c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-414-293.2-415.2-292-416.6-292z"/>
		</g>
		<g>
			<path class="st72" d="M-426.6-302l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-642c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-424-303.2-425.2-302-426.6-302z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="DEBB70B809925036.png"  transform="matrix(1 0 0 1 -917 -557.5432)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -913.6113 -525.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-982.7-433h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-980.7-433.9-981.6-433-982.7-433z"/>
	<rect x="-1104.3" y="-529.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809925018.png"  transform="matrix(1 0 0 1 -1100.1442 -508.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_72_" gradientUnits="userSpaceOnUse" x1="-1058.8502" y1="-492.4619" x2="-1057.1606" y2="-507.0288">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1094.7" y="-502.9" class="st110" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B80992501B.png"  transform="matrix(1 0 0 1 -1099.8997 -491.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_73_" gradientUnits="userSpaceOnUse" x1="-1071.0468" y1="-477.778" x2="-1069.7185" y2="-489.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1094.8" y="-486.5" class="st111" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B809925019.png"  transform="matrix(1 0 0 1 -1100.1566 -476.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_74_" gradientUnits="userSpaceOnUse" x1="-1066.3372" y1="-461.3594" x2="-1064.8344" y2="-474.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1094.8" y="-471" class="st112" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B809925067.png"  transform="matrix(1 0 0 1 -1101.0878 -459.6309)">
		</image>
		<g>
			<linearGradient id="SVGID_75_" gradientUnits="userSpaceOnUse" x1="-1069.8729" y1="-444.5993" x2="-1068.4941" y2="-456.4869">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1095.2" y="-453.6" class="st113" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B809925068.png"  transform="matrix(1 0 0 1 -1043.8997 -491.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_76_" gradientUnits="userSpaceOnUse" x1="-1030.9332" y1="-479.6581" x2="-1030.0411" y2="-487.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1038.5" y="-486.5" class="st114" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1058" y1="-522.5" x2="-1018" y2="-522.5"/>
	<circle class="st18" cx="-1096.5" cy="-523" r="1.5"/>
	<circle class="st18" cx="-1090.5" cy="-523" r="1.5"/>
	<rect x="-916.9" y="-496.6" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -916.8857 -483.2658)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<line class="st50" x1="-898.2" y1="-41.2" x2="-899.9" y2="-41.2"/>
	<line class="st38" x1="-1314.3" y1="-18" x2="-1316.5" y2="-18"/>
	<g class="st16">
		<g>
			<path class="st14" d="M-413.6,171l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-169c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-411,169.8-412.2,171-413.6,171z"/>
		</g>
		<g>
			<path class="st72" d="M-423.6,161l-789.8,0c-1.4,0-2.6-1.2-2.6-2.6V-179c0-1.4,1.2-2.6,2.6-2.6l789.8,0c1.4,0,2.6,1.2,2.6,2.6
				l0,337.3C-421,159.8-422.2,161-423.6,161z"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="202" height="59" xlink:href="DEBB70B80992506B.png"  transform="matrix(1 0 0 1 -914 -94.5431)">
		</image>
		<g>
			
				<text transform="matrix(1 0 0 1 -910.6113 -62.8224)" class="st18" style="font-family:'Poppins-SemiBold'; font-size:36px; letter-spacing:2;">Ultra Fast</text>
		</g>
	</g>
	<path class="st74" d="M-979.7,30h-120.2c-1.1,0-2-0.9-2-2v-81h124.2c0,20.3,0,40.7,0,61v20C-977.7,29.1-978.6,30-979.7,30z"/>
	<rect x="-1101.3" y="-66.5" class="st75" width="123.2" height="14"/>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="85" height="18" xlink:href="DEBB70B809925069.png"  transform="matrix(1 0 0 1 -1097.1442 -45.6873)">
		</image>
		<g>
			<linearGradient id="SVGID_77_" gradientUnits="userSpaceOnUse" x1="-1055.8502" y1="-29.4619" x2="-1054.1606" y2="-44.0288">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1091.7" y="-39.9" class="st115" width="73.4" height="6.2"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="60" height="17" xlink:href="DEBB70B80992506F.png"  transform="matrix(1 0 0 1 -1096.8997 -28.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_78_" gradientUnits="userSpaceOnUse" x1="-1068.0468" y1="-14.778" x2="-1066.7185" y2="-26.2296">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1091.8" y="-23.5" class="st116" width="48.8" height="5.9"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="70" height="18" xlink:href="DEBB70B80992506A.png"  transform="matrix(1 0 0 1 -1097.1566 -13.6997)">
		</image>
		<g>
			<linearGradient id="SVGID_79_" gradientUnits="userSpaceOnUse" x1="-1063.3372" y1="1.6406" x2="-1061.8344" y2="-11.3168">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1091.8" y="-8" class="st117" width="58.4" height="6.4"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="64" height="19" xlink:href="DEBB70B809925065.png"  transform="matrix(1 0 0 1 -1098.0878 3.3691)">
		</image>
		<g>
			<linearGradient id="SVGID_80_" gradientUnits="userSpaceOnUse" x1="-1066.8729" y1="18.4007" x2="-1065.4941" y2="6.5131">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1092.2" y="9.4" class="st118" width="52" height="6"/>
		</g>
	</g>
	<g>
		
			<image style="overflow:visible;opacity:0.4;" width="27" height="17" xlink:href="DEBB70B80992500F.png"  transform="matrix(1 0 0 1 -1040.8997 -28.4428)">
		</image>
		<g>
			<linearGradient id="SVGID_81_" gradientUnits="userSpaceOnUse" x1="-1027.9332" y1="-16.6581" x2="-1027.0411" y2="-24.3495">
				<stop  offset="0" style="stop-color:#FB8817"/>
				<stop  offset="1" style="stop-color:#E02AFF"/>
			</linearGradient>
			<rect x="-1035.5" y="-23.5" class="st119" width="16" height="5.9"/>
		</g>
	</g>
	<line class="st81" x1="-1055" y1="-59.5" x2="-1015" y2="-59.5"/>
	<circle class="st18" cx="-1093.5" cy="-60" r="1.5"/>
	<circle class="st18" cx="-1087.5" cy="-60" r="1.5"/>
	<rect x="-913.9" y="-33.6" class="st47" width="389.8" height="118.3"/>
	<text transform="matrix(0.9755 0 0 1 -913.8857 -20.2658)"><tspan x="0" y="0" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer </tspan><tspan x="0" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibh eu</tspan><tspan x="387.7" y="27" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">-</tspan><tspan x="0" y="54" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">ismod Lorem ipsum dolor sit amet, tetuer </tspan><tspan x="0" y="81" class="st18" style="font-family:'Poppins-Regular'; font-size:18px;">adipiscing elit, sed diam nonummy nibmod </tspan></text>
	<rect x="-1621.5" y="384" class="st64" width="1602" height="444"/>
	<path class="st120" d="M-1513-1572.5"/>
	<path class="st120" d="M-1495.5-1555"/>
	<path class="st14" d="M-1393.4-1374.9c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C-1393.2-1374.9-1393.3-1374.9-1393.4-1374.9z"/>
	<path class="st14" d="M-1393.8-1375.7c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-1402.6-1348.1c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
		C-1402.4-1348.1-1402.5-1348.1-1402.6-1348.1z"/>
	<linearGradient id="SVGID_82_" gradientUnits="userSpaceOnUse" x1="-1356.4111" y1="-1648.968" x2="-1326.4088" y2="-1648.968">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_83_" gradientUnits="userSpaceOnUse" x1="-1356.7821" y1="-1648.968" x2="-1326.0377" y2="-1648.968">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st121" d="M-1337.3-1640.1c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C-1337.2-1640.1-1337.2-1640.1-1337.3-1640.1z"/>
	<linearGradient id="SVGID_84_" gradientUnits="userSpaceOnUse" x1="-1356.4128" y1="-1632.7845" x2="-1329.1738" y2="-1632.7845">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_85_" gradientUnits="userSpaceOnUse" x1="-1356.7838" y1="-1632.7845" x2="-1328.8027" y2="-1632.7845">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st122" d="M-1329.4-1622.9c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-1329.1-1623.3-1329.2-1623-1329.4-1622.9z"/>
	<linearGradient id="SVGID_86_" gradientUnits="userSpaceOnUse" x1="-1330.0281" y1="-1639.4987" x2="-1318.5287" y2="-1639.4987">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_87_" gradientUnits="userSpaceOnUse" x1="-1330.399" y1="-1639.4987" x2="-1318.1577" y2="-1639.4987">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st123" d="M-1329.4-1622.9c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-1329.3-1623-1329.4-1623-1329.4-1622.9z"/>
	<linearGradient id="SVGID_88_" gradientUnits="userSpaceOnUse" x1="-173.276" y1="-1400.4655" x2="-136.0884" y2="-1400.4655">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st124" d="M-162.9-1389.5c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C-162.7-1389.4-162.8-1389.4-162.9-1389.5z"
		/>
	<linearGradient id="SVGID_89_" gradientUnits="userSpaceOnUse" x1="-189.0637" y1="-1391.3448" x2="-137.1035" y2="-1391.3448">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st125" d="M-178.6-1368.3c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1s0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1s25.3,4,25.1,4.3l-40.7,17.7
		C-178-1368.2-178.4-1368.1-178.6-1368.3z"/>
	<linearGradient id="SVGID_90_" gradientUnits="userSpaceOnUse" x1="-178.8731" y1="-1376.4922" x2="-136.0858" y2="-1376.4922">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st126" d="M-137.2-1387.4c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<linearGradient id="SVGID_91_" gradientUnits="userSpaceOnUse" x1="-251.4985" y1="-1802.4865" x2="-196.9198" y2="-1802.4865">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st127" d="M-203.1-1821.7C-203.1-1821.8-203.1-1821.8-203.1-1821.7c-0.1-0.1-0.1-0.2-0.1-0.2c0,0-0.1-0.1-0.1-0.1
		c0,0,0,0-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l-26.6-8.5c-0.2-0.1-0.5,0-0.6,0.1l-20.5,19.1c0,0,0,0,0,0
		c-0.1,0.1-0.1,0.1-0.1,0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1,0,0.2c0,0,0,0.1,0,0.1c0,0,0,0,0,0l6.2,27.7c0.1,0.2,0.2,0.4,0.4,0.5
		l26.6,8.4c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.3-0.1c0,0,0,0,0,0l20.5-19.1c0.2-0.2,0.2-0.4,0.2-0.6L-203.1-1821.7z M-244.1-1783.8
		l-5.9-26.3l25.2,8l5.9,26.3L-244.1-1783.8z"/>
	<path class="st92" d="M-1311.5-2137.8l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C-1311.4-2137.5-1311.4-2137.7-1311.5-2137.8z M-1348.3-2137.7l8.9-15.4l17.6-0.1l-8.9,15.5
		L-1348.3-2137.7z"/>
	<rect x="37" y="-2020.5" class="st47" width="22" height="22.6"/>
	
		<text transform="matrix(1 0 0 1 43 -2006.3434)" class="st35" style="font-family:'SourceCodeVariable-Roman'; font-size:20px; letter-spacing:1;">❤</text>
	<g>
		<g>
			<path class="st46" d="M72.1-2006.5h6.4v-9.7h3.2v9.7h3.2v-13H72.1V-2006.5z M102.8-2019.5v13h6.4v-9.7h3.2v9.7h3.2v-9.7h3.2v9.7
				h3.2v-13H102.8L102.8-2019.5z M93.8-2016.2H97v6.5h-3.2V-2016.2z M87.4-2003.3h6.4v-3.2h6.4v-13H87.4V-2003.3z"/>
			<rect x="72.1" y="-2019.5" class="st47" width="49.9" height="16.2"/>
		</g>
		<polygon class="st46" points="137.1,-2016.3 137.1,-2010 143.2,-2010 143.2,-2006.9 137,-2006.9 130.7,-2006.9 130.8,-2019.5 
			143.2,-2019.5 143.2,-2016.4 		"/>
		<rect x="145.8" y="-2019.5" class="st46" width="6.4" height="12.9"/>
		
			<rect x="151.7" y="-2012.9" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1854.8926 -2161.3679)" class="st46" width="3.1" height="9.5"/>
		
			<rect x="172.9" y="-2011.5" transform="matrix(-1.836970e-16 1 -1 -1.836970e-16 -1833.5237 -2181.23)" class="st46" width="2" height="8.3"/>
		<rect x="159.9" y="-2019.5" class="st46" width="6.4" height="12.9"/>
	</g>
	<linearGradient id="SVGID_92_" gradientUnits="userSpaceOnUse" x1="0" y1="-2041.0431" x2="1600" y2="-2041.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st128" x1="0" y1="-2041" x2="1600" y2="-2041"/>
	<linearGradient id="SVGID_93_" gradientUnits="userSpaceOnUse" x1="1402.5" y1="-1993.0431" x2="1437.5" y2="-1993.0431">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<line class="st129" x1="1402.5" y1="-1993" x2="1437.5" y2="-1993"/>
	<line class="st130" x1="1" y1="-1976.5" x2="1600" y2="-1976.5"/>
	<g>
		<path class="st14" d="M46-1931c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C40.5-1928.7,42.9-1931,46-1931z"/>
		<path class="st14" d="M56.8-1919.6c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C61.4-1921.4,59.4-1919.6,56.8-1919.6z M56.8-1921.5c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C54.7-1922.4,55.7-1921.5,56.8-1921.5z"/>
		<path class="st14" d="M69-1924.6c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9H63v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2H69V-1924.6z"/>
		<path class="st14" d="M73.5-1926.7h-1v-1.8h1v-0.4c0-2.2,1.2-3.2,3.6-3.1v1.9c-1.1,0-1.4,0.3-1.4,1.3v0.4h1.5v1.8h-1.5v7h-2.2
			V-1926.7z"/>
		<path class="st14" d="M78.4-1931c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3C79-1929.6,78.4-1930.2,78.4-1931z
			 M78.6-1928.6h2.2v8.9h-2.2V-1928.6z"/>
		<path class="st14" d="M86.3-1928.7c1.4,0,2.3,0.6,2.9,1.4v-1.3h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3h2.2
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S84.1-1928.7,86.3-1928.7z
			 M86.9-1926.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S88.1-1926.8,86.9-1926.8z"/>
		<path class="st14" d="M101.7-1919.7h-2.2v-1.1c-0.5,0.8-1.5,1.2-2.6,1.2c-2,0-3.5-1.3-3.5-3.8v-5.2h2.2v4.9c0,1.4,0.8,2.2,1.9,2.2
			c1.2,0,1.9-0.8,1.9-2.2v-4.9h2.2V-1919.7z"/>
		<path class="st14" d="M106-1919.7h-2.2v-8.9h2.2v1.4c0.5-0.9,1.5-1.5,2.7-1.5v2.4h-0.6c-1.3,0-2.1,0.5-2.1,2.2V-1919.7z"/>
		<path class="st14" d="M113.9-1919.6c-2.5,0-4.3-1.8-4.3-4.6c0-2.8,1.8-4.6,4.3-4.6c2.5,0,4.3,1.7,4.3,4.4c0,0.3,0,0.6-0.1,0.9
			h-6.3c0.1,1.3,1,2,2.1,2c0.9,0,1.5-0.5,1.7-1.1h2.4C117.5-1920.9,116-1919.6,113.9-1919.6z M111.8-1925h4.1c0-1.2-0.9-1.9-2.1-1.9
			C112.8-1926.9,111.9-1926.2,111.8-1925z"/>
		<path class="st14" d="M132.7-1930.9v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H132.7z"/>
		<path class="st14" d="M138.9-1924h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C142.8-1925.7,141.7-1924,138.9-1924z
			 M138.8-1925.8c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H138.8z"/>
		<path class="st14" d="M144.3-1930.9h2.5l3.5,8.3l3.5-8.3h2.5v11.2H154v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V-1930.9z"/>
	</g>
	<g>
		<path class="st14" d="M41-1885.9h2.2v6.9c0,1.5,0.8,2.3,2.2,2.3c1.4,0,2.2-0.8,2.2-2.3v-6.9h2.2v6.9c0,2.9-2.1,4.4-4.4,4.4
			c-2.4,0-4.4-1.4-4.4-4.4V-1885.9z"/>
		<path class="st14" d="M55.2-1874.6c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C58.6-1875.7,57.3-1874.6,55.2-1874.6z"/>
		<path class="st14" d="M60.2-1886c0-0.7,0.6-1.3,1.3-1.3c0.8,0,1.3,0.6,1.3,1.3s-0.6,1.3-1.3,1.3
			C60.7-1884.6,60.2-1885.2,60.2-1886z M60.4-1883.6h2.2v8.9h-2.2V-1883.6z"/>
		<path class="st14" d="M70.8-1879.6c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V-1879.6z"/>
		<path class="st14" d="M78.4-1883.7c1.4,0,2.3,0.6,2.9,1.4v-1.3h2.2v8.9c0,2.4-1.4,4.3-4.3,4.3c-2.4,0-4.1-1.2-4.4-3.3H77
			c0.2,0.8,1,1.3,2.1,1.3c1.2,0,2.1-0.7,2.1-2.4v-1.4c-0.5,0.8-1.5,1.5-2.9,1.5c-2.2,0-4-1.8-4-4.6S76.2-1883.7,78.4-1883.7z
			 M79-1881.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6S80.2-1881.8,79-1881.8z"/>
		<path class="st14" d="M98.6-1885.9v11.2h-2.2l-4.9-7.7v7.7h-2.2v-11.2h2.2l4.9,7.7v-7.7H98.6z"/>
		<path class="st14" d="M104.8-1879h-1.8v4.3h-2.2v-11.2h4c2.6,0,3.9,1.5,3.9,3.5C108.7-1880.7,107.6-1879,104.8-1879z
			 M104.7-1880.8c1.2,0,1.8-0.6,1.8-1.6c0-1-0.5-1.6-1.8-1.6h-1.7v3.2H104.7z"/>
		<path class="st14" d="M110.2-1885.9h2.5l3.5,8.3l3.5-8.3h2.5v11.2h-2.2v-7.3l-2.9,7.3h-1.7l-2.9-7.3v7.3h-2.2V-1885.9z"/>
	</g>
	<g>
		<path class="st14" d="M46.9-1842c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6H52c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C41.4-1839.7,43.7-1842,46.9-1842z"/>
		<path class="st14" d="M56-1841.9v9.4h3.6v1.8h-5.8v-11.2H56z"/>
		<path class="st14" d="M61-1841.9h2.2v11.2H61V-1841.9z"/>
		<path class="st14" d="M74-1842c2.4,0,4.4,1.3,5.1,3.6h-2.5c-0.5-1.1-1.5-1.6-2.6-1.6c-1.9,0-3.3,1.4-3.3,3.7
			c0,2.3,1.4,3.7,3.3,3.7c1.2,0,2.1-0.5,2.6-1.6h2.5c-0.7,2.3-2.7,3.6-5.1,3.6c-3.1,0-5.5-2.4-5.5-5.7
			C68.5-1839.7,70.9-1842,74-1842z"/>
		<path class="st14" d="M84.8-1830.6c-2.5,0-4.4-1.8-4.4-4.6c0-2.8,2-4.6,4.4-4.6c2.5,0,4.4,1.8,4.4,4.6
			C89.4-1832.4,87.4-1830.6,84.8-1830.6z M84.8-1832.5c1.2,0,2.3-0.9,2.3-2.6c0-1.8-1.1-2.6-2.2-2.6s-2.2,0.8-2.2,2.6
			C82.7-1833.4,83.7-1832.5,84.8-1832.5z"/>
		<path class="st14" d="M103-1835.6c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9H97v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2v-8.9h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2H103V-1835.6z"/>
		<path class="st14" d="M119.4-1835.6c0-1.4-0.8-2.1-1.9-2.1c-1.2,0-1.9,0.7-1.9,2.1v4.9h-2.2v-4.9c0-1.4-0.8-2.1-1.9-2.1
			c-1.2,0-2,0.7-2,2.1v4.9h-2.2v-8.9h2.2v1.1c0.5-0.7,1.5-1.2,2.5-1.2c1.3,0,2.5,0.6,3,1.7c0.6-1,1.7-1.7,3-1.7
			c2.1,0,3.5,1.3,3.5,3.8v5.2h-2.2V-1835.6z"/>
		<path class="st14" d="M127-1839.7c1.4,0,2.3,0.7,2.9,1.4v-1.3h2.2v8.9h-2.2v-1.3c-0.5,0.8-1.5,1.4-2.9,1.4c-2.2,0-3.9-1.8-3.9-4.6
			S124.8-1839.7,127-1839.7z M127.6-1837.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6
			S128.8-1837.8,127.6-1837.8z"/>
		<path class="st14" d="M140.3-1835.6c0-1.4-0.8-2.2-1.9-2.2c-1.2,0-2,0.8-2,2.2v4.9h-2.2v-8.9h2.2v1.1c0.6-0.8,1.5-1.2,2.6-1.2
			c2,0,3.5,1.3,3.5,3.8v5.2h-2.2V-1835.6z"/>
		<path class="st14" d="M147.9-1839.7c1.1,0,2.2,0.5,2.8,1.4v-4.2h2.2v11.8h-2.2v-1.3c-0.5,0.8-1.5,1.5-2.8,1.5c-2.2,0-4-1.8-4-4.6
			S145.7-1839.7,147.9-1839.7z M148.4-1837.8c-1.2,0-2.3,0.9-2.3,2.6s1.1,2.6,2.3,2.6c1.2,0,2.3-0.9,2.3-2.6
			S149.6-1837.8,148.4-1837.8z"/>
		<path class="st14" d="M158.4-1830.6c-2.2,0-3.7-1.3-3.8-2.9h2.2c0.1,0.7,0.7,1.2,1.6,1.2c0.9,0,1.3-0.4,1.3-0.9
			c0-1.6-4.9-0.6-4.9-3.8c0-1.5,1.3-2.7,3.4-2.7c2.1,0,3.4,1.2,3.5,2.9h-2.1c-0.1-0.7-0.6-1.2-1.5-1.2c-0.8,0-1.2,0.3-1.2,0.8
			c0,1.6,4.8,0.6,4.9,3.9C161.8-1831.7,160.5-1830.6,158.4-1830.6z"/>
	</g>
	<text transform="matrix(1 0 0 1 1402.9082 -2003.2418)"><tspan x="0" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">docs</tspan><tspan x="34.3" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:-1;">	</tspan><tspan x="36" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:32;">	</tspan><tspan x="72" y="0" style="font-family:'Poppins-Medium'; font-size:14px;">npmjs.com</tspan><tspan x="151.5" y="0" style="font-family:'Poppins-Medium'; font-size:14px; letter-spacing:24;">	</tspan></text>
	<text transform="matrix(1 0 0 1 -1292.8369 -1945.6735)"><tspan x="0" y="0" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">These little terminal windows could be secretly </tspan><tspan x="0" y="14.4" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">dismissable, and if you close all they just reappear again</tspan></text>
	<text transform="matrix(1 0 0 1 -522.8369 -1633.6735)" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">&lt;----- imagine this is blinking </text>
	<text transform="matrix(1 0 0 1 -909.8369 -806.6735)" class="st131" style="font-family:'Poppins-Medium'; font-size:12px;">Hmm I should probably put some CTAs in these sections</text>
	<g>
		<rect x="-1212.9" y="546.4" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 -1212.8857 559.7342)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy nibh euismod Lorem ipsum </tspan><tspan x="0" y="27" class="st8" style="font-family:'Poppins-Regular'; font-size:18px;">dolor sit amet, tetuer adipiscing elit, sed diam nonummy nibmod </tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 39.8115 -1785.5431)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">set access level on published packages</text>
	
		<text transform="matrix(0.9997 -2.420000e-02 2.420000e-02 0.9997 41.1204 -1800.894)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">access</text>
	
		<text transform="matrix(1 0 0 1 40.8115 -1756.5709)" style="opacity:0.9;fill:#FB3B49; font-family:'Poppins-SemiBold'; font-size:14px;">add user</text>
	<g>
		<text transform="matrix(1 0 0 1 40.8115 -1663.5709)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bin</text>
	</g>
	<g>
		<text transform="matrix(1 0 0 1 40.8115 -1616.5709)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bugs</text>
	</g>
	<rect x="628" y="-1286.5" class="st133" width="64" height="27"/>
	<rect x="784" y="-1287.5" class="st133" width="64" height="27"/>
	<g>
		<text transform="matrix(1 0 0 1 40.8115 -1568.5709)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">build</text>
	</g>
	<text transform="matrix(1 0 0 1 40.8115 -1524.5709)" class="st51"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">bundle</tspan><tspan x="0" y="39.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">cache </tspan><tspan x="0" y="53" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">manipulates packages cache</tspan><tspan x="0" y="86.6" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">ci </tspan><tspan x="0" y="98.6" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">install a project with a clean slate</tspan><tspan x="0" y="132.2" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">config</tspan><tspan x="0" y="144.2" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">manage npm configuration files</tspan><tspan x="0" y="177.8" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dedupe</tspan><tspan x="0" y="189.8" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">reduce duplication</tspan><tspan x="0" y="223.4" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">deprecate</tspan><tspan x="0" y="235.4" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">deprecate a version of a package</tspan><tspan x="0" y="269" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">dist-tag</tspan><tspan x="0" y="281" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">modify package distribution tags</tspan></text>
	<text transform="matrix(1 0 0 1 40.8115 -1739.5431)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">add a registry user account</text>
	<g>
		<text transform="matrix(1 0 0 1 40.8115 -1709.7887)" class="st14" style="font-family:'Poppins-SemiBold'; font-size:14px;">audit</text>
		<text transform="matrix(1 0 0 1 39.8115 -1694.6564)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">run a security audit</text>
	</g>
	<text transform="matrix(1 0 0 1 39.8115 -1649.5431)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">display npm bin folder</text>
	<rect x="531" y="-1465.5" class="st133" width="96" height="25"/>
	<text transform="matrix(1 0 0 1 41.8115 -1555.5431)" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">build a package</text>
	<text transform="matrix(1 0 0 1 40.8115 -1512.5431)" class="st132" style="font-family:'MyriadPro-Regular'; font-size:11px;">removed</text>
	<rect x="649" y="-1429.5" class="st133" width="49" height="21"/>
	<rect x="1147" y="-1465.5" class="st133" width="125" height="26"/>
	<text transform="matrix(1 0 0 1 41.8115 -1602.5431)" class="st132" style="font-family:'Poppins-Regular'; font-size:10px;">bugs for a package in a web browser maybe</text>
	<polyline class="st134" points="168,-1928.5 174,-1922.5 180,-1928.5 	"/>
	<polyline class="st134" points="170,-1833.5 176,-1839.5 182,-1833.5 	"/>
	<polyline class="st134" points="134,-1883.5 140,-1877.5 146,-1883.5 	"/>
	<rect x="31" y="-1635.5" class="st135" width="282" height="45"/>
	<text transform="matrix(1 0 0 1 498.9707 -1891.4962)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-SemiBold'; font-size:42px;">    npm-bugs</tspan><tspan x="0" y="40" class="st98" style="font-family:'Poppins-Regular'; font-size:24px;">Bugs for a package in a web browser maybe</tspan></text>
	<text transform="matrix(1 0 0 1 500.7861 -1769.2501)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Synopsis</text>
	<text transform="matrix(1 0 0 1 500.7861 -1522.2501)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Description</text>
	<g>
		<rect x="499.3" y="-1493.6" class="st47" width="894.4" height="310.2"/>
		<text transform="matrix(1 0 0 1 499.2539 -1481.7189)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">This command tries to guess at the likely location of a package’s bug tracker URL, and then tries to open it using </tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">the</tspan><tspan x="26" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> --browser</tspan><tspan x="122" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> config param. If no package name is provided, it will search for a</tspan><tspan x="643.2" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;"> package.json</tspan><tspan x="768" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> in the current </tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">folder and use the  </tspan><tspan x="153.9" y="68" class="st98" style="font-family:'AndaleMono'; font-size:16px;">name</tspan><tspan x="192.3" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">  property.</tspan></text>
	</g>
	<text transform="matrix(1 0 0 1 500.7861 -1348.2501)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">Configuration</text>
	<text transform="matrix(1 0 0 1 500.7861 -1305.2501)" class="st137" style="font-family:'Poppins-Medium'; font-size:17px;">browser</text>
	<linearGradient id="SVGID_94_" gradientUnits="userSpaceOnUse" x1="-1998.5436" y1="588.9249" x2="-2875.4563" y2="1633.9889">
		<stop  offset="0" style="stop-color:#D4BEB8"/>
		<stop  offset="1" style="stop-color:#FFFFFF"/>
	</linearGradient>
	<rect x="-3236" y="891.5" class="st138" width="1598" height="440"/>
	<text transform="matrix(1 0 0 1 501.7861 -1176.2501)" class="st137" style="font-family:'Poppins-Medium'; font-size:17px;">registry</text>
	<g>
		<text transform="matrix(1 0 0 1 501.7861 -1017.2502)" class="st136" style="font-family:'Poppins-Medium'; font-size:24px;">See Also</text>
	</g>
	<g>
		<rect x="517.3" y="-1280.3" class="st47" width="754.9" height="125.6"/>
		<text transform="matrix(1 0 0 1 517.2998 -1268.4591)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: OS X:</tspan><tspan x="104.6" y="0" class="st98" style="font-family:'Inconsolata-Bold'; font-size:16px;"> </tspan><tspan x="108" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;open&quot;,</tspan><tspan x="175.2" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;"> Windows: </tspan><tspan x="259.5" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;start&quot;</tspan><tspan x="326.7" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">, Others:  </tspan><tspan x="398.9" y="0" class="st98" style="font-family:'AndaleMono'; font-size:16px;">&quot;xdg-open&quot;</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: String</tspan></text>
	</g>
	<circle class="st98" cx="507" cy="-1274.5" r="4"/>
	<circle class="st98" cx="507" cy="-1239.5" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 517.2998 -1143.7843)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Default: https://registry.npmjs.org/</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Type: url</tspan></text>
	</g>
	<rect x="920" y="-1284.5" class="st133" width="94" height="25"/>
	<circle class="st98" cx="506" cy="-1150.5" r="4"/>
	<circle class="st98" cx="506" cy="-1115.5" r="4"/>
	<g>
		<text transform="matrix(1 0 0 1 506.1631 -969.6349)"><tspan x="0" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-docs</tspan><tspan x="0" y="29" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-view</tspan><tspan x="0" y="58" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-publish</tspan><tspan x="0" y="87" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-registry</tspan><tspan x="0" y="116" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="145" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npm-config</tspan><tspan x="0" y="174" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">npmrc</tspan><tspan x="0" y="203" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">package.json</tspan></text>
	</g>
	<line class="st2" x1="498" y1="-1002.5" x2="1345.5" y2="-1002.5"/>
	<path class="st98" d="M1265.6-1591.5H505.4c-1.9,0-3.4-1.5-3.4-3.4v-146.3c0-1.9,1.5-3.4,3.4-3.4h760.3c1.9,0,3.4,1.5,3.4,3.4
		v146.3C1269-1593,1267.5-1591.5,1265.6-1591.5z"/>
	<text transform="matrix(1 0 0 1 528.2207 -1684.9684)"><tspan x="0" y="0" class="st8" style="font-family:'AndaleMono'; font-size:30px;">npm bugs [&lt;pkgname&gt;]</tspan><tspan x="0" y="60" class="st8" style="font-family:'AndaleMono'; font-size:30px;">aliases: issues</tspan></text>
	<rect x="998" y="-611.5" class="st133" width="247" height="30"/>
	<text transform="matrix(1 0 0 1 531.667 -625.6135)"><tspan x="0" y="0" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">        Found a typo?</tspan><tspan x="147.4" y="0" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;"> Let us know!</tspan><tspan x="0" y="34" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">The current stable version of npm is here. To upgrade run:  </tspan><tspan x="468.1" y="34" class="st98" style="font-family:'AndaleMono'; font-size:16px;">npm install npm@latest -g</tspan><tspan x="0" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">To report bugs or submit feature requests for the docs, please post </tspan><tspan x="537" y="68" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here</tspan><tspan x="573.8" y="68" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">. </tspan><tspan x="0" y="102" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Submit npm issues</tspan><tspan x="151.9" y="102" style="font-family:'Poppins-Regular'; font-size:16px;"> </tspan><tspan x="156.2" y="102" class="st14" style="font-family:'Poppins-SemiBold'; font-size:16px;">here.</tspan></text>
	<rect y="-1976.5" class="st139" width="330" height="1207"/>
	<linearGradient id="SVGID_95_" gradientUnits="userSpaceOnUse" x1="506.9974" y1="-1908.2098" x2="526.4839" y2="-1908.2098">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_96_" gradientUnits="userSpaceOnUse" x1="506.7565" y1="-1908.2098" x2="526.7249" y2="-1908.2098">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st140" d="M519.4-1902.4c0,0-0.1,0-0.1,0l-12-1.2c-0.2,0-0.3-0.1-0.3-0.3c0,0,0-0.1,0-0.1l6.9-9.9
		c0.1-0.1,0.2-0.1,0.3-0.1l12,1.2c0.2,0,0.3,0.1,0.3,0.3c0,0,0,0.1,0,0.1l-6.9,9.9C519.5-1902.5,519.4-1902.4,519.4-1902.4z"/>
	<linearGradient id="SVGID_97_" gradientUnits="userSpaceOnUse" x1="506.9964" y1="-1897.6986" x2="524.688" y2="-1897.6986">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_98_" gradientUnits="userSpaceOnUse" x1="506.7554" y1="-1897.6986" x2="524.929" y2="-1897.6986">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st141" d="M524.5-1891.3c0,0-0.1,0-0.1,0l-12-1.2c-0.1,0-0.2-0.1-0.2-0.2l-5.1-11.1c-0.1-0.1,0-0.3,0.1-0.4
		s0.3,0,0.4,0.1l5.1,11l11.4,1.1l-4.9-10.7c-0.1-0.1,0-0.3,0.1-0.4s0.3,0,0.4,0.1l5.1,11.1C524.7-1891.5,524.7-1891.4,524.5-1891.3z
		"/>
	<linearGradient id="SVGID_99_" gradientUnits="userSpaceOnUse" x1="524.1332" y1="-1902.0594" x2="531.602" y2="-1902.0594">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_100_" gradientUnits="userSpaceOnUse" x1="523.8922" y1="-1902.0594" x2="531.843" y2="-1902.0594">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st142" d="M524.5-1891.3c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.2-0.3-0.1-0.4l6.8-9.8l-5-11c-0.1-0.1,0-0.3,0.1-0.4
		s0.3,0,0.4,0.1l5.1,11.1c0,0.1,0,0.2,0,0.3l-6.9,9.9C524.6-1891.4,524.6-1891.3,524.5-1891.3z"/>
	<path class="st120" d="M-2635,1027.5"/>
	<path class="st120" d="M-3017.5,873"/>
	<path class="st143" d="M-3113.4,1141.1c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C-3113.2,1141.1-3113.3,1141.1-3113.4,1141.1z"/>
	<path class="st14" d="M-3113.8,1140.3c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-3122.6,1167.9c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
		C-3122.4,1167.9-3122.5,1167.9-3122.6,1167.9z"/>
	<linearGradient id="SVGID_101_" gradientUnits="userSpaceOnUse" x1="-2621.4111" y1="951.0319" x2="-2591.4087" y2="951.0319">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_102_" gradientUnits="userSpaceOnUse" x1="-2621.7822" y1="951.0319" x2="-2591.0378" y2="951.0319">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st144" d="M-2602.3,959.9c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2C-2602.2,959.9-2602.2,959.9-2602.3,959.9
		z"/>
	<linearGradient id="SVGID_103_" gradientUnits="userSpaceOnUse" x1="-2621.4128" y1="967.2155" x2="-2594.1738" y2="967.2155">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st145" d="M-2594.4,977.1c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-2594.1,976.7-2594.2,977-2594.4,977.1z"/>
	<linearGradient id="SVGID_104_" gradientUnits="userSpaceOnUse" x1="-2595.0281" y1="960.5013" x2="-2583.5286" y2="960.5013">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st146" d="M-2594.4,977.1c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-2594.3,977-2594.4,977-2594.4,977.1z"/>
	<path class="st120" d="M-1763.5,571.4"/>
	<path class="st92" d="M-1823.1,1005.1c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
		c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19
		C-1822.9,1005-1823,1005.1-1823.1,1005.1z"/>
	<path class="st92" d="M-1816.9,1032.8c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
		s0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
		C-1816.3,1032.3-1816.6,1032.7-1816.9,1032.8z"/>
	<path class="st92" d="M-1816.9,1032.8c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
		c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-1816.7,1032.7-1816.8,1032.7-1816.9,1032.8z"/>
	<path class="st93" d="M-2179.9,1206.5c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
		c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9C-2179.7,1206.6-2179.8,1206.6-2179.9,1206.5
		z"/>
	<path class="st93" d="M-2195.6,1227.7c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
		c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,25.3,4,25.1,4.3
		l-40.7,17.7C-2195,1227.8-2195.4,1227.9-2195.6,1227.7z"/>
	<path class="st93" d="M-2154.2,1208.6c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
		l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	<g>
		<rect x="-2902.9" y="560.4" class="st47" width="951.9" height="118.3"/>
		<text transform="matrix(0.9755 0 0 1 -2878.0029 578.1746)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem ipsum dolor sit amet, consectetuer adipiscing elit, sed diam nonummy</tspan><tspan x="-25.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="-20.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nibh</tspan><tspan x="32.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="37.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">euismod</tspan><tspan x="142.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="148.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">Lorem</tspan><tspan x="222.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="227.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">ipsum</tspan><tspan x="302.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="307.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">dolor</tspan><tspan x="369.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="374.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sit</tspan><tspan x="401.6" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="407" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">amet,</tspan><tspan x="476.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="481.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">tetuer</tspan><tspan x="552.9" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="558.2" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">adipiscing</tspan><tspan x="683.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="688.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">elit,</tspan><tspan x="728.5" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="733.8" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">sed</tspan><tspan x="777.4" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="782.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">diam</tspan><tspan x="845.7" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px; letter-spacing:-1;"> </tspan><tspan x="851" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">nonum</tspan><tspan x="937.1" y="50" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">-</tspan><tspan x="393.3" y="100" class="st8" style="font-family:'Poppins-Regular'; font-size:24px;">my nibmod </tspan></text>
	</g>
	<g>
		<rect x="-2432.3" y="740.3" class="st14" width="23.3" height="6.1"/>
	</g>
	<linearGradient id="SVGID_105_" gradientUnits="userSpaceOnUse" x1="-2791.8535" y1="-1832.7931" x2="-2790.6465" y2="-1832.7931">
		<stop  offset="0" style="stop-color:#F15A24"/>
		<stop  offset="1" style="stop-color:#FF00FF"/>
	</linearGradient>
	<line class="st147" x1="-2791" y1="-1832.5" x2="-2791.5" y2="-1833"/>
	<line class="st40" x1="-2745.1" y1="-1899.7" x2="-2747.8" y2="-1899.7"/>
	<line class="st40" x1="-2776.8" y1="-1872.5" x2="-2779.5" y2="-1872.5"/>
	<line class="st40" x1="-2580.2" y1="-1841.3" x2="-2582.9" y2="-1841.3"/>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="491" xlink:href="DEBB70B80992500D.png"  transform="matrix(1 0 0 1 -2902 -1936.5431)">
			</image>
			<g>
				<path class="st98" d="M-2130.1-1917.9v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-2131.4-1920.9-2130.1-1919.5-2130.1-1917.9z"/>
				<path class="st81" d="M-2130.1-1917.9v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-2131.4-1920.9-2130.1-1919.5-2130.1-1917.9z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-2130.1-1917.6v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-2131.4-1920.6-2130.1-1919.2-2130.1-1917.6z"/>
			<path class="st62" d="M-2130.1-1917.6v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-2131.4-1920.6-2130.1-1919.2-2130.1-1917.6z"/>
		</g>
	</g>
	<g>
		<line class="st40" x1="-2744.6" y1="-1824.9" x2="-2747.4" y2="-1824.9"/>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="770" height="490" xlink:href="DEBB70B809925003.png"  transform="matrix(1 0 0 1 -2863 -1880.5431)">
			</image>
			<g>
				<path class="st98" d="M-2091.8-1862.2v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-2093.1-1865.2-2091.8-1863.8-2091.8-1862.2z"/>
				<path class="st99" d="M-2091.8-1862.2v468.9c0,2.2-1.8,4-4,4h-748.7c-2.2,0-4-1.8-4-4v-468.9c0-1.7,1.3-3,3-3h750.6
					C-2093.1-1865.2-2091.8-1863.8-2091.8-1862.2z"/>
			</g>
		</g>
		<g>
			<path class="st8" d="M-2092.1-1862.6v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-2093.4-1865.6-2092.1-1864.2-2092.1-1862.6z"/>
			<path class="st62" d="M-2092.1-1862.6v21.7c0,1.7-1.3,3-3,3h-750.6c-1.7,0-3-1.3-3-3v-21.7c0-1.7,1.3-3,3-3h750.6
				C-2093.4-1865.6-2092.1-1864.2-2092.1-1862.6z"/>
		</g>
	</g>
	<g>
		<g>
			
				<image style="overflow:visible;opacity:0.2;" width="771" height="490" xlink:href="DEBB70B809925005.png"  transform="matrix(1 0 0 1 -2829 -1820.5431)">
			</image>
			<g>
				<path class="st98" d="M-2057.4-1803.7v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C-2058.3-1805.7-2057.4-1804.8-2057.4-1803.7z"/>
				<path class="st99" d="M-2057.4-1803.7v472.9c0,0.5-0.4,1-1,1h-754.7c-0.5,0-1-0.4-1-1v-472.9c0-1.1,0.9-2,2-2h752.6
					C-2058.3-1805.7-2057.4-1804.8-2057.4-1803.7z"/>
			</g>
		</g>
		<g>
			<g>
				<rect x="-2323.3" y="-1652.7" class="st14" width="23.3" height="6.1"/>
			</g>
			<g>
				<polygon class="st14" points="-2713.6,-1658.9 -2717.5,-1663.6 -2706.3,-1673.1 -2717.5,-1682.8 -2713.6,-1687.4 
					-2696.8,-1673.1 				"/>
			</g>
			<rect x="-2718.7" y="-1586.1" class="st47" width="551.7" height="304.6"/>
			<text transform="matrix(1 0 0 1 -2718.7363 -1571.3141)"><tspan x="0" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">The</tspan><tspan x="40.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="52.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">intelligent</tspan><tspan x="165.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="178.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">package</tspan><tspan x="276.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="288.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">manager</tspan><tspan x="391.3" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="403.5" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">for</tspan><tspan x="434.2" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="446.4" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">the</tspan><tspan x="482.8" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:7;"> </tspan><tspan x="495.1" y="0" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Node </tspan><tspan x="4.7" y="31" class="st8" style="font-family:'Poppins-Medium'; font-size:20px; letter-spacing:1;">Javascript Platform. Install stuff and get coding!</tspan></text>
			<g>
				<rect x="-2716.7" y="-1486.5" class="st14" width="230" height="59.2"/>
				<rect x="-2710.8" y="-1480.7" class="st19" width="230" height="59.2"/>
				
					<text transform="matrix(1 0 0 1 -2659.8589 -1448.1315)" class="st8" style="font-family:'Poppins-Bold'; font-size:20px; letter-spacing:1;">Read Docs</text>
			</g>
		</g>
		<g>
			<path class="st8" d="M-2057.1-1802.6v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C-2058.4-1805.6-2057.1-1804.2-2057.1-1802.6z"/>
			<path class="st62" d="M-2057.1-1802.6v24.7c0,0,0,0,0,0h-756.6c0,0,0,0,0,0v-24.7c0-1.7,1.3-3,3-3h750.6
				C-2058.4-1805.6-2057.1-1804.2-2057.1-1802.6z"/>
		</g>
		<line class="st66" x1="-2797.4" y1="-1795.9" x2="-2789" y2="-1786.9"/>
		<line class="st66" x1="-2797.7" y1="-1787.2" x2="-2788.7" y2="-1795.6"/>
		<line class="st66" x1="-2837.4" y1="-1855.9" x2="-2829" y2="-1846.9"/>
		<line class="st66" x1="-2837.7" y1="-1847.2" x2="-2828.7" y2="-1855.6"/>
		<line class="st66" x1="-2874.4" y1="-1910.9" x2="-2866" y2="-1901.9"/>
		<line class="st66" x1="-2874.7" y1="-1902.2" x2="-2865.7" y2="-1910.6"/>
	</g>
	<path class="st120" d="M-3170-1557.5"/>
	<path class="st120" d="M-3152.5-1540"/>
	<path class="st14" d="M-3050.4-1359.9c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
		l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
		C-3050.2-1359.9-3050.3-1359.9-3050.4-1359.9z"/>
	<path class="st14" d="M-3050.8-1360.7c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
		c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
	<path class="st14" d="M-3059.6-1333.1c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
		c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
		C-3059.4-1333.1-3059.5-1333.1-3059.6-1333.1z"/>
	<linearGradient id="SVGID_106_" gradientUnits="userSpaceOnUse" x1="-3013.4111" y1="-1633.968" x2="-2983.4087" y2="-1633.968">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_107_" gradientUnits="userSpaceOnUse" x1="-3013.7822" y1="-1633.968" x2="-2983.0378" y2="-1633.968">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st148" d="M-2994.3-1625.1c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C-2994.2-1625.1-2994.2-1625.1-2994.3-1625.1z"/>
	<linearGradient id="SVGID_108_" gradientUnits="userSpaceOnUse" x1="-3013.4128" y1="-1617.7845" x2="-2986.1738" y2="-1617.7845">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_109_" gradientUnits="userSpaceOnUse" x1="-3013.7837" y1="-1617.7845" x2="-2985.8027" y2="-1617.7845">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st149" d="M-2986.4-1607.9c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
		C-2986.1-1608.3-2986.2-1608-2986.4-1607.9z"/>
	<linearGradient id="SVGID_110_" gradientUnits="userSpaceOnUse" x1="-2987.0281" y1="-1624.4987" x2="-2975.5286" y2="-1624.4987">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_111_" gradientUnits="userSpaceOnUse" x1="-2987.3992" y1="-1624.4987" x2="-2975.1577" y2="-1624.4987">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st150" d="M-2986.4-1607.9c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-2986.3-1608-2986.4-1608-2986.4-1607.9z"/>
	<path class="st92" d="M-3172.3-1890.7c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.2,0,0.5-0.2,0.6c-0.1,0-0.1,0.1-0.2,0.1l-18.6,0.1
		c-0.2,0-0.3-0.1-0.4-0.2l-9.2-16.2c-0.1-0.2,0-0.5,0.2-0.6c0.1,0,0.1-0.1,0.2-0.1l18.6-0.1
		C-3172.4-1890.8-3172.4-1890.8-3172.3-1890.7z"/>
	<path class="st92" d="M-3172.1-1890.2c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.2,0.4-0.3,0.6-0.2
		c0.1,0,0.1,0.1,0.2,0.2l9.2,16.1c0.1,0.1,0.1,0.3,0,0.4l-9.4,16.4c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6"/>
	<path class="st92" d="M-3162.9-1907.1c0.1,0.1,0.2,0.2,0.2,0.4c0,0.2-0.2,0.4-0.4,0.4l-18.4,0.1l-9.3,16.1
		c-0.1,0.2-0.4,0.3-0.6,0.2c-0.2-0.1-0.3-0.4-0.2-0.6l9.4-16.3c0.1-0.1,0.2-0.2,0.4-0.2l18.6-0.1
		C-3163-1907.1-3162.9-1907.1-3162.9-1907.1z"/>
	<g>
		
			<image style="overflow:visible;opacity:0.2;" width="356" height="93" xlink:href="DEBB70B809925007.png"  transform="matrix(1 0 0 1 -2685.5894 -1716.1324)">
		</image>
		<g>
			<g>
				<path class="st8" d="M-2684.4-1647.4v-45.8h10.8v4.7c1.5-1.7,3.3-3.1,5.4-4.1c2.1-1,4.2-1.6,6.3-1.6c2.2,0,4.3,0.3,6.1,1
					c1.8,0.7,3.4,1.8,4.7,3.3c1.3,1.5,2.3,3.5,3,5.9c0.7,2.4,1.1,5.3,1.1,8.7v27.7h-10.5v-27.6c0-1.8-0.2-3.3-0.5-4.6
					c-0.3-1.3-0.8-2.3-1.3-3.1c-0.6-0.8-1.2-1.4-2-1.8c-0.8-0.4-1.6-0.6-2.6-0.6c-1.3,0-2.6,0.3-3.8,0.8c-1.2,0.6-2.2,1.3-3.1,2.3
					c-0.9,1-1.5,2.2-2,3.6c-0.5,1.4-0.7,2.9-0.7,4.6v26.5H-2684.4z"/>
				<path class="st8" d="M-2633.3-1693.2h10.3v4.7c0.7-1,1.5-1.9,2.4-2.6c0.9-0.7,1.8-1.3,2.7-1.8c0.9-0.5,1.9-0.8,2.8-1
					c1-0.2,1.9-0.3,2.8-0.3c2.6,0,5.1,0.5,7.4,1.6c2.3,1,4.3,2.6,6,4.7s3.1,4.7,4.1,7.8c1,3.1,1.5,6.8,1.5,11.1
					c0,3.9-0.5,7.2-1.5,10.1c-1,2.9-2.4,5.3-4.1,7.2c-1.7,1.9-3.7,3.4-6,4.4s-4.7,1.5-7.2,1.5c-1.8,0-3.7-0.4-5.6-1.2
					c-1.9-0.8-3.5-1.9-4.7-3.3v19.4h-10.7L-2633.3-1693.2z M-2622.5-1670.8c0,2.8,0.2,5.2,0.6,7.3c0.4,2,1,3.7,1.7,4.9
					c0.7,1.2,1.6,2.2,2.7,2.7c1.1,0.6,2.3,0.9,3.6,0.9c1.1,0,2.2-0.3,3.3-0.8c1.1-0.5,2.2-1.4,3.2-2.5c1-1.1,1.7-2.6,2.3-4.5
					c0.6-1.9,0.9-4.2,0.9-6.9c0-5-0.9-8.8-2.6-11.5c-1.7-2.7-4.2-4-7.5-4c-1.7,0-3.1,0.4-4.2,1.2c-1.1,0.8-1.9,1.8-2.6,3.2
					c-0.6,1.3-1.1,2.9-1.3,4.6C-2622.4-1674.5-2622.5-1672.7-2622.5-1670.8z"/>
				<path class="st8" d="M-2583.7-1647.4v-46.3h9.2v2.8c1.4-1.5,2.8-2.6,4.2-3.3c1.4-0.7,2.7-1,4-1c0.6,0,1.2,0.1,1.9,0.3
					c0.7,0.2,1.3,0.5,2,0.9c0.6,0.4,1.3,1,1.9,1.7c0.6,0.7,1.1,1.6,1.6,2.6c1.1-1.8,2.5-3.1,4.1-4.1c1.7-0.9,3.3-1.4,5-1.4
					c1.7,0,3.1,0.3,4.3,0.9c1.1,0.6,2,1.5,2.7,2.6c0.7,1.2,1.2,2.6,1.5,4.4c0.3,1.7,0.4,3.7,0.4,6v33.8h-9.9v-32
					c0-2.5-0.2-4.4-0.6-5.5c-0.4-1.1-1.2-1.7-2.2-1.7c-2.4,0-3.6,3-3.6,9v30.1h-9.9v-31.5c0-1.6-0.1-2.9-0.2-3.9s-0.4-1.7-0.7-2.3
					c-0.3-0.6-0.6-0.9-0.9-1.1c-0.3-0.2-0.6-0.3-1-0.3c-0.6,0-1.1,0.1-1.6,0.4c-0.5,0.3-0.9,0.7-1.2,1.4c-0.3,0.7-0.6,1.6-0.8,2.7
					c-0.2,1.1-0.3,2.6-0.3,4.3v30.4H-2583.7z"/>
				<path class="st8" d="M-2446.2-1677.1c-0.3-0.1-0.4-0.2-0.6-0.4c-0.1-0.2-0.2-0.4-0.3-0.6c-0.1-0.2-0.1-0.4-0.2-0.7
					c-0.1-0.3-0.1-0.6-0.2-0.8c-0.9-1.5-2.2-2.8-3.8-3.8c-1.6-1-3.6-1.6-6-1.6c-1.6,0-3.1,0.4-4.5,1.1c-1.4,0.7-2.7,1.7-3.8,3
					c-1.1,1.3-1.9,2.8-2.5,4.6c-0.6,1.8-0.9,3.7-0.9,5.9c0,2.2,0.3,4.2,0.9,6c0.6,1.8,1.4,3.4,2.4,4.8c1.1,1.4,2.3,2.5,3.7,3.2
					c1.4,0.8,3,1.2,4.8,1.2c0.9,0,1.8-0.1,2.7-0.3c0.9-0.2,1.9-0.5,2.9-1c1-0.5,1.9-1.1,2.9-1.9c0.9-0.8,1.8-1.7,2.7-2.8l6.2,7.4
					c-2.6,2.9-5.3,5-8.3,6.2c-3,1.3-6.2,1.9-9.6,1.9c-3.3,0-6.2-0.6-9-1.8c-2.7-1.2-5.1-2.9-7-5c-1.9-2.1-3.5-4.7-4.6-7.6
					c-1.1-2.9-1.7-6.1-1.7-9.6c0-3.4,0.5-6.6,1.6-9.6c1.1-3,2.6-5.5,4.6-7.7c2-2.2,4.4-3.9,7.2-5.1c2.8-1.3,5.8-1.9,9.2-1.9
					c1.8,0,3.6,0.2,5.4,0.6c1.8,0.4,3.4,0.9,4.9,1.7c1.5,0.7,2.9,1.6,4.2,2.7c1.3,1.1,2.4,2.4,3.3,3.8L-2446.2-1677.1z"/>
				<path class="st8" d="M-2424.9-1713.6h22.4v57.7h12.2v8.5h-35.2v-8.5h12.2v-49.1h-11.7V-1713.6z"/>
				<path class="st8" d="M-2370.5-1693.2h20.9v37.3h9.9v8.5h-31.3v-8.5h10.8v-28.7h-10.3V-1693.2z M-2354.8-1713.7
					c0.9,0,1.8,0.2,2.6,0.5c0.8,0.3,1.5,0.8,2.2,1.4c0.6,0.6,1.1,1.3,1.4,2c0.3,0.8,0.5,1.6,0.5,2.4c0,0.9-0.2,1.7-0.5,2.5
					c-0.4,0.8-0.8,1.5-1.4,2c-0.6,0.6-1.3,1-2.2,1.3c-0.8,0.3-1.7,0.5-2.6,0.5c-0.9,0-1.8-0.2-2.6-0.5c-0.8-0.3-1.5-0.8-2.2-1.3
					c-0.6-0.6-1.1-1.2-1.4-2s-0.5-1.6-0.5-2.5c0-0.8,0.1-1.5,0.4-2.3c0.3-0.7,0.7-1.4,1.2-2c0.5-0.6,1.2-1.1,2.1-1.5
					C-2356.9-1713.5-2355.9-1713.7-2354.8-1713.7z"/>
			</g>
		</g>
	</g>
	<path class="st151" d="M467.9-222.4"/>
	<path class="st152" d="M100.5-148.4c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C100.6-148.4,100.5-148.4,100.5-148.4z"/>
	<path class="st14" d="M100.2-148.9c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M94.9-132.3c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		s0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C95-132.2,95-132.2,94.9-132.3z"/>
	<linearGradient id="SVGID_112_" gradientUnits="userSpaceOnUse" x1="476.0706" y1="-228.6894" x2="494.0978" y2="-228.6894">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_113_" gradientUnits="userSpaceOnUse" x1="475.8477" y1="-228.6894" x2="494.3207" y2="-228.6894">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st153" d="M487.5-223.3c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C487.6-223.4,487.6-223.4,487.5-223.3z"/>
	<linearGradient id="SVGID_114_" gradientUnits="userSpaceOnUse" x1="476.0696" y1="-218.9654" x2="492.4364" y2="-218.9654">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st154" d="M492.3-213c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3
		C492.5-213.3,492.4-213.1,492.3-213z"/>
	<linearGradient id="SVGID_115_" gradientUnits="userSpaceOnUse" x1="491.9231" y1="-222.9996" x2="498.8326" y2="-222.9996">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st155" d="M492.3-213c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C492.4-213.1,492.3-213.1,492.3-213z"/>
	<path class="st92" d="M1468.8-122c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4C1468.9-122.1,1468.8-122.1,1468.8-122z
		"/>
	<path class="st92" d="M1472.5-105.4c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C1472.8-105.7,1472.7-105.5,1472.5-105.4z"/>
	<path class="st92" d="M1472.5-105.4c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		s0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C1472.6-105.5,1472.5-105.4,1472.5-105.4z"/>
	<path class="st93" d="M1219.2-199c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C1219.3-199,1219.2-199,1219.2-199z"/>
	<path class="st93" d="M1209.7-186.3c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		c0.2,0.1,0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C1210.1-186.3,1209.9-186.2,1209.7-186.3z"/>
	<path class="st93" d="M1234.6-197.8c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1
		l-15.5-1.7c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<text transform="matrix(1 0 0 1 636.54 -121.9445)" class="st98" style="font-family:'Poppins-Regular'; font-size:16px;">Some footer text or something here </text>
	<path class="st92" d="M-1494.5-1891.8l-9.2-16.1c0-0.1-0.1-0.1-0.2-0.2c-0.1,0-0.1,0-0.2-0.1c0,0,0,0,0,0l-18.6,0.1
		c-0.2,0-0.3,0.1-0.4,0.2l-9.4,16.3c0,0,0,0,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0
		l9.2,16.2c0.1,0.1,0.2,0.2,0.4,0.2l18.6-0.1c0,0,0,0,0,0c0.1,0,0.1,0,0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0.1,0,0.1-0.1
		c0,0,0,0,0-0.1c0,0,0,0,0,0l9.4-16.4C-1494.4-1891.5-1494.4-1891.7-1494.5-1891.8z M-1531.3-1891.7l8.9-15.4l17.6-0.1l-8.9,15.5
		L-1531.3-1891.7z"/>
	<linearGradient id="SVGID_116_" gradientUnits="userSpaceOnUse" x1="-1268.5916" y1="-2147.5432" x2="-1212.4084" y2="-2147.5432">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st156" d="M-1212.4-2153.1c0-0.1,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1-0.1-0.1c0,0,0,0,0,0l-18.2-21.2
		c-0.2-0.2-0.4-0.3-0.6-0.2l-27.4,5.5c0,0,0,0,0,0c-0.1,0-0.2,0.1-0.2,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1
		c0,0,0,0.1-0.1,0.1c0,0,0,0,0,0l-9.3,26.9c-0.1,0.2,0,0.5,0.1,0.6l18.3,21.1c0.1,0.1,0.2,0.1,0.3,0.2c0.1,0,0.2,0,0.3,0
		c0,0,0,0,0,0l27.4-5.5c0.2,0,0.4-0.2,0.5-0.4l9.2-26.8c0,0,0-0.1,0-0.1C-1212.4-2153-1212.4-2153-1212.4-2153.1z M-1222.8-2126.8
		l-26,5.2l8.7-25.4l26-5.2L-1222.8-2126.8z"/>
	<linearGradient id="SVGID_117_" gradientUnits="userSpaceOnUse" x1="-1166.4846" y1="-2150.5432" x2="-1113.5154" y2="-2150.5432">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st157" d="M-1113.5-2147.5c0,0,0-0.1,0-0.1c0,0,0-0.1,0-0.1c0-0.1,0-0.1,0-0.2c0,0,0,0,0,0l-10.1-24
		c-0.1-0.2-0.3-0.3-0.5-0.4l-25.9-2.9c0,0,0,0,0,0c-0.1,0-0.2,0-0.2,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0-0.1,0-0.1,0.1c0,0-0.1,0-0.1,0.1
		c0,0,0,0,0,0l-15.8,21.2c-0.1,0.2-0.2,0.4-0.1,0.6l10.2,23.9c0,0.1,0.1,0.2,0.2,0.2c0.1,0.1,0.2,0.1,0.2,0.1c0,0,0,0,0,0l25.9,2.9
		c0.2,0,0.4-0.1,0.5-0.2l15.7-21.1C-1113.6-2147.4-1113.6-2147.4-1113.5-2147.5C-1113.6-2147.4-1113.6-2147.5-1113.5-2147.5z
		 M-1118.4-2147.6l2.1,0.2l-0.2,0.1C-1117-2147.3-1117.6-2147.4-1118.4-2147.6z M-1150.2-2173.3l9.6,22.7l-14.9,20.1l-9.6-22.6
		L-1150.2-2173.3z"/>
	<linearGradient id="SVGID_118_" gradientUnits="userSpaceOnUse" x1="-1101.04" y1="-2151.0432" x2="-1050.96" y2="-2151.0432">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st158" d="M-1056.6-2168.7C-1056.7-2168.7-1056.7-2168.7-1056.6-2168.7c0-0.1-0.1-0.1-0.1-0.2c0,0-0.1-0.1-0.1-0.1
		c0,0,0,0-0.1-0.1c0,0-0.1-0.1-0.1-0.1c0,0,0,0,0,0l-24.4-7.8c-0.2-0.1-0.4,0-0.6,0.1l-18.8,17.5c0,0,0,0,0,0
		c-0.1,0.1-0.1,0.1-0.1,0.2c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0.1,0,0.1c0,0,0,0.1,0,0.1c0,0,0,0,0,0l5.7,25.5c0,0.2,0.2,0.4,0.4,0.4
		l24.4,7.8c0.1,0,0.2,0,0.3,0c0.1,0,0.2-0.1,0.2-0.1c0,0,0,0,0,0l18.8-17.5c0.2-0.1,0.2-0.4,0.2-0.6L-1056.6-2168.7z
		 M-1094.3-2133.9l-5.4-24.1l23.1,7.4l5.4,24.1L-1094.3-2133.9z"/>
	<linearGradient id="SVGID_119_" gradientUnits="userSpaceOnUse" x1="-1482.942" y1="-2146.4204" x2="-1455.7031" y2="-2146.4204">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_120_" gradientUnits="userSpaceOnUse" x1="-1483.3131" y1="-2146.4204" x2="-1455.332" y2="-2146.4204">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st159" d="M-1456-2136.6c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6s0.5,0,0.6,0.2l7.9,17.1
		C-1455.6-2136.9-1455.7-2136.7-1456-2136.6z"/>
	<linearGradient id="SVGID_121_" gradientUnits="userSpaceOnUse" x1="-1456.5574" y1="-2153.1345" x2="-1445.058" y2="-2153.1345">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_122_" gradientUnits="userSpaceOnUse" x1="-1456.9283" y1="-2153.1345" x2="-1444.6869" y2="-2153.1345">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st160" d="M-1456-2136.6c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9c-0.1-0.2,0-0.5,0.2-0.6
		c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2C-1455.8-2136.6-1455.9-2136.6-1456-2136.6z"/>
	<linearGradient id="SVGID_123_" gradientUnits="userSpaceOnUse" x1="-1482.9404" y1="-2162.6038" x2="-1452.9381" y2="-2162.6038">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_124_" gradientUnits="userSpaceOnUse" x1="-1483.3114" y1="-2162.6038" x2="-1452.567" y2="-2162.6038">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st161" d="M-1463.8-2153.7c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
		c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
		C-1463.7-2153.8-1463.8-2153.7-1463.8-2153.7z"/>
	<g>
		<path class="st120" d="M-2640.9,1530.6"/>
		<path class="st143" d="M-3119.3,1644.2c-0.1,0-0.2-0.1-0.3-0.2l-18.2-21.2c-0.2-0.3-0.2-0.7,0.1-0.9c0.1-0.1,0.2-0.1,0.3-0.1
			l27.4-5.5c0.2,0,0.5,0,0.6,0.2l18.2,21.2c0.2,0.3,0.2,0.7-0.1,0.9c-0.1,0.1-0.2,0.1-0.3,0.1l-27.4,5.5
			C-3119.1,1644.3-3119.2,1644.3-3119.3,1644.2z"/>
		<path class="st14" d="M-3119.7,1643.4c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.3-0.5,0.5-0.8,0.4
			c-0.1,0-0.2-0.1-0.3-0.2l-18.3-21.1c-0.1-0.2-0.2-0.4-0.1-0.6l9.3-26.9c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8"/>
		<path class="st14" d="M-3128.5,1671c-0.2-0.1-0.4-0.3-0.4-0.5c-0.1-0.3,0.2-0.7,0.5-0.8l27.1-5.4l9.1-26.4
			c0.1-0.3,0.5-0.5,0.8-0.4c0.3,0.1,0.5,0.5,0.4,0.8l-9.2,26.8c-0.1,0.2-0.3,0.4-0.5,0.4l-27.4,5.5
			C-3128.3,1671.1-3128.4,1671.1-3128.5,1671z"/>
		<linearGradient id="SVGID_125_" gradientUnits="userSpaceOnUse" x1="-2627.2891" y1="1454.188" x2="-2597.2869" y2="1454.188">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="0.3721" style="stop-color:#FB8719"/>
			<stop  offset="0.5095" style="stop-color:#FA8420"/>
			<stop  offset="0.608" style="stop-color:#F9802C"/>
			<stop  offset="0.6881" style="stop-color:#F7793D"/>
			<stop  offset="0.7568" style="stop-color:#F47053"/>
			<stop  offset="0.8177" style="stop-color:#F1656E"/>
			<stop  offset="0.8729" style="stop-color:#ED578F"/>
			<stop  offset="0.9237" style="stop-color:#E948B5"/>
			<stop  offset="0.9691" style="stop-color:#E437DE"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<linearGradient id="SVGID_126_" gradientUnits="userSpaceOnUse" x1="-2627.6602" y1="1454.188" x2="-2596.9158" y2="1454.188">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st162" d="M-2608.2,1463.1c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.4-0.2-0.4-0.5c0-0.1,0-0.1,0.1-0.2l10.6-15.2
			c0.1-0.1,0.2-0.2,0.4-0.2l18.5,1.8c0.2,0,0.4,0.2,0.4,0.5c0,0.1,0,0.1-0.1,0.2l-10.7,15.2
			C-2608.1,1463-2608.1,1463.1-2608.2,1463.1z"/>
		<linearGradient id="SVGID_127_" gradientUnits="userSpaceOnUse" x1="-2627.2908" y1="1470.3716" x2="-2600.0518" y2="1470.3716">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st163" d="M-2600.3,1480.2c-0.1,0-0.1,0-0.2,0l-18.5-1.8c-0.2,0-0.3-0.1-0.3-0.2l-7.9-17.2c-0.1-0.2,0-0.5,0.2-0.6
			c0.2-0.1,0.5,0,0.6,0.2l7.8,16.9l17.5,1.7l-7.6-16.5c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1
			C-2600,1479.9-2600.1,1480.1-2600.3,1480.2z"/>
		<linearGradient id="SVGID_128_" gradientUnits="userSpaceOnUse" x1="-2600.906" y1="1463.6575" x2="-2589.4067" y2="1463.6575">
			<stop  offset="0" style="stop-color:#FB8817"/>
			<stop  offset="1" style="stop-color:#E02AFF"/>
		</linearGradient>
		<path class="st164" d="M-2600.3,1480.2c-0.1,0.1-0.3,0-0.4,0c-0.2-0.1-0.2-0.4-0.1-0.6l10.5-15.1l-7.8-16.9
			c-0.1-0.2,0-0.5,0.2-0.6c0.2-0.1,0.5,0,0.6,0.2l7.9,17.1c0.1,0.1,0.1,0.3,0,0.4l-10.6,15.2
			C-2600.2,1480.1-2600.2,1480.2-2600.3,1480.2z"/>
		<path class="st92" d="M-1829,1508.2c-0.1,0-0.2,0-0.3,0l-26.6-8.5c-0.3-0.1-0.5-0.5-0.4-0.8c0-0.1,0.1-0.2,0.2-0.3l20.5-19.1
			c0.2-0.2,0.4-0.2,0.6-0.1l26.6,8.5c0.3,0.1,0.5,0.5,0.4,0.8c0,0.1-0.1,0.2-0.2,0.3l-20.5,19
			C-1828.8,1508.2-1828.9,1508.2-1829,1508.2z"/>
		<path class="st92" d="M-1822.8,1535.9c-0.1,0-0.2,0-0.3,0l-26.6-8.4c-0.2-0.1-0.4-0.2-0.4-0.5l-6.2-27.7c-0.1-0.3,0.1-0.7,0.5-0.8
			c0.3-0.1,0.7,0.1,0.8,0.5l6.1,27.4l25.2,8l-5.9-26.6c-0.1-0.3,0.1-0.7,0.5-0.8c0.3-0.1,0.7,0.1,0.8,0.5l6.2,27.7
			C-1822.2,1535.5-1822.4,1535.8-1822.8,1535.9z"/>
		<path class="st92" d="M-1822.8,1535.9c-0.2,0-0.5,0-0.6-0.2c-0.2-0.3-0.2-0.7,0-0.9l20.2-18.8l-6.1-27.3c-0.1-0.3,0.1-0.7,0.5-0.8
			s0.7,0.1,0.8,0.5l6.2,27.6c0.1,0.2,0,0.5-0.2,0.6l-20.5,19.1C-1822.6,1535.8-1822.7,1535.9-1822.8,1535.9z"/>
		<path class="st93" d="M-2185.8,1709.7c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.3,0-0.7,0.3-0.8c0.1,0,0.2-0.1,0.3,0l25.9,2.9
			c0.2,0,0.4,0.2,0.5,0.4l10.1,24c0.1,0.3,0,0.7-0.3,0.8c-0.1,0-0.2,0.1-0.3,0l-25.8-2.9
			C-2185.6,1709.8-2185.7,1709.8-2185.8,1709.7z"/>
		<path class="st93" d="M-2201.5,1730.8c-0.1-0.1-0.2-0.1-0.2-0.2l-10.2-23.9c-0.1-0.2-0.1-0.4,0.1-0.6l15.8-21.2
			c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.6,20.9l9.6,22.6l15.1-20.3c0.2-0.3,0.6-0.3,0.8-0.1
			c0.3,0.2,25.3,4,25.1,4.3l-40.7,17.7C-2200.9,1731-2201.2,1731-2201.5,1730.8z"/>
		<path class="st93" d="M-2160,1711.7c0.2-0.3,0.6-0.3,0.8-0.1c0.3,0.2,0.3,0.6,0.1,0.8l-15.7,21.1c-0.1,0.2-0.3,0.3-0.5,0.2
			l-25.9-2.9c-0.1,0-0.2-0.1-0.3-0.1c-0.2-0.1-0.3-0.3-0.2-0.5c0-0.3,0.3-0.6,0.7-0.5"/>
	</g>
	<linearGradient id="SVGID_129_" gradientUnits="userSpaceOnUse" x1="323.5711" y1="-119.5477" x2="356.6108" y2="-119.5477">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st165" d="M352.9-131.2C352.9-131.2,352.8-131.2,352.9-131.2c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1L323.7-125c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L352.9-131.2z M328-108.2l-3.6-15.9l15.3,4.9l3.6,15.9L328-108.2z"/>
	<linearGradient id="SVGID_130_" gradientUnits="userSpaceOnUse" x1="876.9289" y1="-197.2353" x2="898.6652" y2="-197.2353">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st166" d="M898.7-199.4C898.7-199.4,898.7-199.4,898.7-199.4C898.7-199.4,898.7-199.5,898.7-199.4c0-0.1,0-0.1,0-0.1
		c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1l-10.6,2.1c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0l10.6-2.1
		c0.1,0,0.2-0.1,0.2-0.2L898.7-199.4C898.7-199.3,898.7-199.3,898.7-199.4C898.7-199.3,898.7-199.4,898.7-199.4z M894.7-189.2l-10,2
		l3.4-9.8l10-2L894.7-189.2z"/>
	<linearGradient id="SVGID_131_" gradientUnits="userSpaceOnUse" x1="1478.5369" y1="-229.9909" x2="1498.9146" y2="-229.9909">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st167" d="M1496.6-237.2C1496.6-237.2,1496.6-237.2,1496.6-237.2C1496.6-237.2,1496.6-237.2,1496.6-237.2
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L1496.6-237.2z M1481.3-223l-2.2-9.8l9.4,3l2.2,9.8
		L1481.3-223z"/>
	<path class="st151" d="M474.4,43.3"/>
	<path class="st152" d="M107,117.4c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.2-0.1-0.4,0-0.5c0.1,0,0.1-0.1,0.2-0.1l16.5-3.3
		c0.1,0,0.3,0,0.4,0.1l11,12.8c0.1,0.2,0.1,0.4,0,0.5c-0.1,0-0.1,0.1-0.2,0.1l-16.5,3.3C107.1,117.4,107.1,117.4,107,117.4z"/>
	<path class="st14" d="M106.8,116.9c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c-0.1,0.2-0.3,0.3-0.5,0.2
		c-0.1,0-0.1-0.1-0.2-0.1l-11-12.7c-0.1-0.1-0.1-0.2-0.1-0.4l5.6-16.1c0.1-0.2,0.3-0.3,0.5-0.2c0.2,0.1,0.3,0.3,0.2,0.5"/>
	<path class="st14" d="M101.4,133.5c-0.1,0-0.2-0.2-0.3-0.3c0-0.2,0.1-0.4,0.3-0.5l16.3-3.3l5.5-15.9c0.1-0.2,0.3-0.3,0.5-0.2
		c0.2,0.1,0.3,0.3,0.2,0.5l-5.5,16.1c0,0.1-0.2,0.2-0.3,0.3l-16.5,3.3C101.6,133.5,101.5,133.5,101.4,133.5z"/>
	<linearGradient id="SVGID_132_" gradientUnits="userSpaceOnUse" x1="482.6115" y1="37.0498" x2="500.6387" y2="37.0498">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="0.3721" style="stop-color:#FB8719"/>
		<stop  offset="0.5095" style="stop-color:#FA8420"/>
		<stop  offset="0.608" style="stop-color:#F9802C"/>
		<stop  offset="0.6881" style="stop-color:#F7793D"/>
		<stop  offset="0.7568" style="stop-color:#F47053"/>
		<stop  offset="0.8177" style="stop-color:#F1656E"/>
		<stop  offset="0.8729" style="stop-color:#ED578F"/>
		<stop  offset="0.9237" style="stop-color:#E948B5"/>
		<stop  offset="0.9691" style="stop-color:#E437DE"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<linearGradient id="SVGID_133_" gradientUnits="userSpaceOnUse" x1="482.3885" y1="37.0498" x2="500.8616" y2="37.0498">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st168" d="M494.1,42.4c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.3c0,0,0-0.1,0-0.1l6.4-9.2
		c0.1-0.1,0.1-0.1,0.2-0.1l11.1,1.1c0.1,0,0.2,0.1,0.2,0.3c0,0,0,0.1,0,0.1l-6.4,9.2C494.2,42.4,494.1,42.4,494.1,42.4z"/>
	<linearGradient id="SVGID_134_" gradientUnits="userSpaceOnUse" x1="482.6105" y1="46.7739" x2="498.9773" y2="46.7739">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st169" d="M498.8,52.7c0,0-0.1,0-0.1,0l-11.1-1.1c-0.1,0-0.2-0.1-0.2-0.1l-4.7-10.3c-0.1-0.1,0-0.3,0.1-0.3
		c0.1-0.1,0.3,0,0.3,0.1l4.7,10.2l10.5,1l-4.6-9.9c-0.1-0.1,0-0.3,0.1-0.3c0.1-0.1,0.3,0,0.3,0.1l4.7,10.3
		C499,52.5,499,52.6,498.8,52.7z"/>
	<linearGradient id="SVGID_135_" gradientUnits="userSpaceOnUse" x1="498.464" y1="42.7396" x2="505.3735" y2="42.7396">
		<stop  offset="0" style="stop-color:#FB8817"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st170" d="M498.8,52.7c-0.1,0-0.2,0-0.3,0c-0.1-0.1-0.1-0.2-0.1-0.4l6.3-9l-4.7-10.1c-0.1-0.1,0-0.3,0.1-0.3
		s0.3,0,0.3,0.1l4.7,10.3c0,0.1,0,0.2,0,0.3l-6.4,9.2C498.9,52.6,498.9,52.7,498.8,52.7z"/>
	<path class="st92" d="M1475.3,143.7c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.2-0.1-0.3-0.3-0.3-0.5c0-0.1,0.1-0.1,0.1-0.2l12.3-11.5
		c0.1-0.1,0.2-0.1,0.4-0.1l16,5.1c0.2,0.1,0.3,0.3,0.3,0.5c0,0.1-0.1,0.1-0.1,0.2l-12.3,11.4
		C1475.4,143.7,1475.4,143.7,1475.3,143.7z"/>
	<path class="st92" d="M1479,160.3c-0.1,0-0.1,0-0.2,0l-16-5.1c-0.1,0-0.2-0.1-0.3-0.3l-3.7-16.7c0-0.2,0.1-0.4,0.3-0.5
		c0.2,0,0.4,0.1,0.5,0.3l3.7,16.4l15.2,4.8l-3.6-16c0-0.2,0.1-0.4,0.3-0.5c0.2,0,0.4,0.1,0.5,0.3l3.7,16.6
		C1479.3,160.1,1479.2,160.3,1479,160.3z"/>
	<path class="st92" d="M1479,160.3c-0.1,0-0.3,0-0.4-0.1c-0.1-0.2-0.1-0.4,0-0.5l12.1-11.3l-3.7-16.4c0-0.2,0.1-0.4,0.3-0.5
		s0.4,0.1,0.5,0.3l3.7,16.6c0,0.1,0,0.3-0.1,0.4l-12.3,11.5C1479.1,160.3,1479.1,160.3,1479,160.3z"/>
	<path class="st93" d="M1225.7,66.7c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.2,0-0.4,0.2-0.5c0.1,0,0.1,0,0.2,0l15.5,1.7
		c0.1,0,0.2,0.1,0.3,0.2l6.1,14.4c0.1,0.2,0,0.4-0.2,0.5c-0.1,0-0.1,0-0.2,0l-15.5-1.7C1225.9,66.8,1225.8,66.7,1225.7,66.7z"/>
	<path class="st93" d="M1216.3,79.4c-0.1,0-0.1-0.1-0.1-0.1l-6.1-14.4c-0.1-0.1,0-0.3,0-0.4l9.5-12.7c0.1-0.2,0.3-0.2,0.5-0.1
		c0.2,0.1,0.2,0.3,0.1,0.5l-9.4,12.6l5.8,13.6l9.1-12.2c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,15.2,2.4,15.1,2.6l-24.5,10.6
		C1216.7,79.5,1216.4,79.5,1216.3,79.4z"/>
	<path class="st93" d="M1241.2,67.9c0.1-0.2,0.3-0.2,0.5-0.1c0.2,0.1,0.2,0.3,0.1,0.5l-9.5,12.7c-0.1,0.1-0.2,0.2-0.3,0.1l-15.5-1.7
		c-0.1,0-0.1,0-0.2-0.1c-0.1-0.1-0.2-0.2-0.1-0.3c0-0.2,0.2-0.3,0.4-0.3"/>
	<linearGradient id="SVGID_136_" gradientUnits="userSpaceOnUse" x1="330.112" y1="146.1916" x2="363.1516" y2="146.1916">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st171" d="M359.4,134.5C359.4,134.5,359.4,134.5,359.4,134.5c0-0.1,0-0.1,0-0.1c0,0,0,0-0.1-0.1c0,0,0,0,0,0
		c0,0-0.1,0-0.1,0c0,0,0,0,0,0l-16.1-5.2c-0.1,0-0.3,0-0.4,0.1l-12.4,11.5c0,0,0,0,0,0c0,0-0.1,0.1-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0.1,0,0.1c0,0,0,0,0,0.1c0,0,0,0,0,0l3.8,16.8c0,0.1,0.1,0.2,0.3,0.3l16.1,5.1c0.1,0,0.1,0,0.2,0c0.1,0,0.1,0,0.2-0.1
		c0,0,0,0,0,0l12.4-11.5c0.1-0.1,0.1-0.2,0.1-0.4L359.4,134.5z M334.6,157.5l-3.6-15.9l15.3,4.9l3.6,15.9L334.6,157.5z"/>
	<linearGradient id="SVGID_137_" gradientUnits="userSpaceOnUse" x1="883.4698" y1="68.504" x2="905.2061" y2="68.504">
		<stop  offset="0" style="stop-color:#FB3B49"/>
		<stop  offset="0.9988" style="stop-color:#EC3B49"/>
	</linearGradient>
	<path class="st172" d="M905.2,66.4C905.2,66.3,905.2,66.3,905.2,66.4C905.2,66.3,905.2,66.3,905.2,66.4c0-0.1,0-0.1,0-0.1
		c0,0,0,0,0,0l-7.1-8.2c-0.1-0.1-0.1-0.1-0.2-0.1L887.2,60c0,0,0,0,0,0c0,0-0.1,0-0.1,0c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0
		c0,0,0,0,0,0c0,0,0,0,0,0l-3.6,10.4c0,0.1,0,0.2,0,0.2l7.1,8.2c0,0,0.1,0.1,0.1,0.1c0,0,0.1,0,0.1,0c0,0,0,0,0,0l10.6-2.1
		c0.1,0,0.2-0.1,0.2-0.2L905.2,66.4C905.2,66.4,905.2,66.4,905.2,66.4C905.2,66.4,905.2,66.4,905.2,66.4z M901.2,76.5l-10,2l3.4-9.8
		l10-2L901.2,76.5z"/>
	<linearGradient id="SVGID_138_" gradientUnits="userSpaceOnUse" x1="1485.0778" y1="35.7484" x2="1505.4554" y2="35.7484">
		<stop  offset="0" style="stop-color:#913FFF"/>
		<stop  offset="1" style="stop-color:#E02AFF"/>
	</linearGradient>
	<path class="st173" d="M1503.1,28.6C1503.1,28.6,1503.1,28.5,1503.1,28.6C1503.1,28.5,1503.1,28.5,1503.1,28.6
		c0-0.1-0.1-0.1-0.1-0.1c0,0,0,0,0,0c0,0,0,0-0.1,0c0,0,0,0,0,0l-9.9-3.2c-0.1,0-0.2,0-0.2,0.1l-7.6,7.1c0,0,0,0,0,0
		c0,0,0,0-0.1,0.1c0,0,0,0,0,0c0,0,0,0,0,0c0,0,0,0,0,0.1c0,0,0,0,0,0c0,0,0,0,0,0l2.3,10.4c0,0.1,0.1,0.1,0.2,0.2l9.9,3.2
		c0,0,0.1,0,0.1,0c0,0,0.1,0,0.1,0c0,0,0,0,0,0l7.6-7.1c0.1-0.1,0.1-0.1,0.1-0.2L1503.1,28.6z M1487.8,42.7l-2.2-9.8l9.4,3l2.2,9.8
		L1487.8,42.7z"/>
</g>
<g id="Layer_2">
</g>
</svg>
