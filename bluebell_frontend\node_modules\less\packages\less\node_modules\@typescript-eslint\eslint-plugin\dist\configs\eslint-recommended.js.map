{"version": 3, "file": "eslint-recommended.js", "sourceRoot": "", "sources": ["../../src/configs/eslint-recommended.ts"], "names": [], "mappings": ";;AAAA;;;;;;;GAOG;AACH,kBAAe;IACb,SAAS,EAAE;QACT;YACE,KAAK,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC;YACxB,KAAK,EAAE;gBACL;;mBAEG;gBACH,kCAAkC;gBAClC,eAAe,EAAE,KAAK;gBACtB,mCAAmC;gBACnC,cAAc,EAAE,KAAK;gBACrB,mCAAmC;gBACnC,cAAc,EAAE,KAAK;gBACrB,mCAAmC;gBACnC,gBAAgB,EAAE,KAAK;gBACvB,mCAAmC;gBACnC,cAAc,EAAE,KAAK;gBACrB,mCAAmC;gBACnC,iBAAiB,EAAE,KAAK;gBACxB,mCAAmC;gBACnC,eAAe,EAAE,KAAK;gBACtB,mCAAmC;gBACnC,sBAAsB,EAAE,KAAK;gBAC7B,qEAAqE;gBACrE,UAAU,EAAE,KAAK;gBACjB,yCAAyC;gBACzC,uBAAuB,EAAE,KAAK;gBAC9B,yCAAyC;gBACzC,cAAc,EAAE,KAAK;gBACrB;;mBAEG;gBACH,kDAAkD;gBAClD,QAAQ,EAAE,OAAO;gBACjB,cAAc,EAAE,OAAO;gBACvB,wEAAwE;gBACxE,oBAAoB,EAAE,OAAO;gBAC7B,eAAe,EAAE,OAAO;aACzB;SACF;KACF;CACF,CAAC"}