.z {
  color: red;
  .c {
    color: green;
  }
}
.only-with-visible,
.z {
  color: green;
  &:hover {
    color: green;
  }
  & {
    color: green;
  }
  & + & {
    color: green;
    .sub {
      color: green;
    }
  }
}

& {
  .hidden {
    hidden: true;
  }
}

@media tv {
  .hidden {
    hidden: true;
  }
}

/* comment is not output */

.zz {
  .y {
    pulled-in: yes /* inline comment survives */;
  }
  /* comment pulled in */
}
@max-size: 450px;
.mixin-with-mediaq(@num) {
  color: green;
  test: @num;
  @media (max-size: @max-size) {
    color: red;
  }
}
//https://github.com/less/less.js/issues/2359
@supports (something: else) {
  .class {
    something: else;
  }
  .nestedToo {
    .class {
      something: else;
    }
  }
  .invisible {
    something: else;
  }
}
//https://github.com/less/less.js/issues/1979
.mixin-with-nested-selectors() {
  .test-rule {
    color: red;
    &:first-child {
      color: blue;
    }
  }
}
.mixin-with-directives(@keyframeName) {
  @keyframes @keyframeName {
    @rules1();
  }
  @supports (animation-name: test) {
    @keyframes @keyframeName {
      @rules2();
    }
    .selector {
      color: red;
    }
  }
  @rules1: {property: value;};
  @rules2: {property: value;};
}

@import (inline, multiple) "invalid-css.less";
@import "import-inline-invalid-css.less";
.print-referenced-import-inline() {
  div {
    @import (inline, multiple) "invalid-css.less";
  }
  @import (inline, multiple) "invalid-css.less";
}