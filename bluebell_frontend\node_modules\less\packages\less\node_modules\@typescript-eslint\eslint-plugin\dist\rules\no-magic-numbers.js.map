{"version": 3, "file": "no-magic-numbers.js", "sourceRoot": "", "sources": ["../../src/rules/no-magic-numbers.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,8EAG+C;AAC/C,yFAAyD;AACzD,8CAAgC;AAMhC,MAAM,cAAc,GAAI,0BAAQ,CAAC,IAAI,CAAC,MAAwB,CAAC,CAAC,CAAC,CAAC;AAElE,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,kBAAkB;IACxB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,yBAAyB;YACtC,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,iFAAiF;QACjF,MAAM,EAAE;8BAED,cAAc,IACjB,UAAU,oBACL,cAAc,CAAC,UAAU,IAC5B,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;qBAChB,EACD,WAAW,EAAE;wBACX,IAAI,EAAE,SAAS;qBAChB;SAGN;QACD,QAAQ,EAAE,0BAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE;QACd;YACE,MAAM,EAAE,EAAE;YACV,kBAAkB,EAAE,KAAK;YACzB,YAAY,EAAE,KAAK;YACnB,aAAa,EAAE,KAAK;YACpB,yBAAyB,EAAE,KAAK;YAChC,WAAW,EAAE,KAAK;SACnB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,KAAK,GAAG,0BAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QAEvC;;;;WAIG;QACH,SAAS,QAAQ,CAAC,IAAsB;YACtC,OAAO,OAAO,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC;QACxC,CAAC;QAED;;;;;WAKG;QACH,SAAS,mCAAmC,CAAC,IAAmB;YAC9D,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM;gBACtC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,sBAAsB;gBACnE,CAAC,CAAC,KAAK,CAAC;QACZ,CAAC;QAED;;;;;WAKG;QACH,SAAS,wBAAwB,CAAC,IAAmB;YACnD,IACE,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM,CAAC,MAAM;gBAClB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,WAAW,EACtD;gBACA,OAAO,mCAAmC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;aACzD;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;;WAKG;QACH,SAAS,yBAAyB,CAAC,IAAmB;YACpD,OAAO,CACL,OAAO,IAAI,CAAC,MAAM,KAAK,WAAW;gBAClC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,YAAY,CACjD,CAAC;QACJ,CAAC;QAED;;;;;WAKG;QACH,SAAS,qBAAqB,CAAC,IAAmB;YAChD,OAAO,IAAI,CAAC,MAAM;gBAChB,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa;gBACnD,CAAC,CAAC,KAAK,CAAC;QACZ,CAAC;QAED;;;;;WAKG;QACH,SAAS,sBAAsB,CAAC,IAAmB;YACjD,+CAA+C;YAC/C,IACE,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,eAAe;gBACnD,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,GAAG,EAC5B;gBACA,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC;aACpB;YAED,0DAA0D;YAC1D,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,EAAE;gBAChC,OAAO,KAAK,CAAC;aACd;YAED,yDAAyD;YACzD,IAAI,mCAAmC,CAAC,IAAI,CAAC,EAAE;gBAC7C,OAAO,IAAI,CAAC;aACb;YAED,0FAA0F;YAC1F,IAAI,wBAAwB,CAAC,IAAI,CAAC,EAAE;gBAClC,OAAO,IAAI,CAAC;aACb;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,OAAO,CAAC,IAAI;gBACV,qDAAqD;gBACrD,IAAI,OAAO,CAAC,WAAW,IAAI,yBAAyB,CAAC,IAAI,CAAC,EAAE;oBAC1D,OAAO;iBACR;gBAED,sDAAsD;gBACtD,IACE,OAAO,CAAC,yBAAyB;oBACjC,QAAQ,CAAC,IAAI,CAAC;oBACd,sBAAsB,CAAC,IAAI,CAAC,EAC5B;oBACA,OAAO;iBACR;gBAED,uCAAuC;gBACvC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACtB,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}