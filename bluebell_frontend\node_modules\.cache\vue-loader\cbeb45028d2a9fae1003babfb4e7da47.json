{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Home.vue?vue&type=style&index=0&id=fae5bece&scoped=true&lang=less", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Home.vue", "mtime": 1600241974000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AA+LA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <!-- <h4 class=\"c-l-title\">热门帖子</h4> -->\n      <div class=\"c-l-header\">\n        <div class=\"new btn-iconfont\"\n        :class=\"{ active: timeOrder }\"\n        @click=\"selectOrder('time')\"\n        >\n          <i class=\"iconfont icon-polygonred\"></i>New\n        </div>\n        <div class=\"top btn-iconfont\"\n         :class=\"{ active: scoreOrder }\"\n         @click=\"selectOrder('score')\"\n        >\n          <i class=\"iconfont icon-top\"></i>Score\n        </div>\n        <button class=\"btn-publish\" @click=\"goPublish\">发表</button>\n      </div>\n      <ul class=\"c-l-list\">\n        <li class=\"c-l-item\"  v-for=\"post in postList\" :key=\"post.id\">\n          <div class=\"post\">\n            <a class=\"vote\">\n              <span class=\"iconfont icon-up\"\n              @click=\"vote(post.id, '1')\"\n              ></span>\n            </a>\n            <span class=\"text\">{{post.vote_num}}</span>\n            <a class=\"vote\">\n              <span class=\"iconfont icon-down\"\n              @click=\"vote(post.id, '-1')\"\n              ></span>\n            </a>\n          </div>\n          <div class=\"l-container\" @click=\"goDetail(post.id)\">\n            <h4 class=\"con-title\">{{post.title}}</h4>\n            <div class=\"con-memo\">\n              <p>{{post.content}}</p>\n            </div>\n            <!-- <div class=\"user-btn\">\n              <span class=\"btn-item\">\n                <i class=\"iconfont icon-comment\"></i>\n                <span>{{post.comments}} comments</span>\n              </span>\n            </div> -->\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div class=\"right\">\n      <div class=\"communities\">\n        <h2 class=\"r-c-title\">今日火热频道排行榜</h2>\n        <ul class=\"r-c-content\">\n          <li class=\"r-c-item\">\n            <span class=\"index\">1</span>\n            <i class=\"icon\"></i>\n            b/coding\n          </li>\n          <li class=\"r-c-item\">\n            <span class=\"index\">2</span>\n            <i class=\"icon\"></i>\n            b/tree_hole\n          </li>\n          <li class=\"r-c-item\">\n            <span class=\"index\">3</span>\n            <i class=\"icon\"></i>\n            b/job\n          </li>\n        </ul>\n        <button class=\"view-all\">查看所有</button>\n      </div>\n      <div class=\"r-trending\">\n        <h2 class=\"r-t-title\">持续热门频道</h2>\n        <ul class=\"rank\">\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/Book</span>\n                <p class=\"info-num\">7.1k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/coding</span>\n                <p class=\"info-num\">3.2k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/job</span>\n                <p class=\"info-num\">2.5k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n// @ is an alias to /src\n\nexport default {\n  name: \"Home\",\n  components: {},\n  data() {\n    return {\n      order: \"time\",\n      page: 1,\n      postList: []\n    };\n  },\n  methods: {\n    selectOrder(order){\n      this.order = order;\n      this.getPostList()\n    },\n    goPublish(){\n      this.$router.push({ name: \"Publish\" });\n    },\n    goDetail(id){\n      this.$router.push({ name: \"Content\", params: { id: id }});\n    },\n    getPostList() {\n      this.$axios({\n        method: \"get\",\n        url: \"/posts2\",\n        params: {\n          page: this.page,\n          order: this.order,\n        }\n      })\n        .then(response => {\n          console.log(response.data, 222);\n          if (response.code == 1000) {\n            this.postList = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    vote(post_id, direction){\n      this.$axios({\n        method: \"post\",\n        url: \"/vote\",\n        data: JSON.stringify({\n          post_id: post_id,\n          direction: direction,\n        })\n      })\n        .then(response => {\n          if (response.code == 1000) {\n            console.log(\"vote success\");\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    }\n  },\n  mounted: function() {\n    this.getPostList();\n  },\n  computed:{\n    timeOrder(){\n      return this.order == \"time\";\n    },\n    scoreOrder(){\n      return this.order == \"score\";\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 48px auto 0;\n  padding: 20px 24px;\n  .left {\n    width: 640px;\n    padding-bottom: 10px;\n    .c-l-title {\n      font-size: 14px;\n      font-weight: 500;\n      line-height: 18px;\n      color: #1a1a1b;\n      text-transform: unset;\n      padding-bottom: 10px;\n    }\n    .c-l-header {\n      align-items: center;\n      background-color: #ffffff;\n      border: 1px solid #ccc;\n      border-radius: 4px;\n      box-sizing: border-box;\n      display: -ms-flexbox;\n      display: flex;\n      -ms-flex-flow: row nowrap;\n      flex-flow: row nowrap;\n      height: 56px;\n      -ms-flex-pack: start;\n      justify-content: flex-start;\n      margin-bottom: 16px;\n      padding: 0 12px;\n      .iconfont {\n        margin-right: 4px;\n      }\n      .btn-iconfont {\n        display: flex;\n        display: -webkit-flex;\n      }\n      .active {\n        background: #f6f7f8;\n        color: #0079d3;\n        fill: #0079d3;\n        border-radius: 20px;\n        height: 32px;\n        line-height: 32px;\n        margin-right: 8px;\n        padding: 0 10px;\n      }\n      .new {\n        font-size: 14px;\n        margin-right: 18px;\n      }\n      .top {\n        font-size: 14px;\n      }\n      .btn-publish {\n        width: 64px;\n        height: 32px;\n        line-height: 32px;\n        background-color: #54b351;\n        color: #ffffff;\n        border: 1px solid transparent;\n        border-radius: 4px;\n        box-sizing: border-box;\n        text-align: center;\n        margin-left: auto;\n        cursor: pointer;\n      }\n      .sort {\n        margin-left: 300px;\n        display: flex;\n        color: #0079d3;\n        display: -webkit-flex;\n        align-items: center;\n        .sort-triangle {\n          width: 0;\n          height: 0;\n          border-top: 5px solid #0079d3;\n          border-right: 5px solid transparent;\n          border-bottom: 5px solid transparent;\n          border-left: 5px solid transparent;\n          margin-top: 5px;\n          margin-left: 10px;\n        }\n      }\n    }\n    .c-l-list {\n      .c-l-item {\n        list-style: none;\n        border-radius: 4px;\n        padding-left: 40px;\n        cursor: pointer;\n        border: 1px solid #ccc;\n        margin-bottom: 10px;\n        background-color: rgba(255, 255, 255, 0.8);\n        color: #878a8c;\n        position: relative;\n        .post {\n          align-items: center;\n          box-sizing: border-box;\n          display: -ms-flexbox;\n          display: flex;\n          -ms-flex-direction: column;\n          flex-direction: column;\n          height: 100%;\n          left: 0;\n          padding: 8px 4px 8px 0;\n          position: absolute;\n          top: 0;\n          width: 40px;\n          border-left: 4px solid transparent;\n          background: #f8f9fa;\n          .iconfont {\n            margin-right: 0;\n          }\n          .down {\n            transform: scaleY(-1);\n          }\n          .text {\n            color: #1a1a1b;\n            font-size: 12px;\n            font-weight: 700;\n            line-height: 16px;\n            pointer-events: none;\n            word-break: normal;\n          }\n        }\n        .l-container {\n          padding: 15px;\n          .con-title {\n            color: #000000;\n            font-size: 18px;\n            font-weight: 500;\n            line-height: 22px;\n            text-decoration: none;\n            word-break: break-word;\n          }\n          .con-memo {\n            margin-top: 10px;\n            margin-bottom: 10px;\n          }\n          .con-cover {\n            height: 512px;\n            width: 100%;\n            background: url(\"https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1585999647247&di=7e9061211c23e3ed9f0c4375bb3822dc&imgtype=0&src=http%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F04d8cda08e170f4a58c18c45a93c539375c22162.jpg\")\n              no-repeat;\n            background-size: cover;\n            margin-top: 10px;\n            margin-bottom: 10px;\n          }\n          .user-btn {\n            font-size: 14px;\n            display: flex;\n            display: -webkit-flex;\n            .btn-item {\n              display: flex;\n              display: -webkit-flex;\n              margin-right: 10px;\n              .iconfont {\n                margin-right: 4px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  .right {\n    width: 312px;\n    margin-left: 24px;\n    margin-top: 28px;\n    .communities {\n      background-color: #ffffff;\n      color: #1a1a1b;\n      border: 1px solid #ccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      margin-bottom: 20px;\n      .r-c-title {\n        background-image: linear-gradient(\n          0deg,\n          rgba(0, 0, 0, 0.3) 0,\n          transparent\n        );\n        background-color: #0079d3;\n        height: 80px;\n        width: 100%;\n        color: #fff;\n        font-size: 20px;\n        line-height: 120px;\n        padding-left: 10px;\n        box-sizing: border-box;\n        text-align: center;\n      }\n      .r-c-content {\n        .r-c-item {\n          align-items: center;\n          display: flex;\n          display: -webkit-flex;\n          height: 48px;\n          padding: 0 12px;\n          border-bottom: thin solid #edeff1;\n          font-size: 14px;\n          .index {\n            width: 20px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 500;\n            line-height: 18px;\n          }\n          .icon {\n            width: 32px;\n            height: 32px;\n            background-image: url(\"../assets/images/avatar.png\");\n            background-repeat: no-repeat;\n            background-size: cover;\n            margin-right: 20px;\n          }\n          &:last-child {\n            border-bottom: none;\n          }\n        }\n      }\n      .view-all {\n        background-color: #0079d3;\n        border: 1px solid transparent;\n        border-radius: 4px;\n        box-sizing: border-box;\n        text-align: center;\n        letter-spacing: 1px;\n        text-decoration: none;\n        font-size: 12px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 24px;\n        text-transform: uppercase;\n        padding: 3px 0;\n        width: 280px;\n        color: #fff;\n        margin: 20px 0 20px 16px;\n      }\n    }\n    .r-trending {\n      padding-top: 16px;\n      width: 312px;\n      background-color: #ffffff;\n      color: #1a1a1b;\n      fill: #1a1a1b;\n      border: 1px solid #cccccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      .r-t-title {\n        font-size: 10px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 12px;\n        text-transform: uppercase;\n        background-color: #ffffff;\n        border-radius: 3px 3px 0 0;\n        color: #1a1a1b;\n        display: -ms-flexbox;\n        display: flex;\n        fill: #1a1a1b;\n        padding: 0 12px 12px;\n      }\n      .rank {\n        padding: 12px;\n        .r-t-cell {\n          display: flex;\n          display: -webkit-flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 16px;\n          .r-t-cell-info {\n            display: flex;\n          }\n          .avatar {\n            width: 32px;\n            height: 32px;\n            background: url(\"../assets/images/avatar.png\") no-repeat;\n            background-size: cover;\n            margin-right: 10px;\n          }\n          .info {\n            margin-right: 10px;\n            .info-title {\n              font-size: 12px;\n              font-weight: 500;\n              line-height: 16px;\n              text-overflow: ellipsis;\n              width: 144px;\n            }\n            .info-num {\n              font-size: 12px;\n              font-weight: 400;\n              line-height: 16px;\n              padding-bottom: 4px;\n            }\n          }\n          .join-btn {\n            width: 106px;\n            height: 32px;\n            line-height: 32px;\n            background-color: #0079d3;\n            color: #ffffff;\n            border: 1px solid transparent;\n            border-radius: 4px;\n            box-sizing: border-box;\n            text-align: center;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}