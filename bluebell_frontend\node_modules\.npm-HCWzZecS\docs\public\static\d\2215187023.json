{"data": {"allMarkdownRemark": {"nodes": [{"fields": {"slug": "/using-npm/semver"}, "frontmatter": {"description": null, "section": null, "title": ""}}, {"fields": {"slug": "/cli-commands/npm-adduser"}, "frontmatter": {"description": null, "section": null, "title": ""}}, {"fields": {"slug": "/cli-commands/npm-dist-tag"}, "frontmatter": {"description": null, "section": null, "title": ""}}, {"fields": {"slug": "/cli-commands/npm-explore"}, "frontmatter": {"description": null, "section": null, "title": ""}}, {"fields": {"slug": "/using-npm/config"}, "frontmatter": {"description": "More than you probably want to know about npm configuration", "section": "using-npm", "title": "config"}}, {"fields": {"slug": "/using-npm/developers"}, "frontmatter": {"description": "Developer Guide", "section": "using-npm", "title": "developers"}}, {"fields": {"slug": "/using-npm/disputes"}, "frontmatter": {"description": "Handling Module Name Disputes", "section": "using-npm", "title": "disputes"}}, {"fields": {"slug": "/configuring-npm/folders"}, "frontmatter": {"description": "Folder Structures Used by npm", "section": "configuring-npm", "title": "folders"}}, {"fields": {"slug": "/configuring-npm/install"}, "frontmatter": {"description": "Download and install node and npm", "section": "configuring-npm", "title": "install"}}, {"fields": {"slug": "/cli-commands/npm"}, "frontmatter": {"description": "javascript package manager", "section": "cli-commands", "title": "npm"}}, {"fields": {"slug": "/cli-commands/npm-access"}, "frontmatter": {"description": "Set access level on published packages", "section": "cli-commands", "title": "npm-access"}}, {"fields": {"slug": "/cli-commands/npm-audit"}, "frontmatter": {"description": "Run a security audit", "section": "cli-commands", "title": "npm-audit"}}, {"fields": {"slug": "/cli-commands/npm-bin"}, "frontmatter": {"description": "Display npm bin folder", "section": "cli-commands", "title": "npm-bin"}}, {"fields": {"slug": "/cli-commands/npm-bugs"}, "frontmatter": {"description": "Bugs for a package in a web browser maybe", "section": "cli-commands", "title": "npm-bugs"}}, {"fields": {"slug": "/cli-commands/npm-build"}, "frontmatter": {"description": "Build a package", "section": "cli-commands", "title": "npm-build"}}, {"fields": {"slug": "/cli-commands/npm-bundle"}, "frontmatter": {"description": "REMOVED", "section": "cli-commands", "title": "npm-bundle"}}, {"fields": {"slug": "/cli-commands/npm-cache"}, "frontmatter": {"description": "Manipulates packages cache", "section": "cli-commands", "title": "npm-cache"}}, {"fields": {"slug": "/cli-commands/npm-ci"}, "frontmatter": {"description": "Install a project with a clean slate", "section": "cli-commands", "title": "npm-ci"}}, {"fields": {"slug": "/cli-commands/npm-completion"}, "frontmatter": {"description": "Tab Completion for npm", "section": "cli-commands", "title": "npm-completion"}}, {"fields": {"slug": "/cli-commands/npm-config"}, "frontmatter": {"description": "Manage the npm configuration files", "section": "cli-commands", "title": "npm-config"}}, {"fields": {"slug": "/cli-commands/npm-dedupe"}, "frontmatter": {"description": "Reduce duplication", "section": "cli-commands", "title": "npm-dedupe"}}, {"fields": {"slug": "/cli-commands/npm-deprecate"}, "frontmatter": {"description": "Deprecate a version of a package", "section": "cli-commands", "title": "npm-deprecate"}}, {"fields": {"slug": "/cli-commands/npm-docs"}, "frontmatter": {"description": "Docs for a package in a web browser maybe", "section": "cli-commands", "title": "npm-docs"}}, {"fields": {"slug": "/cli-commands/npm-doctor"}, "frontmatter": {"description": "Check your environments", "section": "cli-commands", "title": "npm-doctor"}}, {"fields": {"slug": "/cli-commands/npm-edit"}, "frontmatter": {"description": "Edit an installed package", "section": "cli-commands", "title": "npm-edit"}}, {"fields": {"slug": "/cli-commands/npm-fund"}, "frontmatter": {"description": "Retrieve funding information", "section": "cli-commands", "title": "npm-fund"}}, {"fields": {"slug": "/cli-commands/npm-help"}, "frontmatter": {"description": "Get help on npm", "section": "cli-commands", "title": "npm-help"}}, {"fields": {"slug": "/cli-commands/npm-help-search"}, "frontmatter": {"description": "Search npm help documentation", "section": "cli-commands", "title": "npm-help-search"}}, {"fields": {"slug": "/cli-commands/npm-hook"}, "frontmatter": {"description": "Manage registry hooks", "section": "cli-commands", "title": "npm-hook"}}, {"fields": {"slug": "/cli-commands/npm-init"}, "frontmatter": {"description": "create a package.json file", "section": "cli-commands", "title": "npm-init"}}, {"fields": {"slug": "/cli-commands/npm-install"}, "frontmatter": {"description": "Install a package", "section": "cli-commands", "title": "npm-install"}}, {"fields": {"slug": "/cli-commands/npm-install-ci-test"}, "frontmatter": {"description": "Install a project with a clean slate and run tests", "section": "cli-commands", "title": "npm-install-ci-test"}}, {"fields": {"slug": "/cli-commands/npm-install-test"}, "frontmatter": {"description": "Install package(s) and run tests", "section": "cli-commands", "title": "npm-install-test"}}, {"fields": {"slug": "/cli-commands/npm-link"}, "frontmatter": {"description": "Symlink a package folder", "section": "cli-commands", "title": "npm-link"}}, {"fields": {"slug": "/cli-commands/npm-logout"}, "frontmatter": {"description": "Log out of the registry", "section": "cli-commands", "title": "npm-logout"}}, {"fields": {"slug": "/cli-commands/npm-ls"}, "frontmatter": {"description": "List installed packages", "section": "cli-commands", "title": "npm-ls"}}, {"fields": {"slug": "/cli-commands/npm-org"}, "frontmatter": {"description": "Manage orgs", "section": "cli-commands", "title": "npm-org"}}, {"fields": {"slug": "/cli-commands/npm-outdated"}, "frontmatter": {"description": "Check for outdated packages", "section": "cli-commands", "title": "npm-outdated"}}, {"fields": {"slug": "/cli-commands/npm-owner"}, "frontmatter": {"description": "Manage package owners", "section": "cli-commands", "title": "npm-owner"}}, {"fields": {"slug": "/cli-commands/npm-pack"}, "frontmatter": {"description": "Create a tarball from a package", "section": "cli-commands", "title": "npm-pack"}}, {"fields": {"slug": "/cli-commands/npm-ping"}, "frontmatter": {"description": "Ping npm registry", "section": "cli-commands", "title": "npm-ping"}}, {"fields": {"slug": "/cli-commands/npm-prefix"}, "frontmatter": {"description": "Display prefix", "section": "cli-commands", "title": "npm-prefix"}}, {"fields": {"slug": "/cli-commands/npm-profile"}, "frontmatter": {"description": "Change settings on your registry profile", "section": "cli-commands", "title": "npm-profile"}}, {"fields": {"slug": "/cli-commands/npm-prune"}, "frontmatter": {"description": "Remove extraneous packages", "section": "cli-commands", "title": "npm-prune"}}, {"fields": {"slug": "/cli-commands/npm-publish"}, "frontmatter": {"description": "Publish a package", "section": "cli-commands", "title": "npm-publish"}}, {"fields": {"slug": "/cli-commands/npm-rebuild"}, "frontmatter": {"description": "Rebuild a package", "section": "cli-commands", "title": "npm-rebuild"}}, {"fields": {"slug": "/cli-commands/npm-repo"}, "frontmatter": {"description": "Open package repository page in the browser", "section": "cli-commands", "title": "npm-repo"}}, {"fields": {"slug": "/cli-commands/npm-restart"}, "frontmatter": {"description": "Restart a package", "section": "cli-commands", "title": "npm-restart"}}, {"fields": {"slug": "/cli-commands/npm-root"}, "frontmatter": {"description": "Display npm root", "section": "cli-commands", "title": "npm-root"}}, {"fields": {"slug": "/cli-commands/npm-run-script"}, "frontmatter": {"description": "Run arbitrary package scripts", "section": "cli-commands", "title": "npm-run-script"}}, {"fields": {"slug": "/cli-commands/npm-search"}, "frontmatter": {"description": "Search for packages", "section": "cli-commands", "title": "npm-search"}}, {"fields": {"slug": "/cli-commands/npm-shrinkwrap"}, "frontmatter": {"description": "Lock down dependency versions for publication", "section": "cli-commands", "title": "npm-shrinkwrap"}}, {"fields": {"slug": "/cli-commands/npm-star"}, "frontmatter": {"description": "Mark your favorite packages", "section": "cli-commands", "title": "npm-star"}}, {"fields": {"slug": "/cli-commands/npm-stars"}, "frontmatter": {"description": "View packages marked as favorites", "section": "cli-commands", "title": "npm-stars"}}, {"fields": {"slug": "/cli-commands/npm-start"}, "frontmatter": {"description": "Start a package", "section": "cli-commands", "title": "npm-start"}}, {"fields": {"slug": "/cli-commands/npm-stop"}, "frontmatter": {"description": "Stop a package", "section": "cli-commands", "title": "npm-stop"}}, {"fields": {"slug": "/cli-commands/npm-team"}, "frontmatter": {"description": "Manage organization teams and team memberships", "section": "cli-commands", "title": "npm-team"}}, {"fields": {"slug": "/cli-commands/npm-test"}, "frontmatter": {"description": "Test a package", "section": "cli-commands", "title": "npm-test"}}, {"fields": {"slug": "/cli-commands/npm-token"}, "frontmatter": {"description": "Manage your authentication tokens", "section": "cli-commands", "title": "npm-token"}}, {"fields": {"slug": "/cli-commands/npm-uninstall"}, "frontmatter": {"description": "Remove a package", "section": "cli-commands", "title": "npm-uninstall"}}, {"fields": {"slug": "/cli-commands/npm-unpublish"}, "frontmatter": {"description": "Remove a package from the registry", "section": "cli-commands", "title": "npm-unpublish"}}, {"fields": {"slug": "/cli-commands/npm-update"}, "frontmatter": {"description": "Update a package", "section": "cli-commands", "title": "npm-update"}}, {"fields": {"slug": "/cli-commands/npm-version"}, "frontmatter": {"description": "Bump a package version", "section": "cli-commands", "title": "npm-version"}}, {"fields": {"slug": "/cli-commands/npm-view"}, "frontmatter": {"description": "View registry info", "section": "cli-commands", "title": "npm-view"}}, {"fields": {"slug": "/cli-commands/npm-whoami"}, "frontmatter": {"description": "Display npm username", "section": "cli-commands", "title": "npm-whoami"}}, {"fields": {"slug": "/configuring-npm/npmrc"}, "frontmatter": {"description": "The npm config files", "section": "configuring-npm", "title": "npmrc"}}, {"fields": {"slug": "/using-npm/orgs"}, "frontmatter": {"description": "Working with Teams & Orgs", "section": "using-npm", "title": "orgs"}}, {"fields": {"slug": "/configuring-npm/package-lock-json"}, "frontmatter": {"description": "A manifestation of the manifest", "section": "configuring-npm", "title": "package-lock.json"}}, {"fields": {"slug": "/configuring-npm/package-locks"}, "frontmatter": {"description": "An explanation of npm lockfiles", "section": "configuring-npm", "title": "package-locks"}}, {"fields": {"slug": "/configuring-npm/package-json"}, "frontmatter": {"description": "Specifics of npm's package.json handling", "section": "configuring-npm", "title": "package.json"}}, {"fields": {"slug": "/using-npm/registry"}, "frontmatter": {"description": "The JavaScript Package Registry", "section": "using-npm", "title": "registry"}}, {"fields": {"slug": "/using-npm/removal"}, "frontmatter": {"description": "Cleaning the Slate", "section": "using-npm", "title": "removal"}}, {"fields": {"slug": "/using-npm/scope"}, "frontmatter": {"description": "Scoped packages", "section": "using-npm", "title": "scope"}}, {"fields": {"slug": "/using-npm/scripts"}, "frontmatter": {"description": "How npm handles the \"scripts\" field", "section": "using-npm", "title": "scripts"}}, {"fields": {"slug": "/configuring-npm/shrinkwrap-json"}, "frontmatter": {"description": "A publishable lockfile", "section": "configuring-npm", "title": "shrinkwrap.json"}}]}}}