{"version": 3, "file": "no-object-literal-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-object-literal-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,kCAAkC;IACxC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,oEAAoE;YACtE,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,OAAO;SACrB;QACD,QAAQ,EAAE;YACR,uBAAuB,EACrB,gFAAgF;SACnF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;SACxB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,gBAAgB,EAAE,CAAC;QACpC;;;WAGG;QACH,SAAS,SAAS,CAAC,IAAuB;YACxC,QAAQ,IAAI,CAAC,IAAI,EAAE;gBACjB,KAAK,mCAAc,CAAC,YAAY,CAAC;gBACjC,KAAK,mCAAc,CAAC,gBAAgB;oBAClC,OAAO,KAAK,CAAC;gBACf,KAAK,mCAAc,CAAC,eAAe;oBACjC,yCAAyC;oBACzC,OAAO,CACL,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,mCAAc,CAAC,UAAU;wBAChD,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,OAAO,CAC/B,CAAC;gBACJ;oBACE,OAAO,IAAI,CAAC;aACf;QACH,CAAC;QAED,OAAO;YACL,iCAAiC,CAC/B,IAAwD;gBAExD,IACE,gBAAgB;oBAChB,IAAI,CAAC,MAAM;oBACX,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa;wBAChD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc,CAAC,EACrD;oBACA,OAAO;iBACR;gBAED,IACE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC;oBAC9B,IAAI,CAAC,UAAU,CAAC,IAAI,KAAK,mCAAc,CAAC,gBAAgB,EACxD;oBACA,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,yBAAyB;qBACrC,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}