<svg id="Layer_1" data-name="Layer 1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 31.48 26.49"><defs><style>.cls-1{fill:url(#linear-gradient);}.cls-2{fill:url(#linear-gradient-2);}</style><linearGradient id="linear-gradient" x1="-0.52" y1="12.65" x2="34.48" y2="12.65" gradientTransform="translate(28.39 -3.74) rotate(90)" gradientUnits="userSpaceOnUse"><stop offset="0" stop-color="#fb8817"/><stop offset="1" stop-color="#e02aff"/></linearGradient><linearGradient id="linear-gradient-2" x1="18.41" y1="36.61" x2="53.41" y2="36.61" gradientTransform="matrix(1, 0, 0, -1, -20.17, 49.85)" xlink:href="#linear-gradient"/></defs><title>hamburger-close</title><rect class="cls-1" x="13.74" y="-4.25" width="4" height="35" rx="2" ry="2" transform="translate(-4.78 14.75) rotate(-44.28)"/><rect class="cls-2" x="-1.76" y="11.25" width="35" height="4" rx="2" ry="2" transform="translate(-4.79 14.51) rotate(-43.62)"/></svg>