@map: {
  @width: 400px;
  @colors: {
    toolbar-background: red;
    toolbar-foreground: inherit;
  }
};

#ns {
  .mixin() {
    @height: 200px;
  }
}

@breakpoints: {
  mobile: 320px;
  tablet: 768px;
  desktop: 1024px;
};

@media (min-width: @breakpoints[mobile]) {
  .toolbar {
    width: @map[@width];
    height: #ns.mixin[@height];
    background: @map[@colors][toolbar-background];
    color: @map[@colors][toolbar-foreground];
  }
}

// !important after map usage
// https://github.com/less/less.js/issues/3430
@margins: {
    zero: 0;
    ten: 10px;
}
.cell {
  margin: @margins[zero] @margins[ten]/2 !important;
}

.mixin(@color: black; @margin: 10px; @padding: 20px) {
  color: @color;
  margin: @margin;
  padding: @padding;
  width: @margins[zero] !important
}
.class1 {
  .mixin(@margin: 20px; @color: #33acfe) !important;
}
.class2 {
  .mixin(#efca44; @padding: 40px 10px);
}
