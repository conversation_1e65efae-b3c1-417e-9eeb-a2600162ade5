{"name": "@typescript-eslint/eslint-plugin", "version": "1.13.0", "description": "TypeScript plugin for ESLint", "keywords": ["eslint", "eslintplugin", "eslint-plugin", "typescript"], "engines": {"node": "^6.14.0 || ^8.10.0 || >=9.10.0"}, "files": ["dist", "docs", "package.json", "README.md", "LICENSE"], "repository": {"type": "git", "url": "https://github.com/typescript-eslint/typescript-eslint.git", "directory": "packages/eslint-plugin"}, "bugs": {"url": "https://github.com/typescript-eslint/typescript-eslint/issues"}, "license": "MIT", "main": "dist/index.js", "scripts": {"build": "tsc -p tsconfig.build.json", "check:docs": "ts-node --files ./tools/validate-docs/index.ts", "check:configs": "ts-node --files ./tools/validate-configs/index.ts", "clean": "rimraf dist/", "format": "prettier --write \"./**/*.{ts,js,json,md}\" --ignore-path ../../.prettierignore", "generate:configs": "ts-node --files tools/generate-configs.ts", "prebuild": "npm run clean", "test": "jest --coverage", "typecheck": "tsc --noEmit"}, "dependencies": {"@typescript-eslint/experimental-utils": "1.13.0", "eslint-utils": "^1.3.1", "functional-red-black-tree": "^1.0.1", "regexpp": "^2.0.1", "tsutils": "^3.7.0"}, "devDependencies": {"@types/json-schema": "^7.0.3", "@types/marked": "^0.6.5", "chalk": "^2.4.2", "marked": "^0.6.2"}, "peerDependencies": {"@typescript-eslint/parser": "^1.9.0", "eslint": "^5.0.0"}, "gitHead": "c367b34abd8c58eddd2c15685ed8c17b983f0da1"}