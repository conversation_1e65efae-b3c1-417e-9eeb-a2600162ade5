{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Content.vue?vue&type=style&index=0&id=76cda6af&lang=less&scoped=true", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Content.vue", "mtime": 1598770386000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Content.vue"], "names": [], "mappings": ";AAuGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Content.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <div class=\"container\">\n        <div class=\"post\">\n          <a class=\"vote\">\n            <span class=\"iconfont icon-up\"></span>\n          </a>\n          <span class=\"text\">50.2k</span>\n          <a class=\"vote\">\n            <span class=\"iconfont icon-down\"></span>\n          </a>\n        </div>\n        <div class=\"l-container\">\n          <h4 class=\"con-title\">{{post.title}}</h4>\n          <div class=\"con-info\">{{post.content}}</div>\n          <div class=\"user-btn\">\n            <span class=\"btn-item\">\n              <i class=\"iconfont icon-comment\"></i>comment\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- <div class=\"comment\">\n        <div class=\"c-left\">\n          <div class=\"line\"></div>\n          <div class=\"c-arrow\">\n                            <a class=\"vote\"><span class=\"iconfont icon-up\"></span></a>\n                            <a class=\"up down\"></a>\n          </div>\n        </div>\n        <div class=\"c-right\">\n          <div class=\"c-user-info\">\n            <span class=\"name\">mahlerific</span>\n            <span class=\"num\">1.4k points</span>\n            <span class=\"num\">· 5 hours ago</span>\n          </div>\n          <p\n            class=\"c-content\"\n          >We're having the same experience in Yerevan, Armenia. Though you can see mountains all around the city on good days, now you can see even farther into Turkey and Iran. Every crag on the mountains around us is now clearer than ever.</p>\n        </div>\n      </div> -->\n    </div>\n    <div class=\"right\">\n      <div class=\"topic-info\">\n        <h5 class=\"t-header\"></h5>\n        <div class=\"t-info\">\n          <a class=\"avatar\"></a>\n          <span class=\"topic-name\">b/{{post.community_name}}</span>\n        </div>\n        <p class=\"t-desc\">树洞 树洞 无限树洞的树洞</p>\n        <ul class=\"t-num\">\n          <li class=\"t-num-item\">\n            <p class=\"number\">5.2m</p>\n            <span class=\"unit\">Members</span>\n          </li>\n          <li class=\"t-num-item\">\n            <p class=\"number\">5.2m</p>\n            <span class=\"unit\">Members</span>\n          </li>\n        </ul>\n        <div class=\"date\">Created Apr 10, 2008</div>\n        <button class=\"topic-btn\">JOIN</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Content\",\n  data(){\n    return {\n      post:{},\n    }\n  },\n  methods:{\n    getPostDetail() {\n      this.$axios({\n        method: \"get\",\n        url: \"/post/\"+ this.$route.params.id,\n      })\n        .then(response => {\n          console.log(1, response.data);\n          if (response.code == 1000) {\n            this.post = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n  },\n  mounted: function() {\n    this.getPostDetail();\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 0 auto;\n  padding: 20px 24px;\n  margin-top: 48px;\n  .left {\n    flex-grow: 1;\n    max-width: 740px;\n    border-radius: 4px;\n    word-break: break-word;\n    background: #ffffff;\n    border: #edeff1;\n    flex: 1;\n    margin: 32px;\n    margin-right: 12px;\n    padding-bottom: 30px;\n    position: relative;\n    .container {\n      width: 100%;\n      height: auto;\n      position: relative;\n      .post {\n        align-items: center;\n        box-sizing: border-box;\n        display: -ms-flexbox;\n        display: flex;\n        -ms-flex-direction: column;\n        flex-direction: column;\n        height: 100%;\n        left: 0;\n        padding: 8px 4px 8px 0;\n        position: absolute;\n        top: 0;\n        width: 40px;\n        border-left: 4px solid transparent;\n        // background: #f8f9fa;\n        .text {\n          color: #1a1a1b;\n          font-size: 12px;\n          font-weight: 700;\n          line-height: 16px;\n          pointer-events: none;\n          word-break: normal;\n        }\n      }\n      .l-container {\n        padding: 15px;\n        margin-left: 40px;\n        .con-title {\n          color: #000000;\n          font-size: 18px;\n          font-weight: 500;\n          line-height: 22px;\n          text-decoration: none;\n          word-break: break-word;\n        }\n        .con-info{\n          margin: 25px 0;\n          padding: 15px 0;\n          border-bottom: 1px solid grey;\n        }\n        .con-cover {\n          height: 512px;\n          width: 100%;\n          background: url(\"https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1585999647247&di=7e9061211c23e3ed9f0c4375bb3822dc&imgtype=0&src=http%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F04d8cda08e170f4a58c18c45a93c539375c22162.jpg\")\n            no-repeat;\n          background-size: cover;\n          margin-top: 10px;\n          margin-bottom: 10px;\n        }\n        .user-btn {\n          font-size: 12px;\n          display: flex;\n          display: -webkit-flex;\n          .btn-item {\n            display: flex;\n            display: -webkit-flex;\n            align-items: center;\n            margin-right: 10px;\n            .iconfont{\n              margin-right: 4px;\n            }\n          }\n        }\n      }\n    }\n    .comment {\n      width: 100%;\n      height: auto;\n      position: relative;\n      .c-left {\n        .line {\n          border-right: 2px solid #edeff1;\n          // width: 20px;\n          height: 100%;\n          position: absolute;\n          left: 20px;\n        }\n        .c-arrow {\n          display: flex;\n          display: -webkit-flex;\n          position: absolute;\n          z-index: 2;\n          flex-direction: column;\n          left: 12px;\n          background: #ffffff;\n          padding-bottom: 5px;\n        }\n      }\n      .c-right {\n        margin-left: 40px;\n        padding-right: 10px;\n        .c-user-info {\n          margin-bottom: 10px;\n          .name {\n            color: #1c1c1c;\n            font-size: 12px;\n            font-weight: 400;\n            line-height: 16px;\n          }\n          .num {\n            padding-left: 4px;\n            font-size: 12px;\n            font-weight: 400;\n            line-height: 16px;\n            color: #7c7c7c;\n          }\n        }\n        .c-content {\n          font-family: Noto Sans, Arial, sans-serif;\n          font-size: 14px;\n          font-weight: 400;\n          line-height: 21px;\n          word-break: break-word;\n          color: rgb(26, 26, 27);\n        }\n      }\n    }\n  }\n  .right {\n    flex-grow: 0;\n    width: 312px;\n    margin-top: 32px;\n    .topic-info {\n      width: 100%;\n      // padding: 12px;\n      cursor: pointer;\n      background-color: #ffffff;\n      color: #1a1a1b;\n      border: 1px solid #cccccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      padding-bottom: 30px;\n      .t-header {\n        width: 100%;\n        height: 34px;\n        background: #0079d3;\n      }\n      .t-info {\n        padding: 0 12px;\n        display: flex;\n        display: -webkit-flex;\n        width: 100%;\n        height: 54px;\n        align-items: center;\n        .avatar {\n          width: 54px;\n          height: 54px;\n          background: url(\"../assets/images/avatar.png\") no-repeat;\n          background-size: cover;\n          margin-right: 10px;\n        }\n        .topic-name {\n          height: 100%;\n          line-height: 54px;\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n      .t-desc {\n        font-family: Noto Sans, Arial, sans-serif;\n        font-size: 14px;\n        line-height: 21px;\n        font-weight: 400;\n        word-wrap: break-word;\n        margin-bottom: 8px;\n        padding: 0 12px;\n      }\n      .t-num {\n        padding: 0 12px 20px 12px;\n        display: flex;\n        display: -webkit-flex;\n        align-items: center;\n        border-bottom: 1px solid #edeff1;\n        .t-num-item {\n          list-style: none;\n          display: flex;\n          display: -webkit-flex;\n          flex-direction: column;\n          width: 50%;\n          .number {\n            font-size: 16px;\n            font-weight: 500;\n            line-height: 20px;\n          }\n          .unit {\n            font-size: 12px;\n            font-weight: 500;\n            line-height: 16px;\n            word-break: break-word;\n          }\n        }\n      }\n      .date {\n        font-family: Noto Sans, Arial, sans-serif;\n        font-size: 14px;\n        font-weight: 400;\n        line-height: 18px;\n        margin-top: 20px;\n        padding: 0 12px;\n      }\n      .topic-btn {\n        width: 286px;\n        height: 34px;\n        line-height: 34px;\n        color: #ffffff;\n        margin: 12px auto 0 auto;\n        background: #003f6d;\n        border-radius: 4px;\n        box-sizing: border-box;\n        margin-left: 13px;\n      }\n    }\n  }\n}\n</style>"]}]}