{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\components\\HeadBar.vue?vue&type=style&index=0&id=f61a4ada&scoped=true&lang=less", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\components\\HeadBar.vue", "mtime": 1596349902000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5oZWFkZXIgewogIHdpZHRoOiAxMDAlOwogIGhlaWdodDogNDhweDsKICBwb3NpdGlvbjogZml4ZWQ7CiAgYmFja2dyb3VuZDogI2ZmZmZmZjsKICBkaXNwbGF5OiBmbGV4OwogIGRpc3BsYXk6IC13ZWJraXQtZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHRvcDogMDsKICB6LWluZGV4OiAxOwogIC5sb2dvIHsKICAgIG1hcmdpbi1sZWZ0OiAxMHB4OwogICAgaGVpZ2h0OiAzMnB4OwogICAgYmFja2dyb3VuZDogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL2xvZ28ucG5nIikgbm8tcmVwZWF0OwogICAgYmFja2dyb3VuZC1zaXplOiAzMnB4IDMycHg7CiAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBsZWZ0IGNlbnRlcjsKICAgIHBhZGRpbmctbGVmdDogMzVweDsKICAgIGxpbmUtaGVpZ2h0OiAzMnB4OwogICAgZmxleC1ncm93OiAwOwogICAgbWFyZ2luLXJpZ2h0OiAxNnB4OwogICAgY3Vyc29yOiBwb2ludGVyOwogIH0KICAuc2VhcmNoIHsKICAgIGZsZXgtZ3JvdzogMTsKICAgIG1hcmdpbjogMCBhdXRvOwogICAgbWF4LXdpZHRoOiA2OTBweDsKICAgIHBvc2l0aW9uOiByZWxhdGl2ZTsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBkaXNwbGF5OiAtd2Via2l0LWZsZXg7CiAgICAucy1sb2dvIHsKICAgICAgd2lkdGg6IDE4cHg7CiAgICAgIGhlaWdodDogMThweDsKICAgICAgYmFja2dyb3VuZDogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL3NlYXJjaC5wbmciKSBuby1yZXBlYXQ7CiAgICAgIGJhY2tncm91bmQtc2l6ZTogY292ZXI7CiAgICAgIGRpc3BsYXk6IGlubGluZS1ibG9jazsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICB0b3A6IDUwJTsKICAgICAgbWFyZ2luLXRvcDogLTlweDsKICAgICAgbGVmdDogMTVweDsKICAgIH0KICAgIC5zLWlucHV0IHsKICAgICAgZmxleC1ncm93OiAxOwogICAgICAtd2Via2l0LWFwcGVhcmFuY2U6IG5vbmU7CiAgICAgIGFwcGVhcmFuY2U6IG5vbmU7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNmY3Zjg7CiAgICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgICAgYm9yZGVyOiAxcHggc29saWQgI2VkZWZmMTsKICAgICAgYm94LXNoYWRvdzogbm9uZTsKICAgICAgY29sb3I6ICNjMWMxYzE7CiAgICAgIGRpc3BsYXk6IGJsb2NrOwogICAgICBoZWlnaHQ6IDM2cHg7CiAgICAgIG91dGxpbmU6IG5vbmU7CiAgICAgIHBhZGRpbmc6IDAgMTZweCAwIDQwcHg7CiAgICAgIHdpZHRoOiAxMDAlOwogICAgfQogIH0KICAuYnRucyB7CiAgICBmbGV4LWdyb3c6IDA7CiAgICBtYXJnaW4tbGVmdDogMTZweDsKICAgIG1hcmdpbi1yaWdodDogMTBweDsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBkaXNwbGF5OiAtd2Via2l0LWZsZXg7CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgLmxvZ2luLWJ0biB7CiAgICAgIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50OwogICAgICBib3JkZXItcmFkaXVzOiA0cHg7CiAgICAgIGJveC1zaXppbmc6IGJvcmRlci1ib3g7CiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjsKICAgICAgbGV0dGVyLXNwYWNpbmc6IDFweDsKICAgICAgdGV4dC1kZWNvcmF0aW9uOiBub25lOwogICAgICBmb250LXNpemU6IDEycHg7CiAgICAgIGZvbnQtd2VpZ2h0OiA3MDA7CiAgICAgIGxldHRlci1zcGFjaW5nOiAwLjVweDsKICAgICAgbGluZS1oZWlnaHQ6IDI0cHg7CiAgICAgIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7CiAgICAgIHBhZGRpbmc6IDNweCAxNnB4OwogICAgICBib3JkZXItY29sb3I6ICMwMDc5ZDM7CiAgICAgIGNvbG9yOiAjMDA3OWQzOwogICAgICBmaWxsOiAjMDA3OWQzOwogICAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgJjpudGgtY2hpbGQoMSkgewogICAgICAgIG1hcmdpbi1yaWdodDogNXB4OwogICAgICB9CiAgICAgICY6bnRoLWNoaWxkKDIpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDEwcHg7CiAgICAgIH0KICAgIH0KICAgIC51c2VyIHsKICAgICAgd2lkdGg6IGF1dG87CiAgICAgIGhlaWdodDogMjRweDsKICAgICAgYmFja2dyb3VuZDogdXJsKCIuLi9hc3NldHMvaW1hZ2VzL2F2YXRhci5wbmciKSBuby1yZXBlYXQ7CiAgICAgIGJhY2tncm91bmQtc2l6ZTogMjRweCAyNHB4OwogICAgICBiYWNrZ3JvdW5kLXBvc2l0aW9uOiBsZWZ0IGNlbnRlcjsKICAgICAgcGFkZGluZy1sZWZ0OiAyOHB4OwogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBkaXNwbGF5OiAtd2Via2l0LWZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGN1cnNvcjogcG9pbnRlcjsKICAgICAgcGFkZGluZzogMTJweCAxMnB4IDEycHggMjhweDsKICAgICAgJjo6YWZ0ZXIgewogICAgICAgIGNvbnRlbnQ6ICIiOwogICAgICAgIHdpZHRoOiAwOwogICAgICAgIGhlaWdodDogMDsKICAgICAgICBib3JkZXItdG9wOiA1cHggc29saWQgIzg3OGE4YzsKICAgICAgICBib3JkZXItcmlnaHQ6IDVweCBzb2xpZCB0cmFuc3BhcmVudDsKICAgICAgICBib3JkZXItYm90dG9tOiA1cHggc29saWQgdHJhbnNwYXJlbnQ7CiAgICAgICAgYm9yZGVyLWxlZnQ6IDVweCBzb2xpZCB0cmFuc3BhcmVudDsKICAgICAgICBtYXJnaW4tdG9wOiA1cHg7CiAgICAgICAgbWFyZ2luLWxlZnQ6IDEwcHg7CiAgICAgIH0KICAgIH0KICAgIC5kcm9wZG93bi1jb250ZW50IHsKICAgICAgZGlzcGxheTogbm9uZTsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjlmOWY5OwogICAgICBtaW4td2lkdGg6IDE2MHB4OwogICAgICBib3gtc2hhZG93OiAwcHggOHB4IDE2cHggMHB4IHJnYmEoMCwwLDAsMC4yKTsKICAgICAgYSB7CiAgICAgICAgY29sb3I6IGJsYWNrOwogICAgICAgIHBhZGRpbmc6IDEycHggMTZweDsKICAgICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7CiAgICAgICAgZGlzcGxheTogYmxvY2s7CiAgICAgICAgY3Vyc29yOiBwb2ludGVyOwogICAgICB9CiAgICAgIGE6aG92ZXIge2JhY2tncm91bmQtY29sb3I6ICNmMWYxZjF9CiAgICB9CiAgICAudXNlci1ib3g6aG92ZXIgLmRyb3Bkb3duLWNvbnRlbnQgewogICAgICBkaXNwbGF5OiBibG9jazsKICAgIH0KICB9CiAgCn0K"}, {"version": 3, "sources": ["HeadBar.vue"], "names": [], "mappings": ";AAwDA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA", "file": "HeadBar.vue", "sourceRoot": "src/components", "sourcesContent": ["<template>\n  <header class=\"header\">\n      <span class=\"logo\" @click=\"goIndex\">bluebell</span>\n    <div class=\"search\">\n      <label class=\"s-logo\"></label>\n      <input type=\"text\" class=\"s-input\" placeholder=\"搜索\" />\n    </div>\n    <div class=\"btns\">\n      <div v-show=\"!isLogin\">\n        <a class=\"login-btn\" @click=\"goLogin\">登录</a>\n        <a class=\"login-btn\" @click=\"goSignUp\">注册</a>\n      </div>\n      <div class=\"user-box\" v-show=\"isLogin\">\n        <span class=\"user\">{{ currUsername }}</span>\n        <div class=\"dropdown-content\">\n          <a @click=\"goLogout\">登出</a>\n        </div>\n      </div>\n    </div>\n  </header>\n</template>\n\n<script>\nexport default {\n  name: \"HeadBar\",\n  created(){\n    this.$store.commit(\"init\");\n  },\n  computed: {\n    isLogin() {\n      return this.$store.getters.isLogin;\n    },\n    currUsername(){\n      console.log(this.$store.getters.username);\n      return this.$store.getters.username;\n    }\n  },\n  methods: {\n    goIndex(){\n      this.$router.push({ name: \"Home\" });\n    },\n    goLogin() {\n      this.$router.push({ name: \"Login\" });\n    },\n    goSignUp() {\n      this.$router.push({ name: \"SignUp\" });\n    },\n    goLogout(){\n      this.$store.commit(\"logout\");\n    }\n  }\n};\n</script>\n\n<!-- Add \"scoped\" attribute to limit CSS to this component only -->\n<style scoped lang=\"less\">\n.header {\n  width: 100%;\n  height: 48px;\n  position: fixed;\n  background: #ffffff;\n  display: flex;\n  display: -webkit-flex;\n  align-items: center;\n  top: 0;\n  z-index: 1;\n  .logo {\n    margin-left: 10px;\n    height: 32px;\n    background: url(\"../assets/images/logo.png\") no-repeat;\n    background-size: 32px 32px;\n    background-position: left center;\n    padding-left: 35px;\n    line-height: 32px;\n    flex-grow: 0;\n    margin-right: 16px;\n    cursor: pointer;\n  }\n  .search {\n    flex-grow: 1;\n    margin: 0 auto;\n    max-width: 690px;\n    position: relative;\n    display: flex;\n    display: -webkit-flex;\n    .s-logo {\n      width: 18px;\n      height: 18px;\n      background: url(\"../assets/images/search.png\") no-repeat;\n      background-size: cover;\n      display: inline-block;\n      position: absolute;\n      top: 50%;\n      margin-top: -9px;\n      left: 15px;\n    }\n    .s-input {\n      flex-grow: 1;\n      -webkit-appearance: none;\n      appearance: none;\n      background-color: #f6f7f8;\n      border-radius: 4px;\n      border: 1px solid #edeff1;\n      box-shadow: none;\n      color: #c1c1c1;\n      display: block;\n      height: 36px;\n      outline: none;\n      padding: 0 16px 0 40px;\n      width: 100%;\n    }\n  }\n  .btns {\n    flex-grow: 0;\n    margin-left: 16px;\n    margin-right: 10px;\n    display: flex;\n    display: -webkit-flex;\n    align-items: center;\n    .login-btn {\n      border: 1px solid transparent;\n      border-radius: 4px;\n      box-sizing: border-box;\n      text-align: center;\n      letter-spacing: 1px;\n      text-decoration: none;\n      font-size: 12px;\n      font-weight: 700;\n      letter-spacing: 0.5px;\n      line-height: 24px;\n      text-transform: uppercase;\n      padding: 3px 16px;\n      border-color: #0079d3;\n      color: #0079d3;\n      fill: #0079d3;\n      display: inline-block;\n      cursor: pointer;\n      &:nth-child(1) {\n        margin-right: 5px;\n      }\n      &:nth-child(2) {\n        margin-right: 10px;\n      }\n    }\n    .user {\n      width: auto;\n      height: 24px;\n      background: url(\"../assets/images/avatar.png\") no-repeat;\n      background-size: 24px 24px;\n      background-position: left center;\n      padding-left: 28px;\n      display: flex;\n      display: -webkit-flex;\n      align-items: center;\n      cursor: pointer;\n      padding: 12px 12px 12px 28px;\n      &::after {\n        content: \"\";\n        width: 0;\n        height: 0;\n        border-top: 5px solid #878a8c;\n        border-right: 5px solid transparent;\n        border-bottom: 5px solid transparent;\n        border-left: 5px solid transparent;\n        margin-top: 5px;\n        margin-left: 10px;\n      }\n    }\n    .dropdown-content {\n      display: none;\n      position: absolute;\n      background-color: #f9f9f9;\n      min-width: 160px;\n      box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);\n      a {\n        color: black;\n        padding: 12px 16px;\n        text-decoration: none;\n        display: block;\n        cursor: pointer;\n      }\n      a:hover {background-color: #f1f1f1}\n    }\n    .user-box:hover .dropdown-content {\n      display: block;\n    }\n  }\n  \n}\n</style>\n"]}]}