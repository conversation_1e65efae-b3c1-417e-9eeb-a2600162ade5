{"version": 3, "file": "BinarySearchTree.js", "sourceRoot": "", "sources": ["../../../src/rules/indent-new-do-not-use/BinarySearchTree.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,kGAAkG;;;;;AAGlG,0FAAmD;AASnD;;;;GAIG;AACH,MAAa,gBAAgB;IAA7B;QACU,WAAM,GAAG,mCAAU,EAAqB,CAAC;IAwCnD,CAAC;IAtCC;;OAEG;IACI,MAAM,CAAC,GAAW,EAAE,KAAgB;QACzC,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAEvC,IAAI,QAAQ,CAAC,KAAK,EAAE;YAClB,IAAI,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;SACtC;aAAM;YACL,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;SAC9C;IACH,CAAC;IAED;;;OAGG;IACI,MAAM,CAAC,GAAW;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;QAErC,OAAO,EAAE,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE,KAAK,EAAE,QAAQ,CAAC,KAAK,EAAE,CAAC;IACtD,CAAC;IAED;;OAEG;IACI,WAAW,CAAC,KAAa,EAAE,GAAW;QAC3C,+DAA+D;QAC/D,IAAI,KAAK,KAAK,GAAG,EAAE;YACjB,OAAO;SACR;QACD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC;QAEvC,OAAO,QAAQ,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG,GAAG,GAAG,EAAE;YAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YAC/C,QAAQ,CAAC,IAAI,EAAE,CAAC;SACjB;IACH,CAAC;CACF;AAzCD,4CAyCC"}