{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js!E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js??ref--13-0!E:\\ThisGo\\bluebell_frontend\\src\\service\\api.js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\service\\api.js", "mtime": 1598765250000}, {"path": "E:\\ThisGo\\bluebell_frontend\\babel.config.js", "mtime": 1588522048000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\eslint-loader\\index.js", "mtime": 1756017426480}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IGF4aW9zIGZyb20gJ2F4aW9zJzsKYXhpb3MuZGVmYXVsdHMuYmFzZVVSTCA9ICIvYXBpL3YxLyI7CmF4aW9zLmludGVyY2VwdG9ycy5yZXF1ZXN0LnVzZShjb25maWcgPT4gewogIGxldCBsb2dpblJlc3VsdCA9IEpTT04ucGFyc2UobG9jYWxTdG9yYWdlLmdldEl0ZW0oImxvZ2luUmVzdWx0IikpOwogIGlmIChsb2dpblJlc3VsdCkgewogICAgY29uc3QgdG9rZW4gPSBsb2dpblJlc3VsdC50b2tlbjsKICAgIGNvbmZpZy5oZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dG9rZW59YDsKICB9CiAgcmV0dXJuIGNvbmZpZzsKfSwgZXJyb3IgPT4gewogIHJldHVybiBQcm9taXNlLnJlamVjdChlcnJvcik7Cn0pOwpheGlvcy5pbnRlcmNlcHRvcnMucmVzcG9uc2UudXNlKHJlc3BvbnNlID0+IHsKICBpZiAocmVzcG9uc2Uuc3RhdHVzID09PSAyMDApIHsKICAgIHJldHVybiBQcm9taXNlLnJlc29sdmUocmVzcG9uc2UuZGF0YSk7CiAgfSBlbHNlIHsKICAgIHJldHVybiBQcm9taXNlLnJlamVjdChyZXNwb25zZS5kYXRhKTsKICB9Cn0sIGVycm9yID0+IHsKICBjb25zb2xlLmxvZygnZXJyb3InLCBlcnJvcik7Cn0pOwpleHBvcnQgZGVmYXVsdCBheGlvczs="}, {"version": 3, "names": ["axios", "defaults", "baseURL", "interceptors", "request", "use", "config", "loginResult", "JSON", "parse", "localStorage", "getItem", "token", "headers", "Authorization", "error", "Promise", "reject", "response", "status", "resolve", "data", "console", "log"], "sources": ["E:/ThisGo/bluebell_frontend/src/service/api.js"], "sourcesContent": ["\nimport axios from 'axios';\naxios.defaults.baseURL = \"/api/v1/\";\naxios.interceptors.request.use((config) => {\n  let loginResult = JSON.parse(localStorage.getItem(\"loginResult\"));\n  if (loginResult) { \n\tconst token = loginResult.token\n\tconfig.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, (error) => {\n\treturn Promise.reject(error);\n});\n\naxios.interceptors.response.use(\n\tresponse => {\n\t\tif (response.status === 200) {\n\t\t\treturn Promise.resolve(response.data);\n\t\t} else {\n\t\t\treturn Promise.reject(response.data);\n\t\t}\n\t},\n\t(error) => {\n\t\tconsole.log('error', error);\n\t}\n);\n\nexport default axios;"], "mappings": "AACA,OAAOA,KAAK,MAAM,OAAO;AACzBA,KAAK,CAACC,QAAQ,CAACC,OAAO,GAAG,UAAU;AACnCF,KAAK,CAACG,YAAY,CAACC,OAAO,CAACC,GAAG,CAAEC,MAAM,IAAK;EACzC,IAAIC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC,CAAC;EACjE,IAAIJ,WAAW,EAAE;IAClB,MAAMK,KAAK,GAAGL,WAAW,CAACK,KAAK;IAC/BN,MAAM,CAACO,OAAO,CAACC,aAAa,GAAG,UAAUF,KAAK,EAAE;EAC/C;EACA,OAAON,MAAM;AACf,CAAC,EAAGS,KAAK,IAAK;EACb,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC7B,CAAC,CAAC;AAEFf,KAAK,CAACG,YAAY,CAACe,QAAQ,CAACb,GAAG,CAC9Ba,QAAQ,IAAI;EACX,IAAIA,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;IAC5B,OAAOH,OAAO,CAACI,OAAO,CAACF,QAAQ,CAACG,IAAI,CAAC;EACtC,CAAC,MAAM;IACN,OAAOL,OAAO,CAACC,MAAM,CAACC,QAAQ,CAACG,IAAI,CAAC;EACrC;AACD,CAAC,EACAN,KAAK,IAAK;EACVO,OAAO,CAACC,GAAG,CAAC,OAAO,EAAER,KAAK,CAAC;AAC5B,CACD,CAAC;AAED,eAAef,KAAK", "ignoreList": []}]}