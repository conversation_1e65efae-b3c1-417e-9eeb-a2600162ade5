.submixin(@a) {
  border-width: @a;
}
.mixin (9) {
  border: 9 !important;
}
.mixin (@a: 0) {
  border: @a;
  boxer: @a;
  .inner {
    test: @a;
  }
  // comment
  .submixin(@a);
}

.class {
  .mixin(1);
  .mixin(2) !important;
  .mixin(3);
  .mixin(4) !important;
  .mixin(5);
  .mixin !important;
  .mixin(9);
}
.size(@aaa: auto) {
  .set-width(@aaa) {
    width: @aaa;
  }
  .set-width(@aaa);
}
.when-calling-nested-issue-2394 {
  .size() !important;
}
.when-calling-nested-with-param-issue-2394 {
  .size(10px) !important;
}
.test-ruleMixin-2421 () {
    .topCheck-2421 () {
        .nestedCheck-2421() {
            margin: 5px;
        }
        .nestedCheck-2421();
    }
    .topCheck-2421();
}
.class1-2421 {
    .test-ruleMixin-2421() !important;
}
.class2-2421 {
    .test-ruleMixin-2421();
}

