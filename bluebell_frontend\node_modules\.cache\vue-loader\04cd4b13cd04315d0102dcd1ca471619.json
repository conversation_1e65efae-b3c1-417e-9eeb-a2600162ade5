{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Home.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Home.vue", "mtime": 1600241974000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["Home.vue"], "names": [], "mappings": ";AA+GA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <!-- <h4 class=\"c-l-title\">热门帖子</h4> -->\n      <div class=\"c-l-header\">\n        <div class=\"new btn-iconfont\"\n        :class=\"{ active: timeOrder }\"\n        @click=\"selectOrder('time')\"\n        >\n          <i class=\"iconfont icon-polygonred\"></i>New\n        </div>\n        <div class=\"top btn-iconfont\"\n         :class=\"{ active: scoreOrder }\"\n         @click=\"selectOrder('score')\"\n        >\n          <i class=\"iconfont icon-top\"></i>Score\n        </div>\n        <button class=\"btn-publish\" @click=\"goPublish\">发表</button>\n      </div>\n      <ul class=\"c-l-list\">\n        <li class=\"c-l-item\"  v-for=\"post in postList\" :key=\"post.id\">\n          <div class=\"post\">\n            <a class=\"vote\">\n              <span class=\"iconfont icon-up\"\n              @click=\"vote(post.id, '1')\"\n              ></span>\n            </a>\n            <span class=\"text\">{{post.vote_num}}</span>\n            <a class=\"vote\">\n              <span class=\"iconfont icon-down\"\n              @click=\"vote(post.id, '-1')\"\n              ></span>\n            </a>\n          </div>\n          <div class=\"l-container\" @click=\"goDetail(post.id)\">\n            <h4 class=\"con-title\">{{post.title}}</h4>\n            <div class=\"con-memo\">\n              <p>{{post.content}}</p>\n            </div>\n            <!-- <div class=\"user-btn\">\n              <span class=\"btn-item\">\n                <i class=\"iconfont icon-comment\"></i>\n                <span>{{post.comments}} comments</span>\n              </span>\n            </div> -->\n          </div>\n        </li>\n      </ul>\n    </div>\n    <div class=\"right\">\n      <div class=\"communities\">\n        <h2 class=\"r-c-title\">今日火热频道排行榜</h2>\n        <ul class=\"r-c-content\">\n          <li class=\"r-c-item\">\n            <span class=\"index\">1</span>\n            <i class=\"icon\"></i>\n            b/coding\n          </li>\n          <li class=\"r-c-item\">\n            <span class=\"index\">2</span>\n            <i class=\"icon\"></i>\n            b/tree_hole\n          </li>\n          <li class=\"r-c-item\">\n            <span class=\"index\">3</span>\n            <i class=\"icon\"></i>\n            b/job\n          </li>\n        </ul>\n        <button class=\"view-all\">查看所有</button>\n      </div>\n      <div class=\"r-trending\">\n        <h2 class=\"r-t-title\">持续热门频道</h2>\n        <ul class=\"rank\">\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/Book</span>\n                <p class=\"info-num\">7.1k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/coding</span>\n                <p class=\"info-num\">3.2k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n          <li class=\"r-t-cell\">\n            <div class=\"r-t-cell-info\">\n              <div class=\"avatar\"></div>\n              <div class=\"info\">\n                <span class=\"info-title\">b/job</span>\n                <p class=\"info-num\">2.5k members</p>\n              </div>\n            </div>\n            <button class=\"join-btn\">JOIN</button>\n          </li>\n        </ul>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n// @ is an alias to /src\n\nexport default {\n  name: \"Home\",\n  components: {},\n  data() {\n    return {\n      order: \"time\",\n      page: 1,\n      postList: []\n    };\n  },\n  methods: {\n    selectOrder(order){\n      this.order = order;\n      this.getPostList()\n    },\n    goPublish(){\n      this.$router.push({ name: \"Publish\" });\n    },\n    goDetail(id){\n      this.$router.push({ name: \"Content\", params: { id: id }});\n    },\n    getPostList() {\n      this.$axios({\n        method: \"get\",\n        url: \"/posts2\",\n        params: {\n          page: this.page,\n          order: this.order,\n        }\n      })\n        .then(response => {\n          console.log(response.data, 222);\n          if (response.code == 1000) {\n            this.postList = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n    vote(post_id, direction){\n      this.$axios({\n        method: \"post\",\n        url: \"/vote\",\n        data: JSON.stringify({\n          post_id: post_id,\n          direction: direction,\n        })\n      })\n        .then(response => {\n          if (response.code == 1000) {\n            console.log(\"vote success\");\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    }\n  },\n  mounted: function() {\n    this.getPostList();\n  },\n  computed:{\n    timeOrder(){\n      return this.order == \"time\";\n    },\n    scoreOrder(){\n      return this.order == \"score\";\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"less\">\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 48px auto 0;\n  padding: 20px 24px;\n  .left {\n    width: 640px;\n    padding-bottom: 10px;\n    .c-l-title {\n      font-size: 14px;\n      font-weight: 500;\n      line-height: 18px;\n      color: #1a1a1b;\n      text-transform: unset;\n      padding-bottom: 10px;\n    }\n    .c-l-header {\n      align-items: center;\n      background-color: #ffffff;\n      border: 1px solid #ccc;\n      border-radius: 4px;\n      box-sizing: border-box;\n      display: -ms-flexbox;\n      display: flex;\n      -ms-flex-flow: row nowrap;\n      flex-flow: row nowrap;\n      height: 56px;\n      -ms-flex-pack: start;\n      justify-content: flex-start;\n      margin-bottom: 16px;\n      padding: 0 12px;\n      .iconfont {\n        margin-right: 4px;\n      }\n      .btn-iconfont {\n        display: flex;\n        display: -webkit-flex;\n      }\n      .active {\n        background: #f6f7f8;\n        color: #0079d3;\n        fill: #0079d3;\n        border-radius: 20px;\n        height: 32px;\n        line-height: 32px;\n        margin-right: 8px;\n        padding: 0 10px;\n      }\n      .new {\n        font-size: 14px;\n        margin-right: 18px;\n      }\n      .top {\n        font-size: 14px;\n      }\n      .btn-publish {\n        width: 64px;\n        height: 32px;\n        line-height: 32px;\n        background-color: #54b351;\n        color: #ffffff;\n        border: 1px solid transparent;\n        border-radius: 4px;\n        box-sizing: border-box;\n        text-align: center;\n        margin-left: auto;\n        cursor: pointer;\n      }\n      .sort {\n        margin-left: 300px;\n        display: flex;\n        color: #0079d3;\n        display: -webkit-flex;\n        align-items: center;\n        .sort-triangle {\n          width: 0;\n          height: 0;\n          border-top: 5px solid #0079d3;\n          border-right: 5px solid transparent;\n          border-bottom: 5px solid transparent;\n          border-left: 5px solid transparent;\n          margin-top: 5px;\n          margin-left: 10px;\n        }\n      }\n    }\n    .c-l-list {\n      .c-l-item {\n        list-style: none;\n        border-radius: 4px;\n        padding-left: 40px;\n        cursor: pointer;\n        border: 1px solid #ccc;\n        margin-bottom: 10px;\n        background-color: rgba(255, 255, 255, 0.8);\n        color: #878a8c;\n        position: relative;\n        .post {\n          align-items: center;\n          box-sizing: border-box;\n          display: -ms-flexbox;\n          display: flex;\n          -ms-flex-direction: column;\n          flex-direction: column;\n          height: 100%;\n          left: 0;\n          padding: 8px 4px 8px 0;\n          position: absolute;\n          top: 0;\n          width: 40px;\n          border-left: 4px solid transparent;\n          background: #f8f9fa;\n          .iconfont {\n            margin-right: 0;\n          }\n          .down {\n            transform: scaleY(-1);\n          }\n          .text {\n            color: #1a1a1b;\n            font-size: 12px;\n            font-weight: 700;\n            line-height: 16px;\n            pointer-events: none;\n            word-break: normal;\n          }\n        }\n        .l-container {\n          padding: 15px;\n          .con-title {\n            color: #000000;\n            font-size: 18px;\n            font-weight: 500;\n            line-height: 22px;\n            text-decoration: none;\n            word-break: break-word;\n          }\n          .con-memo {\n            margin-top: 10px;\n            margin-bottom: 10px;\n          }\n          .con-cover {\n            height: 512px;\n            width: 100%;\n            background: url(\"https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1585999647247&di=7e9061211c23e3ed9f0c4375bb3822dc&imgtype=0&src=http%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F04d8cda08e170f4a58c18c45a93c539375c22162.jpg\")\n              no-repeat;\n            background-size: cover;\n            margin-top: 10px;\n            margin-bottom: 10px;\n          }\n          .user-btn {\n            font-size: 14px;\n            display: flex;\n            display: -webkit-flex;\n            .btn-item {\n              display: flex;\n              display: -webkit-flex;\n              margin-right: 10px;\n              .iconfont {\n                margin-right: 4px;\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n  .right {\n    width: 312px;\n    margin-left: 24px;\n    margin-top: 28px;\n    .communities {\n      background-color: #ffffff;\n      color: #1a1a1b;\n      border: 1px solid #ccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      margin-bottom: 20px;\n      .r-c-title {\n        background-image: linear-gradient(\n          0deg,\n          rgba(0, 0, 0, 0.3) 0,\n          transparent\n        );\n        background-color: #0079d3;\n        height: 80px;\n        width: 100%;\n        color: #fff;\n        font-size: 20px;\n        line-height: 120px;\n        padding-left: 10px;\n        box-sizing: border-box;\n        text-align: center;\n      }\n      .r-c-content {\n        .r-c-item {\n          align-items: center;\n          display: flex;\n          display: -webkit-flex;\n          height: 48px;\n          padding: 0 12px;\n          border-bottom: thin solid #edeff1;\n          font-size: 14px;\n          .index {\n            width: 20px;\n            color: #1c1c1c;\n            font-size: 14px;\n            font-weight: 500;\n            line-height: 18px;\n          }\n          .icon {\n            width: 32px;\n            height: 32px;\n            background-image: url(\"../assets/images/avatar.png\");\n            background-repeat: no-repeat;\n            background-size: cover;\n            margin-right: 20px;\n          }\n          &:last-child {\n            border-bottom: none;\n          }\n        }\n      }\n      .view-all {\n        background-color: #0079d3;\n        border: 1px solid transparent;\n        border-radius: 4px;\n        box-sizing: border-box;\n        text-align: center;\n        letter-spacing: 1px;\n        text-decoration: none;\n        font-size: 12px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 24px;\n        text-transform: uppercase;\n        padding: 3px 0;\n        width: 280px;\n        color: #fff;\n        margin: 20px 0 20px 16px;\n      }\n    }\n    .r-trending {\n      padding-top: 16px;\n      width: 312px;\n      background-color: #ffffff;\n      color: #1a1a1b;\n      fill: #1a1a1b;\n      border: 1px solid #cccccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      .r-t-title {\n        font-size: 10px;\n        font-weight: 700;\n        letter-spacing: 0.5px;\n        line-height: 12px;\n        text-transform: uppercase;\n        background-color: #ffffff;\n        border-radius: 3px 3px 0 0;\n        color: #1a1a1b;\n        display: -ms-flexbox;\n        display: flex;\n        fill: #1a1a1b;\n        padding: 0 12px 12px;\n      }\n      .rank {\n        padding: 12px;\n        .r-t-cell {\n          display: flex;\n          display: -webkit-flex;\n          align-items: center;\n          justify-content: space-between;\n          margin-bottom: 16px;\n          .r-t-cell-info {\n            display: flex;\n          }\n          .avatar {\n            width: 32px;\n            height: 32px;\n            background: url(\"../assets/images/avatar.png\") no-repeat;\n            background-size: cover;\n            margin-right: 10px;\n          }\n          .info {\n            margin-right: 10px;\n            .info-title {\n              font-size: 12px;\n              font-weight: 500;\n              line-height: 16px;\n              text-overflow: ellipsis;\n              width: 144px;\n            }\n            .info-num {\n              font-size: 12px;\n              font-weight: 400;\n              line-height: 16px;\n              padding-bottom: 4px;\n            }\n          }\n          .join-btn {\n            width: 106px;\n            height: 32px;\n            line-height: 32px;\n            background-color: #0079d3;\n            color: #ffffff;\n            border: 1px solid transparent;\n            border-radius: 4px;\n            box-sizing: border-box;\n            text-align: center;\n          }\n        }\n      }\n    }\n  }\n}\n</style>\n"]}]}