@import "nested-gradient-with-svg-gradient/mixin-consumer.less";

@font-face {
  src: url("/fonts/garamond-pro.ttf");
  src: local(Futura-Medium),
       url(fonts.svg#MyGeometricModern) format("svg");
  not-a-comment: url(//z);
}
#shorthands {
  background: url("http://www.lesscss.org/spec.html") no-repeat 0 4px;
  background: url("img.jpg") center / 100px;
  background: #fff url(image.png) center / 1px 100px repeat-x scroll content-box padding-box;
}
#misc {
  background-image: url(images/image.jpg);
}
#data-uri {
  background: url(data:image/png;charset=utf-8;base64,
    kiVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAABlBMVEUAAAD/
    k//+l2Z/dAAAAM0lEQVR4nGP4/5/h/1+G/58ZDrAz3D/McH8yw83NDDeNGe4U
    kg9C9zwz3gVLMDA/A6P9/AFGGFyjOXZtQAAAAAElFTkSuQmCC);
  background-image: url(data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==);
  background-image: url(http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700);
  background-image: url("http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700");
}

#svg-data-uri {
  background: transparent url('data:image/svg+xml, <svg version="1.1"><g></g></svg>');
}

.comma-delimited {
  background: url(bg.jpg) no-repeat, url(bg.png) repeat-x top left, url(bg);
}
.values {
    @a: 'Trebuchet';
    url: url(@a);
}

@import "import/import-and-relative-paths-test";

#relative-url-import {
    .unquoted-relative-path-bg;
    .quoted-relative-path-border-image;
}

#data-uri {
  uri: data-uri('image/jpeg;base64', '../data/image.jpg');
  @var: replace('../data/replace.jpg', "replace", "image");
  background-image: data-uri(@var), data-uri(replace('../data/image.filext', "filext", "jpg"));

  uri-fragment: data-uri('image/jpeg;base64', '../data/image.jpg#fragment');
}

#data-uri-guess {
  uri: data-uri('../data/image.jpg');
}

#data-uri-ascii {
  uri-1: data-uri('text/html', '../data/page.html');
  uri-2: data-uri('../data/page.html');
}

#file-functions {
  svg-not-base-64: data-uri('../data/image.svg');
  size: image-size('../data/data-uri-fail.png');
  width: image-width('../data/data-uri-fail.png');
  height: image-height('../data/data-uri-fail.png');
}
.add_an_import(@file_to_import) {
@import "@{file_to_import}";
}

.add_an_import("file.css");

#svg-functions {
  background-image: svg-gradient(to bottom, black, white);
  background-image: svg-gradient(to bottom, black, orange 3%, white);
  @green_5: green 5%;
  @orange_percentage: 3%;
  @orange_color: orange;
  background-image: svg-gradient(to bottom, (mix(black, white) + #444) 1%, @orange_color @orange_percentage, ((@green_5)), white 95%);
}
//it should work also inside @font-face #2035
@font-face {
  font-family: 'MyWebFont';
  src: url(webfont.eot);
  src+: url('webfont.eot?#iefix');
  src+_: format('embedded-opentype');
  src+: url('webfont.woff') format('woff');
  src+: format('truetype');
  src+_: url('webfont.ttf');
  src+: url('webfont.svg#svgFontName') format('svg');
}
