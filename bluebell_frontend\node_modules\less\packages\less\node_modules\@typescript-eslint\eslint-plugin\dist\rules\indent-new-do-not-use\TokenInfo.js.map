{"version": 3, "file": "TokenInfo.js", "sourceRoot": "", "sources": ["../../../src/rules/indent-new-do-not-use/TokenInfo.ts"], "names": [], "mappings": ";AAAA,6DAA6D;AAC7D,kGAAkG;;AAKlG;;GAEG;AACH,MAAa,SAAS;IAIpB,YAAY,UAA+B;QACzC,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,uBAAuB,GAAG,UAAU,CAAC,iBAAiB,CAAC,MAAM,CAChE,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE;YACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;gBAClC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACtC;YACD,IACE,CAAC,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC;gBAC5B,UAAU,CAAC,IAAI;qBACZ,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;qBAC5D,IAAI,EAAE,EACT;gBACA,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aACpC;YACD,OAAO,GAAG,CAAC;QACb,CAAC,EACD,IAAI,GAAG,EAAE,CACV,CAAC;IACJ,CAAC;IAED;;;OAGG;IACI,mBAAmB,CACxB,KAAqC;QAErC,OAAO,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAE,CAAC;IACjE,CAAC;IAED;;;OAGG;IACI,kBAAkB,CAAC,KAAqB;QAC7C,OAAO,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC;IACnD,CAAC;IAED;;;;OAIG;IACI,cAAc,CAAC,KAAqB;QACzC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAC/B,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,EACvC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CACf,CAAC;IACJ,CAAC;CACF;AAtDD,8BAsDC"}