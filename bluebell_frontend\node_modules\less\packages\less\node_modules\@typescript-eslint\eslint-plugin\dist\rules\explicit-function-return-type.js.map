{"version": 3, "file": "explicit-function-return-type.js", "sourceRoot": "", "sources": ["../../src/rules/explicit-function-return-type.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8EAG+C;AAC/C,8CAAgC;AAWhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,+BAA+B;IACrC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;YAChE,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,MAAM;SACpB;QACD,QAAQ,EAAE;YACR,iBAAiB,EAAE,kCAAkC;SACtD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;oBACD,6BAA6B,EAAE;wBAC7B,IAAI,EAAE,SAAS;qBAChB;oBACD,yBAAyB,EAAE;wBACzB,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,gBAAgB,EAAE,KAAK;YACvB,6BAA6B,EAAE,KAAK;YACpC,yBAAyB,EAAE,KAAK;SACjC;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB;;;WAGG;QACH,SAAS,aAAa,CAAC,IAA+B;YACpD,OAAO,CACL,CAAC,CAAC,IAAI;gBACN,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,IAAI,KAAK,aAAa,CAC5B,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,QAAQ,CAAC,IAA+B;YAC/C,OAAO,CACL,CAAC,CAAC,IAAI;gBACN,CAAC,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,gBAAgB;oBAC5C,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,QAAQ,CAAC;gBACxC,IAAI,CAAC,IAAI,KAAK,KAAK,CACpB,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,sCAAsC,CAC7C,IAAmB;YAEnB,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,kBAAkB;gBAC/C,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CACzB,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,iCAAiC,CAAC,IAAmB;YAC5D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,aAAa,IAAI,CAAC,CAAC,IAAI,CAAC,cAAc,CACpE,CAAC;QACJ,CAAC;QAED;;;;WAIG;QACH,SAAS,UAAU,CAAC,IAAmB;YACrC,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc;gBAC3C,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,eAAe,CAC7C,CAAC;QACJ,CAAC;QAED;;;;;WAKG;QACH,SAAS,0BAA0B,CACjC,MAAiC;YAEjC,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,QAAQ,EAAE;gBACtD,OAAO,KAAK,CAAC;aACd;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,+CAA+C;YACvE,wBAAwB,CAAC,IACvB,CAAC,MAAM;gBACP,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,gBAAgB,EAC/C;gBACA,OAAO,KAAK,CAAC;aACd;YAED,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,+CAA+C;YACvE,wBAAwB,CAAC,IAAI,CAAC,MAAM,EAAE;gBACpC,OAAO,KAAK,CAAC;aACd;YAED,OAAO,CACL,UAAU,CAAC,MAAM,CAAC;gBAClB,iCAAiC,CAAC,MAAM,CAAC;gBACzC,sCAAsC,CAAC,MAAM,CAAC,CAC/C,CAAC;QACJ,CAAC;QAED;;;;;;;;WAQG;QACH,SAAS,uCAAuC,CAAC,EAC/C,IAAI,GAIyB;YAC7B,0DAA0D;YAC1D,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE;gBAClC,OAAO,KAAK,CAAC;aACd;YAED,mDAAmD;YACnD,IACE,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc;gBAC3C,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EACtB;gBACA,MAAM,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;gBAE9B,iEAAiE;gBACjE,IACE,SAAS,CAAC,IAAI,KAAK,mCAAc,CAAC,eAAe;oBACjD,CAAC,CAAC,SAAS,CAAC,QAAQ,EACpB;oBACA,8CAA8C;oBAC9C,IAAI,GAAG,SAAS,CAAC,QAAQ,CAAC;iBAC3B;aACF;YAED,4DAA4D;YAC5D,OAAO,CACL,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,uBAAuB;gBACpD,IAAI,CAAC,IAAI,KAAK,mCAAc,CAAC,kBAAkB,CAChD,CAAC;QACJ,CAAC;QAED;;;WAGG;QACH,SAAS,kBAAkB,CACzB,MAAqB,EACrB,KAAoB;YAEpB,OAAO,CACL,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,cAAc;gBAC7C,+BAA+B;gBAC/B,MAAM,CAAC,MAAM,KAAK,KAAK,CACxB,CAAC;QACJ,CAAC;QAED;;WAEG;QACH,SAAS,uBAAuB,CAC9B,IAG+B;YAE/B,IACE,OAAO,CAAC,yBAAyB;gBACjC,uCAAuC,CAAC,IAAI,CAAC,EAC7C;gBACA,OAAO;aACR;YAED,IACE,IAAI,CAAC,UAAU;gBACf,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC1B,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,EACrB;gBACA,OAAO;aACR;YAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;gBAChD,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,mBAAmB;iBAC/B,CAAC,CAAC;aACJ;QACH,CAAC;QAED;;WAEG;QACH,SAAS,iCAAiC,CACxC,IAAoE;YAEpE,qDAAqD;YACrD,0BAA0B,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE;gBAC1C,IAAI,OAAO,CAAC,6BAA6B,EAAE;oBACzC,IACE,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC;wBACvB,sCAAsC,CAAC,IAAI,CAAC,MAAM,CAAC;wBACnD,iCAAiC,CAAC,IAAI,CAAC,MAAM,CAAC;wBAC9C,0BAA0B,CAAC,IAAI,CAAC,MAAM,CAAC;wBACvC,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EACrC;wBACA,OAAO;qBACR;iBACF;gBAED,IACE,OAAO,CAAC,gBAAgB;oBACxB,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,kBAAkB;oBACtD,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,mCAAc,CAAC,gBAAgB,EACpD;oBACA,OAAO;iBACR;aACF;YAED,uBAAuB,CAAC,IAAI,CAAC,CAAC;QAChC,CAAC;QAED,OAAO;YACL,uBAAuB,EAAE,iCAAiC;YAC1D,mBAAmB,EAAE,uBAAuB;YAC5C,kBAAkB,EAAE,iCAAiC;SACtD,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}