#strings {
  background-image: url("http://son-of-a-banana.com");
  quotes: "~" "~";
  content: "#*%:&^,)!.(~*})";
  empty: "";
  brackets: "{" "}";
  escapes: "\"hello\" \\world";
  escapes2: "\"llo";
}
#comments {
  content: "/* hello */ // not-so-secret";
}
#single-quote {
  quotes: "'" "'";
  content: '""#!&""';
  empty: '';
  semi-colon: ';';
}
#escaped {
  filter: ~"DX.Transform.MS.BS.filter(opacity=50)";
}
#one-line { image: url(http://tooks.com) }
#crazy { image: url(http://), "}", url("http://}") }
#interpolation {
  @var: '/dev';
  url: "http://lesscss.org@{var}/image.jpg";

  @var2: 256;
  url2: "http://lesscss.org/image-@{var2}.jpg";

  @var3: #456;
  url3: "http://lesscss.org@{var3}";

  @var4: hello;
  url4: "http://lesscss.org/@{var4}";

  @var5: 54.4px;
  url5: "http://lesscss.org/@{var5}";
}

// multiple calls with string interpolation

.mix-mul (@a: green) {
    color: ~"@{a}";
}
.mix-mul-class {
    .mix-mul(blue);
    .mix-mul(red);
    .mix-mul(black);
    .mix-mul(orange);
}

@test: Arial, Verdana, San-Serif;
.watermark {
  @family: ~"Univers, @{test}";
  family: @family;
}
#iterated-interpolation {
  @box-small: 10px;
  @box-large: 100px;

  .mixin { // both ruleset and mixin
    width: ~"@{box-@{suffix}}";
    weird: ~"@{box}-@{suffix}}";
    width-str: "@{box-@{suffix}}";
    weird-str: "@{box}-@{suffix}}";
    @box: ~"@{box";
    @suffix: large;
  }
  .interpolation-mixin {
    .mixin(); //call the above as mixin
  }
}
