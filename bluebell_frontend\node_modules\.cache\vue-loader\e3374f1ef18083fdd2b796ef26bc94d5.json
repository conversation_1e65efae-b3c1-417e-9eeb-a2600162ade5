{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\views\\Content.vue?vue&type=script&lang=js", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\views\\Content.vue", "mtime": 1598770386000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1756017427282}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAiQ29udGVudCIsCiAgZGF0YSgpewogICAgcmV0dXJuIHsKICAgICAgcG9zdDp7fSwKICAgIH0KICB9LAogIG1ldGhvZHM6ewogICAgZ2V0UG9zdERldGFpbCgpIHsKICAgICAgdGhpcy4kYXhpb3MoewogICAgICAgIG1ldGhvZDogImdldCIsCiAgICAgICAgdXJsOiAiL3Bvc3QvIisgdGhpcy4kcm91dGUucGFyYW1zLmlkLAogICAgICB9KQogICAgICAgIC50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgIGNvbnNvbGUubG9nKDEsIHJlc3BvbnNlLmRhdGEpOwogICAgICAgICAgaWYgKHJlc3BvbnNlLmNvZGUgPT0gMTAwMCkgewogICAgICAgICAgICB0aGlzLnBvc3QgPSByZXNwb25zZS5kYXRhOwogICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UubXNnKTsKICAgICAgICAgIH0KICAgICAgICB9KQogICAgICAgIC5jYXRjaChlcnJvciA9PiB7CiAgICAgICAgICBjb25zb2xlLmxvZyhlcnJvcik7CiAgICAgICAgfSk7CiAgICB9LAogIH0sCiAgbW91bnRlZDogZnVuY3Rpb24oKSB7CiAgICB0aGlzLmdldFBvc3REZXRhaWwoKTsKICB9Cn07Cg=="}, {"version": 3, "sources": ["Content.vue"], "names": [], "mappings": ";AAsEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "Content.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"content\">\n    <div class=\"left\">\n      <div class=\"container\">\n        <div class=\"post\">\n          <a class=\"vote\">\n            <span class=\"iconfont icon-up\"></span>\n          </a>\n          <span class=\"text\">50.2k</span>\n          <a class=\"vote\">\n            <span class=\"iconfont icon-down\"></span>\n          </a>\n        </div>\n        <div class=\"l-container\">\n          <h4 class=\"con-title\">{{post.title}}</h4>\n          <div class=\"con-info\">{{post.content}}</div>\n          <div class=\"user-btn\">\n            <span class=\"btn-item\">\n              <i class=\"iconfont icon-comment\"></i>comment\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- <div class=\"comment\">\n        <div class=\"c-left\">\n          <div class=\"line\"></div>\n          <div class=\"c-arrow\">\n                            <a class=\"vote\"><span class=\"iconfont icon-up\"></span></a>\n                            <a class=\"up down\"></a>\n          </div>\n        </div>\n        <div class=\"c-right\">\n          <div class=\"c-user-info\">\n            <span class=\"name\">mahlerific</span>\n            <span class=\"num\">1.4k points</span>\n            <span class=\"num\">· 5 hours ago</span>\n          </div>\n          <p\n            class=\"c-content\"\n          >We're having the same experience in Yerevan, Armenia. Though you can see mountains all around the city on good days, now you can see even farther into Turkey and Iran. Every crag on the mountains around us is now clearer than ever.</p>\n        </div>\n      </div> -->\n    </div>\n    <div class=\"right\">\n      <div class=\"topic-info\">\n        <h5 class=\"t-header\"></h5>\n        <div class=\"t-info\">\n          <a class=\"avatar\"></a>\n          <span class=\"topic-name\">b/{{post.community_name}}</span>\n        </div>\n        <p class=\"t-desc\">树洞 树洞 无限树洞的树洞</p>\n        <ul class=\"t-num\">\n          <li class=\"t-num-item\">\n            <p class=\"number\">5.2m</p>\n            <span class=\"unit\">Members</span>\n          </li>\n          <li class=\"t-num-item\">\n            <p class=\"number\">5.2m</p>\n            <span class=\"unit\">Members</span>\n          </li>\n        </ul>\n        <div class=\"date\">Created Apr 10, 2008</div>\n        <button class=\"topic-btn\">JOIN</button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: \"Content\",\n  data(){\n    return {\n      post:{},\n    }\n  },\n  methods:{\n    getPostDetail() {\n      this.$axios({\n        method: \"get\",\n        url: \"/post/\"+ this.$route.params.id,\n      })\n        .then(response => {\n          console.log(1, response.data);\n          if (response.code == 1000) {\n            this.post = response.data;\n          } else {\n            console.log(response.msg);\n          }\n        })\n        .catch(error => {\n          console.log(error);\n        });\n    },\n  },\n  mounted: function() {\n    this.getPostDetail();\n  }\n};\n</script>\n\n<style lang=\"less\" scoped>\n.content {\n  max-width: 100%;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: row;\n  justify-content: center;\n  margin: 0 auto;\n  padding: 20px 24px;\n  margin-top: 48px;\n  .left {\n    flex-grow: 1;\n    max-width: 740px;\n    border-radius: 4px;\n    word-break: break-word;\n    background: #ffffff;\n    border: #edeff1;\n    flex: 1;\n    margin: 32px;\n    margin-right: 12px;\n    padding-bottom: 30px;\n    position: relative;\n    .container {\n      width: 100%;\n      height: auto;\n      position: relative;\n      .post {\n        align-items: center;\n        box-sizing: border-box;\n        display: -ms-flexbox;\n        display: flex;\n        -ms-flex-direction: column;\n        flex-direction: column;\n        height: 100%;\n        left: 0;\n        padding: 8px 4px 8px 0;\n        position: absolute;\n        top: 0;\n        width: 40px;\n        border-left: 4px solid transparent;\n        // background: #f8f9fa;\n        .text {\n          color: #1a1a1b;\n          font-size: 12px;\n          font-weight: 700;\n          line-height: 16px;\n          pointer-events: none;\n          word-break: normal;\n        }\n      }\n      .l-container {\n        padding: 15px;\n        margin-left: 40px;\n        .con-title {\n          color: #000000;\n          font-size: 18px;\n          font-weight: 500;\n          line-height: 22px;\n          text-decoration: none;\n          word-break: break-word;\n        }\n        .con-info{\n          margin: 25px 0;\n          padding: 15px 0;\n          border-bottom: 1px solid grey;\n        }\n        .con-cover {\n          height: 512px;\n          width: 100%;\n          background: url(\"https://timgsa.baidu.com/timg?image&quality=80&size=b9999_10000&sec=1585999647247&di=7e9061211c23e3ed9f0c4375bb3822dc&imgtype=0&src=http%3A%2F%2Fi1.hdslb.com%2Fbfs%2Farchive%2F04d8cda08e170f4a58c18c45a93c539375c22162.jpg\")\n            no-repeat;\n          background-size: cover;\n          margin-top: 10px;\n          margin-bottom: 10px;\n        }\n        .user-btn {\n          font-size: 12px;\n          display: flex;\n          display: -webkit-flex;\n          .btn-item {\n            display: flex;\n            display: -webkit-flex;\n            align-items: center;\n            margin-right: 10px;\n            .iconfont{\n              margin-right: 4px;\n            }\n          }\n        }\n      }\n    }\n    .comment {\n      width: 100%;\n      height: auto;\n      position: relative;\n      .c-left {\n        .line {\n          border-right: 2px solid #edeff1;\n          // width: 20px;\n          height: 100%;\n          position: absolute;\n          left: 20px;\n        }\n        .c-arrow {\n          display: flex;\n          display: -webkit-flex;\n          position: absolute;\n          z-index: 2;\n          flex-direction: column;\n          left: 12px;\n          background: #ffffff;\n          padding-bottom: 5px;\n        }\n      }\n      .c-right {\n        margin-left: 40px;\n        padding-right: 10px;\n        .c-user-info {\n          margin-bottom: 10px;\n          .name {\n            color: #1c1c1c;\n            font-size: 12px;\n            font-weight: 400;\n            line-height: 16px;\n          }\n          .num {\n            padding-left: 4px;\n            font-size: 12px;\n            font-weight: 400;\n            line-height: 16px;\n            color: #7c7c7c;\n          }\n        }\n        .c-content {\n          font-family: Noto Sans, Arial, sans-serif;\n          font-size: 14px;\n          font-weight: 400;\n          line-height: 21px;\n          word-break: break-word;\n          color: rgb(26, 26, 27);\n        }\n      }\n    }\n  }\n  .right {\n    flex-grow: 0;\n    width: 312px;\n    margin-top: 32px;\n    .topic-info {\n      width: 100%;\n      // padding: 12px;\n      cursor: pointer;\n      background-color: #ffffff;\n      color: #1a1a1b;\n      border: 1px solid #cccccc;\n      border-radius: 4px;\n      overflow: visible;\n      word-wrap: break-word;\n      padding-bottom: 30px;\n      .t-header {\n        width: 100%;\n        height: 34px;\n        background: #0079d3;\n      }\n      .t-info {\n        padding: 0 12px;\n        display: flex;\n        display: -webkit-flex;\n        width: 100%;\n        height: 54px;\n        align-items: center;\n        .avatar {\n          width: 54px;\n          height: 54px;\n          background: url(\"../assets/images/avatar.png\") no-repeat;\n          background-size: cover;\n          margin-right: 10px;\n        }\n        .topic-name {\n          height: 100%;\n          line-height: 54px;\n          font-size: 16px;\n          font-weight: 500;\n        }\n      }\n      .t-desc {\n        font-family: Noto Sans, Arial, sans-serif;\n        font-size: 14px;\n        line-height: 21px;\n        font-weight: 400;\n        word-wrap: break-word;\n        margin-bottom: 8px;\n        padding: 0 12px;\n      }\n      .t-num {\n        padding: 0 12px 20px 12px;\n        display: flex;\n        display: -webkit-flex;\n        align-items: center;\n        border-bottom: 1px solid #edeff1;\n        .t-num-item {\n          list-style: none;\n          display: flex;\n          display: -webkit-flex;\n          flex-direction: column;\n          width: 50%;\n          .number {\n            font-size: 16px;\n            font-weight: 500;\n            line-height: 20px;\n          }\n          .unit {\n            font-size: 12px;\n            font-weight: 500;\n            line-height: 16px;\n            word-break: break-word;\n          }\n        }\n      }\n      .date {\n        font-family: Noto Sans, Arial, sans-serif;\n        font-size: 14px;\n        font-weight: 400;\n        line-height: 18px;\n        margin-top: 20px;\n        padding: 0 12px;\n      }\n      .topic-btn {\n        width: 286px;\n        height: 34px;\n        line-height: 34px;\n        color: #ffffff;\n        margin: 12px auto 0 auto;\n        background: #003f6d;\n        border-radius: 4px;\n        box-sizing: border-box;\n        margin-left: 13px;\n      }\n    }\n  }\n}\n</style>"]}]}