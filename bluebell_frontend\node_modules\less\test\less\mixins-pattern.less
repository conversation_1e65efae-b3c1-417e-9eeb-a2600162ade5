.mixin (...) {
  variadic: true;
}
.mixin (@a...) {
  named-variadic: true;
}
.mixin () {
    zero: 0;
}
.mixin (@a: 1px) {
    one: 1;
}
.mixin (@a) {
    one-req: 1;
}
.mixin (@a: 1px, @b: 2px) {
    two: 2;
}

.mixin (@a, @b, @c) {
    three-req: 3;
}

.mixin (@a: 1px, @b: 2px, @c: 3px) {
    three: 3;
}

.zero {
    .mixin();
}

.one {
    .mixin(1);
}

.two {
    .mixin(1, 2);
}

.three {
    .mixin(1, 2, 3);
}

//

.mixout ('left') {
    left: 1;
}

.mixout ('right') {
    right: 1;
}

.left {
    .mixout('left');
}
.right {
    .mixout('right');
}

//

.border (@side, @width) {
    color: black;
    .border-side(@side, @width);
}
.border-side (left, @w) {
    border-left: @w;
}
.border-side (right, @w) {
    border-right: @w;
}

.border-right {
    .border(right, 4px);
}
.border-left {
    .border(left, 4px);
}

//


.border-radius (@r) {
    both: (@r * 10);
}
.border-radius (@r, left) {
    left: @r;
}
.border-radius (@r, right) {
    right: @r;
}

.only-right {
    .border-radius(33, right);
}
.only-left {
    .border-radius(33, left);
}
.left-right {
    .border-radius(33);
}
