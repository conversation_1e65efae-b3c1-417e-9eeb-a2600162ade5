
// test late parsing
@cols: 1, 2;

.a(@i: length(@cols)) when (@i > 0) {
  @divider: e(extract(@cols, @i));
}
.a;

.b(@j: 1) when (@j < length(@cols)) {
  @divider: e(extract(@cols, @j));
}
.b;

// simple array/list:

.multiunit {
  @v: abc "abc" 1 1px 1% #123;
  length: length(@v);
  extract: extract(@v, 1) extract(@v, 2) extract(@v, 3) extract(@v, 4) extract(@v, 5) extract(@v, 6);
}

.incorrect-index {
    @v1: a  b  c;
    @v2: a, b, c;
    v1: extract(@v1,  5);
    v2: extract(@v2, -2);
}

.scalar {
    @var:          variable;
    var-value:     extract(@var, 1);
    var-length:    length(@var);
    ill-index:     extract(@var, 2);

    name-value:    extract(name, 1);
    string-value:  extract("string", 1);
    number-value:  extract(12345678, 1);
    color-value:   extract(blue, 1);
    rgba-value:    extract(rgba(80, 160, 240, 0.67), 1);
    empty-value:   extract(~'', 1);

    name-length:   length(name);
    string-length: length("string");
    number-length: length(12345678);
    color-length:  length(blue);
    rgba-length:   length(rgba(80, 160, 240, 0.67));
    empty-length:  length(~'');
}

.mixin-arguments {
    .mixin-args(a b c d);
    .mixin-args(a, b, c, d);
    .mixin-args(1; 2; 3; 4);
}

.mixin-args(@value) {
    &-1 {
        length:  length(@value);
        extract: extract(@value, 3) ~"|" extract(@value, 2) ~"|" extract(@value, 1);
    }
}

.mixin-args(...) {
    &-2 {
        length:  length(@arguments);
        extract: extract(@arguments, 3) ~"|" extract(@arguments, 2) ~"|" extract(@arguments, 1);
    }
}

.mixin-args(@values...) {
    &-3 {
        length:  length(@values);
        extract: extract(@values, 3) ~"|" extract(@values, 2) ~"|" extract(@values, 1);
    }
}

.mixin-args(@head, @tail...) {
    &-4 {
        length:  length(@tail);
        extract: extract(@tail, 2) ~"|" extract(@tail, 1);
    }
}

// "multidimensional" array/list

.md-space-comma {
    @v: a b c, 1 2 3, "x" "y" "z";
    length-1:  length(@v);
    extract-1: extract(@v, 2);
    length-2:  length(extract(@v, 2));
    extract-2: extract(extract(@v, 2), 2);

    &-as-args {.mixin-args(a b c, 1 2 3, "x" "y" "z")}
}

.md-cat-space-comma {
    @a: a b c;
    @b: 1 2 3;
    @c: "x" "y" "z";
    @v: @a, @b, @c;
    length-1:  length(@v);
    extract-1: extract(@v, 2);
    length-2:  length(extract(@v, 2));
    extract-2: extract(extract(@v, 2), 2);

    &-as-args {.mixin-args(@a, @b, @c)}
}

.md-cat-comma-space {
    @a: a, b, c;
    @b: 1, 2, 3;
    @c: "x", "y", "z";
    @v: @a @b @c;
    length-1:  length(@v);
    extract-1: extract(@v, 2);
    length-2:  length(extract(@v, 2));
    extract-2: extract(extract(@v, 2), 2);

    &-as-args {.mixin-args(@a @b @c)}
}

.md-3D {
    @a: a b c d, 1 2 3 4;
    @b: 5 6 7 8, e f g h;
    .3D(@a, @b);

    .3D(...) {

        @v1:       @arguments;
        length-1:  length(@v1);
        extract-1: extract(@v1, 1);

        @v2:       extract(@v1, 2);
        length-2:  length(@v2);
        extract-2: extract(@v2, 1);

        @v3:       extract(@v2, 1);
        length-3:  length(@v3);
        extract-3: extract(@v3, 3);

        @v4:       extract(@v3, 4);
        length-4:  length(@v4);
        extract-4: extract(@v4, 1);
    }
}
