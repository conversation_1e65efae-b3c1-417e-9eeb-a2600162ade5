{"name": "espree", "description": "An Esprima-compatible JavaScript parser built on Acorn", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/eslint/espree", "main": "espree.js", "version": "5.0.1", "files": ["lib", "espree.js"], "engines": {"node": ">=6.0.0"}, "repository": "eslint/espree", "bugs": {"url": "http://github.com/eslint/espree.git"}, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^6.0.7", "acorn-jsx": "^5.0.0", "eslint-visitor-keys": "^1.0.0"}, "devDependencies": {"browserify": "^7.0.0", "chai": "^1.10.0", "eslint": "^5.7.0", "eslint-config-eslint": "^5.0.1", "eslint-plugin-node": "^8.0.0", "eslint-release": "^1.0.0", "esprima": "latest", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "json-diff": "~0.3.1", "leche": "^1.0.1", "mocha": "^2.0.1", "nyc": "^13.0.1", "regenerate": "~0.5.4", "shelljs": "^0.3.0", "shelljs-nodecli": "^0.1.1", "unicode-6.3.0": "~0.1.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax", "acorn"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node Makefile.js test", "lint": "node Makefile.js lint", "browserify": "node Makefile.js browserify", "generate-release": "eslint-generate-release", "generate-alpharelease": "eslint-generate-prerelease alpha", "generate-betarelease": "eslint-generate-prerelease beta", "generate-rcrelease": "eslint-generate-prerelease rc", "publish-release": "eslint-publish-release"}}