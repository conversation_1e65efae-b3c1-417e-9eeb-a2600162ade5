<!DOCTYPE html><html><head><script>"use strict";!function(){var i=(window.location.pathname.match(/^(\/(?:ipfs|ipns)\/[^/]+)/)||[])[1]||"";window.__GATSBY_IPFS_PATH_PREFIX__=i}();</script><meta charSet="utf-8"/><meta http-equiv="x-ua-compatible" content="ie=edge"/><meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"/><style data-href="../../styles.e93b5499b63484750fba.css">code[class*=language-],pre[class*=language-]{color:#ccc;background:none;font-family:Consolas,Monaco,Andale Mono,Ubuntu Mono,monospace;font-size:1em;text-align:left;white-space:pre;word-spacing:normal;word-break:normal;word-wrap:normal;line-height:1.5;-moz-tab-size:4;-o-tab-size:4;tab-size:4;-webkit-hyphens:none;-ms-hyphens:none;hyphens:none}pre[class*=language-]{padding:1em;margin:.5em 0;overflow:auto}:not(pre)>code[class*=language-],pre[class*=language-]{background:#2d2d2d}:not(pre)>code[class*=language-]{padding:.1em;border-radius:.3em;white-space:normal}.token.block-comment,.token.cdata,.token.comment,.token.doctype,.token.prolog{color:#999}.token.punctuation{color:#ccc}.token.attr-name,.token.deleted,.token.namespace,.token.tag{color:#e2777a}.token.function-name{color:#6196cc}.token.boolean,.token.function,.token.number{color:#f08d49}.token.class-name,.token.constant,.token.property,.token.symbol{color:#f8c555}.token.atrule,.token.builtin,.token.important,.token.keyword,.token.selector{color:#cc99cd}.token.attr-value,.token.char,.token.regex,.token.string,.token.variable{color:#7ec699}.token.entity,.token.operator,.token.url{color:#67cdcc}.token.bold,.token.important{font-weight:700}.token.italic{font-style:italic}.token.entity{cursor:help}.token.inserted{color:green}a,abbr,acronym,address,applet,article,aside,audio,b,big,blockquote,body,canvas,caption,center,cite,code,dd,del,details,dfn,div,dl,dt,em,embed,fieldset,figcaption,figure,footer,form,h1,h2,h3,h4,h5,h6,header,hgroup,html,i,iframe,img,ins,kbd,label,legend,li,mark,menu,nav,object,ol,output,p,pre,q,ruby,s,samp,section,small,span,strike,strong,sub,summary,sup,table,tbody,td,tfoot,th,thead,time,tr,tt,u,ul,var,video{margin:0;padding:0;border:0;font-size:100%;font:inherit;vertical-align:baseline}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:after,blockquote:before,q:after,q:before{content:"";content:none}table{border-collapse:collapse;border-spacing:0}[hidden]{display:none}html{font-family:Poppins,sans-serif}*{box-sizing:border-box}li,p{font-size:15px;line-height:1.7;font-weight:300}p,ul{padding:10px 0}strong{font-weight:700;color:#c3f}li{list-style-type:disc;list-style-position:inside;padding:8px 0}.documentation h1{font-size:42px;font-weight:600;padding:30px 0 10px}.documentation h2{font-size:22px;font-weight:300}.documentation h3{color:#c3f;font-size:22px;padding:30px 0 5px;font-weight:500}.documentation h4{font-weight:600;padding:20px 0 5px}.documentation p{display:inline-block}:not(pre)>code[class*=language-],pre[class*=language-]{border-radius:4px;background-color:#413844;font-size:13px}:not(pre)>code[class*=language-text]{background-color:rgba(204,139,216,.1);color:#413844;padding:2px 6px;border-radius:0;font-size:14px;font-weight:700;border-radius:1px;display:inline-block}.documentation a,a>code[class*=language-text]{color:#fb3b49;font-weight:600}p>code[class*=language-text]{display:inline-block}.documentation h1:before{content:url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' viewBox='0 0 27 26'%3E%3Cdefs%3E%3ClinearGradient id='a' x1='18.13' x2='25.6' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='.37' stop-color='%23fb8719'/%3E%3Cstop offset='.51' stop-color='%23fa8420'/%3E%3Cstop offset='.61' stop-color='%23f9802c'/%3E%3Cstop offset='.69' stop-color='%23f7793d'/%3E%3Cstop offset='.76' stop-color='%23f47053'/%3E%3Cstop offset='.82' stop-color='%23f1656e'/%3E%3Cstop offset='.87' stop-color='%23ed578f'/%3E%3Cstop offset='.92' stop-color='%23e948b5'/%3E%3Cstop offset='.97' stop-color='%23e437de'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='b' x1='17.89' x2='25.84' y1='13.48' y2='13.48' gradientUnits='userSpaceOnUse'%3E%3Cstop offset='0' stop-color='%23fb8817'/%3E%3Cstop offset='1' stop-color='%23e02aff'/%3E%3C/linearGradient%3E%3ClinearGradient id='c' x1='1' x2='18.69' y1='17.84' y2='17.84' xlink:href='%23a'/%3E%3ClinearGradient id='d' x1='.76' x2='18.93' y1='17.84' y2='17.84' xlink:href='%23b'/%3E%3ClinearGradient id='e' x1='1' x2='20.48' y1='7.33' y2='7.33' xlink:href='%23a'/%3E%3ClinearGradient id='f' x1='.76' x2='20.72' y1='7.33' y2='7.33' xlink:href='%23b'/%3E%3C/defs%3E%3Cpath fill='url(%23a)' stroke='url(%23b)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.34-.41L25 14.06l-5-11a.28.28 0 11.5-.23L25.58 14a.28.28 0 010 .28l-6.91 9.9a.28.28 0 01-.14.06z'/%3E%3Cpath fill='url(%23c)' stroke='url(%23d)' stroke-miterlimit='10' stroke-width='.48' d='M18.53 24.24a.28.28 0 01-.14 0l-12-1.15a.28.28 0 01-.23-.09L1 11.81a.28.28 0 11.5-.23l5.07 11L18 23.68 13 13a.28.28 0 11.5-.23l5.12 11.12a.28.28 0 01-.09.35z'/%3E%3Cpath fill='url(%23e)' stroke='url(%23f)' stroke-miterlimit='10' stroke-width='.48' d='M13.4 13.12a.25.25 0 01-.14 0L1.25 12a.28.28 0 01-.2-.44L8 1.64a.28.28 0 01.25-.12l12 1.18a.28.28 0 01.2.44L13.51 13a.25.25 0 01-.11.12z'/%3E%3C/svg%3E");position:relative;display:inline-block;padding-right:8px;top:3px;width:28px}.active-sidebar-link{background-color:#ffebff}.active-navbar-link{border-bottom:3px solid #c3f}.header-link-class{margin-left:-24px}.disabled-body{overflow:hidden}</style><meta name="generator" content="Gatsby 2.18.18"/><title data-react-helmet="true"></title><style data-styled="UihHA jAtLxz bCnUTx bAGJfc hJcdbU kOyZtC eCQAUi fsnHHg bXQeSB dsecBh iPgskl bNiGAM gJQTGP fMOzaj" data-styled-version="4.4.1">
/* sc-component-id: links__NavLink-sc-19vgq0o-1 */
.kOyZtC{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .kOyZtC:hover{opacity:.5;}
/* sc-component-id: links__BasicNavLink-sc-19vgq0o-2 */
.eCQAUi{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#231f20;-webkit-transition:opacity .5s;transition:opacity .5s;margin:0 10px;} .eCQAUi:hover{opacity:.5;}
/* sc-component-id: links__SidebarLink-sc-19vgq0o-3 */
.iPgskl{font-weight:500;-webkit-text-decoration:none;text-decoration:none;-webkit-letter-spacing:.3px;-moz-letter-spacing:.3px;-ms-letter-spacing:.3px;letter-spacing:.3px;font-size:14px;color:#fb3b49;padding:10px;-webkit-transition:background-color .3s;transition:background-color .3s;} .iPgskl:hover{background-color:#ffebff;}
/* sc-component-id: Accordion__SectionButton-i8yhwx-0 */
.dsecBh{outline:none;background-color:transparent;cursor:pointer;color:red;border:none;font-size:18px;font-weight:bold;padding:5px 0;-webkit-transition:opacity .5s;transition:opacity .5s;} .dsecBh:after{background:center / contain no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNi41IDEwIj48ZGVmcz48c3R5bGU+LmNscy0xe2ZpbGw6I2ZiM2I0OTt9PC9zdHlsZT48L2RlZnM+PHRpdGxlPnVwLWNhcnJvdDwvdGl0bGU+PHBhdGggY2xhc3M9ImNscy0xIiBkPSJNOC4yNS44NWExLjE1LDEuMTUsMCwwLDAtLjgxLjM0bC02LDZBMS4xNSwxLjE1LDAsMCwwLDMuMDYsOC44MUw4LjI1LDMuNjNsNS4xOSw1LjE5YTEuMTUsMS4xNSwwLDAsMCwxLjYzLTEuNjNsLTYtNkExLjE1LDEuMTUsMCwwLDAsOC4yNS44NVoiLz48L3N2Zz4=);content:'';height:11px;width:28px;display:inline-block;} .dsecBh:hover{opacity:.6;}
/* sc-component-id: DocLinks__LinkDesc-sc-1vrw6od-0 */
.bNiGAM{font-size:11px;line-height:1.5;text-transform:lowercase;display:block;font-weight:400;color:#767676;}
/* sc-component-id: Sidebar__Container-gs0c67-0 */
.bXQeSB{border-right:1px solid #86838333;padding:30px;height:100vh;display:none;width:380px;position:-webkit-sticky;position:sticky;overflow:scroll;padding-bottom:200px;top:54px;background-color:#ffffff;} @media screen and (min-width:48em){.bXQeSB{display:block;}}
/* sc-component-id: navbar__Container-kjuegf-0 */
.UihHA{width:100%;border-bottom:1px solid #86838333;position:-webkit-sticky;position:sticky;top:0;background-color:#ffffff;z-index:1;}
/* sc-component-id: navbar__Inner-kjuegf-1 */
.jAtLxz{border-top:3px solid;border-image:linear-gradient(139deg,#fb8817,#ff4b01,#c12127,#e02aff) 3;margin:auto;height:53px;padding:0 30px;-webkit-align-items:center;-webkit-box-align:center;-ms-flex-align:center;align-items:center;width:100%;}
/* sc-component-id: navbar__Logo-kjuegf-2 */
.bAGJfc{width:120px;padding:0px 5px;height:18px;vertical-align:middle;display:inline-block;-webkit-transition:opacity .5s;transition:opacity .5s;} .bAGJfc:hover{opacity:.8;}
/* sc-component-id: navbar__Links-kjuegf-3 */
.hJcdbU{display:none;} @media screen and (min-width:48em){.hJcdbU{display:block;margin-left:auto;}}
/* sc-component-id: navbar__Heart-kjuegf-4 */
.bCnUTx{font-size:15px;display:inline-block;}
/* sc-component-id: navbar__Hamburger-kjuegf-5 */
.fsnHHg{border:none;background:center no-repeat url(data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIiB2aWV3Qm94PSIwIDAgMzUgMjMiPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudCk7fS5jbHMtMntmaWxsOnVybCgjbGluZWFyLWdyYWRpZW50LTIpO30uY2xzLTN7ZmlsbDp1cmwoI2xpbmVhci1ncmFkaWVudC0zKTt9PC9zdHlsZT48bGluZWFyR3JhZGllbnQgaWQ9ImxpbmVhci1ncmFkaWVudCIgeTE9IjIiIHgyPSIzNSIgeTI9IjIiIGdyYWRpZW50VW5pdHM9InVzZXJTcGFjZU9uVXNlIj48c3RvcCBvZmZzZXQ9IjAiIHN0b3AtY29sb3I9IiNmYjg4MTciLz48c3RvcCBvZmZzZXQ9IjEiIHN0b3AtY29sb3I9IiNlMDJhZmYiLz48L2xpbmVhckdyYWRpZW50PjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTIiIHkxPSIxMS41IiB5Mj0iMTEuNSIgeGxpbms6aHJlZj0iI2xpbmVhci1ncmFkaWVudCIvPjxsaW5lYXJHcmFkaWVudCBpZD0ibGluZWFyLWdyYWRpZW50LTMiIHkxPSIyMSIgeTI9IjIxIiB4bGluazpocmVmPSIjbGluZWFyLWdyYWRpZW50Ii8+PC9kZWZzPjx0aXRsZT5oYW1idXJnZXI8L3RpdGxlPjxyZWN0IGNsYXNzPSJjbHMtMSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjxyZWN0IGNsYXNzPSJjbHMtMiIgeT0iOS41IiB3aWR0aD0iMzUiIGhlaWdodD0iNCIgcng9IjIiIHJ5PSIyIi8+PHJlY3QgY2xhc3M9ImNscy0zIiB5PSIxOSIgd2lkdGg9IjM1IiBoZWlnaHQ9IjQiIHJ4PSIyIiByeT0iMiIvPjwvc3ZnPg==);height:30px;width:30px;display:block;margin-left:auto;-webkit-transition:opacity .5s;transition:opacity .5s;cursor:pointer;} .fsnHHg:hover{opacity:.6;} @media screen and (min-width:48em){.fsnHHg{display:none;}}
/* sc-component-id: FoundTypo__Container-sc-1e373sc-0 */
.fMOzaj{margin:80px 0;border-top:1px solid black;padding:20px 0;}
/* sc-component-id: Page__Content-sc-4b62ym-0 */
.gJQTGP{max-width:760px;margin:auto;padding:0 30px 120px;}</style><link rel="icon" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="manifest" href="../../manifest.webmanifest"/><meta name="theme-color" content="#663399"/><link rel="apple-touch-icon" sizes="48x48" href="../../icons/icon-48x48.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="72x72" href="../../icons/icon-72x72.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="96x96" href="../../icons/icon-96x96.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="144x144" href="../../icons/icon-144x144.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="192x192" href="../../icons/icon-192x192.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="256x256" href="../../icons/icon-256x256.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="384x384" href="../../icons/icon-384x384.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="apple-touch-icon" sizes="512x512" href="../../icons/icon-512x512.png?v=7a2e468321d0881d02a038e839dfc4a3"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2"/><link rel="preload" as="font" type="font/woff2" crossorigin="anonymous" href="../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2"/><style type="text/css">@font-face{font-family:Poppins;font-style:normal;font-weight:300;src:local('Poppins Light'),local('Poppins-Light'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLDz8Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:400;src:local('Poppins Regular'),local('Poppins-Regular'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiEyp8kv8JHgFVrJJfedA.woff) format('woff');font-display: swap;}@font-face{font-family:Poppins;font-style:normal;font-weight:500;src:local('Poppins Medium'),local('Poppins-Medium'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlFQ.woff2) format('woff2'),url(../../google-fonts/s/poppins/v9/pxiByp8kv8JHgFVrLGT9Z1xlEw.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:400;src:local('Inconsolata Regular'),local('Inconsolata-Regular'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5qg.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldKNThLqRwH-OJ1UHjlKGlZ5q4.woff) format('woff');font-display: swap;}@font-face{font-family:Inconsolata;font-style:normal;font-weight:700;src:local('Inconsolata Bold'),local('Inconsolata-Bold'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_w.woff2) format('woff2'),url(../../google-fonts/s/inconsolata/v18/QldXNThLqRwH-OJ1UHjlKGHiw71p5_o.woff) format('woff');font-display: swap;}</style><style type="text/css">
    .header-link-class.before {
      position: absolute;
      top: 0;
      left: 0;
      transform: translateX(-100%);
      padding-right: 4px;
    }
    .header-link-class.after {
      display: inline-block;
      padding-left: 4px;
    }
    h1 .header-link-class svg,
    h2 .header-link-class svg,
    h3 .header-link-class svg,
    h4 .header-link-class svg,
    h5 .header-link-class svg,
    h6 .header-link-class svg {
      visibility: hidden;
    }
    h1:hover .header-link-class svg,
    h2:hover .header-link-class svg,
    h3:hover .header-link-class svg,
    h4:hover .header-link-class svg,
    h5:hover .header-link-class svg,
    h6:hover .header-link-class svg,
    h1 .header-link-class:focus svg,
    h2 .header-link-class:focus svg,
    h3 .header-link-class:focus svg,
    h4 .header-link-class:focus svg,
    h5 .header-link-class:focus svg,
    h6 .header-link-class:focus svg {
      visibility: visible;
    }
  </style><script>
    document.addEventListener("DOMContentLoaded", function(event) {
      var hash = window.decodeURI(location.hash.replace('#', ''))
      if (hash !== '') {
        var element = document.getElementById(hash)
        if (element) {
          var offset = element.offsetTop
          // Wait for the browser to finish rendering before scrolling.
          setTimeout((function() {
            window.scrollTo(0, offset - 100)
          }), 0)
        }
      }
    })
  </script><link as="script" rel="preload" href="../../webpack-runtime-b622568e0ef6e093f777.js"/><link as="script" rel="preload" href="../../styles-de5e304580bcba768a01.js"/><link as="script" rel="preload" href="../../commons-4df35f6dbd2fdc25d817.js"/><link as="script" rel="preload" href="../../app-041f7e4f56e7debd8d98.js"/><link as="script" rel="preload" href="../../component---src-templates-page-js-7faf8ceb01991e80d244.js"/><link as="fetch" rel="preload" href="../../page-data/configuring-npm/folders/page-data.json" crossorigin="anonymous"/></head><body><div id="___gatsby"><div style="outline:none" tabindex="-1" role="group" id="gatsby-focus-wrapper"><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="navbar__Container-kjuegf-0 UihHA css-4cffwv"><div class="navbar__Inner-kjuegf-1 jAtLxz css-4cffwv"><a href="../../"><style data-emotion-css="26z63x">.css-26z63x{box-sizing:border-box;margin:0;min-width:0;margin-left:4px;margin-right:24px;}</style><div class="navbar__Heart-kjuegf-4 bCnUTx css-26z63x">❤</div><style data-emotion-css="9taffg">.css-9taffg{box-sizing:border-box;margin:0;min-width:0;max-width:100%;height:auto;}</style><img src="data:image/svg+xml;base64,PHN2ZyBpZD0iTGF5ZXJfMSIgZGF0YS1uYW1lPSJMYXllciAxIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDcgMTciPjxkZWZzPjxzdHlsZT4uY2xzLTF7ZmlsbDojMjMxZjIwO30uY2xzLTJ7ZmlsbDpub25lO308L3N0eWxlPjwvZGVmcz48dGl0bGU+Y2xpLWxvZ288L3RpdGxlPjxwYXRoIGNsYXNzPSJjbHMtMSIgZD0iTS41NCwxMy40aDYuNFYzLjY3aDMuMlYxMy40aDMuMlYuNDJILjU0Wk0zMS4yNi40MnYxM2g2LjRWMy42N2gzLjJWMTMuNGgzLjJWMy42N2gzLjE5VjEzLjRoMy4yVi40MlptLTksMy4yNWgzLjJ2Ni40OUgyMi4zWm0tNi40LDEzaDYuNFYxMy40aDYuNFYuNDJIMTUuOVoiLz48cmVjdCBjbGFzcz0iY2xzLTIiIHg9IjAuNTQiIHk9IjAuNDIiIHdpZHRoPSI0OS45MSIgaGVpZ2h0PSIxNi4yMiIvPjxwb2x5Z29uIGNsYXNzPSJjbHMtMSIgcG9pbnRzPSI2NS41OCAzLjU2IDY1LjU4IDkuODYgNzEuNjYgOS44NiA3MS42NiAxMy4wMiA2NS40NCAxMy4wMiA1OS4yIDEzLjA0IDU5LjIyIDAuNDEgNzEuNjYgMC40MSA3MS42NiAzLjU0IDY1LjU4IDMuNTYiLz48cG9seWdvbiBjbGFzcz0iY2xzLTEiIHBvaW50cz0iODAuNjIgMTAuMjMgODAuNjIgMC4zNiA3NC4yMyAwLjM2IDc0LjIzIDEzLjMgNzYuOTIgMTMuMyA4MC42MiAxMy4zIDg2LjQ3IDEzLjMgODYuNDcgMTAuMjMgODAuNjIgMTAuMjMiLz48cmVjdCBjbGFzcz0iY2xzLTEiIHg9IjEwMS4zMiIgeT0iOC4zNyIgd2lkdGg9IjEuOTkiIGhlaWdodD0iOC4yOSIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMTE0LjgzIC04OS43OSkgcm90YXRlKDkwKSIvPjxyZWN0IGNsYXNzPSJjbHMtMSIgeD0iODguMzMiIHk9IjAuMzYiIHdpZHRoPSI2LjM5IiBoZWlnaHQ9IjEyLjk0Ii8+PC9zdmc+" class="navbar__Logo-kjuegf-2 bAGJfc css-9taffg"/></a><ul class="navbar__Links-kjuegf-3 hJcdbU"><a class="links__NavLink-sc-19vgq0o-1 kOyZtC" href="../../cli-commands/npm/index.html">docs</a><a href="https://www.npmjs.com/" class="links__BasicNavLink-sc-19vgq0o-2 eCQAUi">npmjs.org</a></ul><button class="navbar__Hamburger-kjuegf-5 fsnHHg"></button></div></div><style data-emotion-css="4cffwv">.css-4cffwv{box-sizing:border-box;margin:0;min-width:0;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-4cffwv"><nav class="Sidebar__Container-gs0c67-0 bXQeSB sidebar"><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">cli-commands</button><div><style data-emotion-css="l3rx45">.css-l3rx45{box-sizing:border-box;margin:0;min-width:0;-webkit-flex-direction:column;-ms-flex-direction:column;flex-direction:column;display:-webkit-box;display:-webkit-flex;display:-ms-flexbox;display:flex;}</style><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm">npm<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">javascript package manager</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-access">npm access<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Set access level on published packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-audit">npm audit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run a security audit</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bin">npm bin<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm bin folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bugs">npm bugs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bugs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-build">npm build<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Build a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-bundle">npm bundle<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">REMOVED</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-cache">npm cache<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manipulates packages cache</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ci">npm ci<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-completion">npm completion<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Tab Completion for npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-config">npm config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage the npm configuration files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-dedupe">npm dedupe<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Reduce duplication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-deprecate">npm deprecate<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Deprecate a version of a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-docs">npm docs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Docs for a package in a web browser maybe</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-doctor">npm doctor<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check your environments</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-edit">npm edit<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Edit an installed package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-fund">npm fund<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Retrieve funding information</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help">npm help<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Get help on npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-help-search">npm help-search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search npm help documentation</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-hook">npm hook<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage registry hooks</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-init">npm init<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">create a package.json file</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install">npm install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-ci-test">npm install-ci-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install a project with a clean slate and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-install-test">npm install-test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Install package(s) and run tests</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-link">npm link<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Symlink a package folder</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-logout">npm logout<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Log out of the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ls">npm ls<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">List installed packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-org">npm org<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-outdated">npm outdated<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Check for outdated packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-owner">npm owner<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage package owners</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-pack">npm pack<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Create a tarball from a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-ping">npm ping<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Ping npm registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prefix">npm prefix<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display prefix</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-profile">npm profile<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Change settings on your registry profile</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-prune">npm prune<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove extraneous packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-publish">npm publish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Publish a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-rebuild">npm rebuild<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Rebuild a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-repo">npm repo<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Open package repository page in the browser</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-restart">npm restart<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Restart a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-root">npm root<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm root</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-run-script">npm run-script<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Run arbitrary package scripts</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-search">npm search<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Search for packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-shrinkwrap">npm shrinkwrap<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Lock down dependency versions for publication</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-star">npm star<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Mark your favorite packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stars">npm stars<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View packages marked as favorites</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-start">npm start<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Start a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-stop">npm stop<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Stop a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-team">npm team<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage organization teams and team memberships</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-test">npm test<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Test a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-token">npm token<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Manage your authentication tokens</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-uninstall">npm uninstall<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-unpublish">npm unpublish<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Remove a package from the registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-update">npm update<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Update a package</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-version">npm version<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Bump a package version</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-view">npm view<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">View registry info</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../cli-commands/npm-whoami">npm whoami<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Display npm username</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">configuring-npm</button><div><div class="css-l3rx45"><a aria-current="page" class="links__SidebarLink-sc-19vgq0o-3 iPgskl active-sidebar-link" href="../../configuring-npm/folders">folders<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Folder Structures Used by npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/install">install<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Download and install node and npm</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/npmrc">npmrc<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The npm config files</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-lock-json">package-lock.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A manifestation of the manifest</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-locks">package-locks<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">An explanation of npm lockfiles</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/package-json">package.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Specifics of npm&#x27;s package.json handling</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../configuring-npm/shrinkwrap-json">shrinkwrap.json<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">A publishable lockfile</span></a></div></div></div><div><button class="Accordion__SectionButton-i8yhwx-0 dsecBh">using-npm</button><div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/config">config<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">More than you probably want to know about npm configuration</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/developers">developers<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Developer Guide</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/disputes">disputes<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Handling Module Name Disputes</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/orgs">orgs<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Working with Teams &amp; Orgs</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/registry">registry<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">The JavaScript Package Registry</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/removal">removal<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Cleaning the Slate</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scope">scope<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">Scoped packages</span></a></div><div class="css-l3rx45"><a class="links__SidebarLink-sc-19vgq0o-3 iPgskl" href="../../using-npm/scripts">scripts<span class="DocLinks__LinkDesc-sc-1vrw6od-0 bNiGAM">How npm handles the &quot;scripts&quot; field</span></a></div></div></div></nav><style data-emotion-css="16vu25q">.css-16vu25q{box-sizing:border-box;margin:0;min-width:0;width:100%;}</style><div class="css-16vu25q"><div class="Page__Content-sc-4b62ym-0 gJQTGP documentation"><div><h1 id="folders5" style="position:relative;"><a href="#folders5" aria-label="folders5 permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>folders</h1>
<h2 id="folder-structures-used-by-npm" style="position:relative;"><a href="#folder-structures-used-by-npm" aria-label="folder structures used by npm permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Folder Structures Used by npm</h2>
<h3 id="description" style="position:relative;"><a href="#description" aria-label="description permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Description</h3>
<p>npm puts various things on your computer.  That's its job.</p>
<p>This document will tell you what it puts where.</p>
<h4 id="tldr" style="position:relative;"><a href="#tldr" aria-label="tldr permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>tl;dr</h4>
<ul>
<li>Local install (default): puts stuff in <code class="language-text">./node_modules</code> of the current
package root.</li>
<li>Global install (with <code class="language-text">-g</code>): puts stuff in /usr/local or wherever node
is installed.</li>
<li>Install it <strong>locally</strong> if you're going to <code class="language-text">require()</code> it.</li>
<li>Install it <strong>globally</strong> if you're going to run it on the command line.</li>
<li>If you need both, then install it in both places, or use <code class="language-text">npm link</code>.</li>
</ul>
<h4 id="prefix-configuration" style="position:relative;"><a href="#prefix-configuration" aria-label="prefix configuration permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>prefix Configuration</h4>
<p>The <code class="language-text">prefix</code> config defaults to the location where node is installed.
On most systems, this is <code class="language-text">/usr/local</code>. On Windows, it's <code class="language-text">%AppData%\npm</code>.
On Unix systems, it's one level up, since node is typically installed at
<code class="language-text">{prefix}/bin/node</code> rather than <code class="language-text">{prefix}/node.exe</code>.</p>
<p>When the <code class="language-text">global</code> flag is set, npm installs things into this prefix.
When it is not set, it uses the root of the current package, or the
current working directory if not in a package already.</p>
<h4 id="node-modules" style="position:relative;"><a href="#node-modules" aria-label="node modules permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Node Modules</h4>
<p>Packages are dropped into the <code class="language-text">node_modules</code> folder under the <code class="language-text">prefix</code>.
When installing locally, this means that you can
<code class="language-text">require(&quot;packagename&quot;)</code> to load its main module, or
<code class="language-text">require(&quot;packagename/lib/path/to/sub/module&quot;)</code> to load other modules.</p>
<p>Global installs on Unix systems go to <code class="language-text">{prefix}/lib/node_modules</code>.
Global installs on Windows go to <code class="language-text">{prefix}/node_modules</code> (that is, no
<code class="language-text">lib</code> folder.)</p>
<p>Scoped packages are installed the same way, except they are grouped together
in a sub-folder of the relevant <code class="language-text">node_modules</code> folder with the name of that
scope prefix by the @ symbol, e.g. <code class="language-text">npm install @myorg/package</code> would place
the package in <code class="language-text">{prefix}/node_modules/@myorg/package</code>. See <a href="../../using-npm/scope"><code class="language-text">scope</code></a> for more details.</p>
<p>If you wish to <code class="language-text">require()</code> a package, then install it locally.</p>
<h4 id="executables" style="position:relative;"><a href="#executables" aria-label="executables permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Executables</h4>
<p>When in global mode, executables are linked into <code class="language-text">{prefix}/bin</code> on Unix,
or directly into <code class="language-text">{prefix}</code> on Windows.</p>
<p>When in local mode, executables are linked into
<code class="language-text">./node_modules/.bin</code> so that they can be made available to scripts run
through npm.  (For example, so that a test runner will be in the path
when you run <code class="language-text">npm test</code>.)</p>
<h4 id="man-pages" style="position:relative;"><a href="#man-pages" aria-label="man pages permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Man Pages</h4>
<p>When in global mode, man pages are linked into <code class="language-text">{prefix}/share/man</code>.</p>
<p>When in local mode, man pages are not installed.</p>
<p>Man pages are not installed on Windows systems.</p>
<h4 id="cache" style="position:relative;"><a href="#cache" aria-label="cache permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Cache</h4>
<p>See <a href="../../cli-commands/cache"><code class="language-text">npm cache</code></a>.  Cache files are stored in <code class="language-text">~/.npm</code> on Posix, or
<code class="language-text">%AppData%/npm-cache</code> on Windows.</p>
<p>This is controlled by the <code class="language-text">cache</code> configuration param.</p>
<h4 id="temp-files" style="position:relative;"><a href="#temp-files" aria-label="temp files permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Temp Files</h4>
<p>Temporary files are stored by default in the folder specified by the
<code class="language-text">tmp</code> config, which defaults to the TMPDIR, TMP, or TEMP environment
variables, or <code class="language-text">/tmp</code> on Unix and <code class="language-text">c:\windows\temp</code> on Windows.</p>
<p>Temp files are given a unique folder under this root for each run of the
program, and are deleted upon successful exit.</p>
<h3 id="more-information" style="position:relative;"><a href="#more-information" aria-label="more information permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>More Information</h3>
<p>When installing locally, npm first tries to find an appropriate
<code class="language-text">prefix</code> folder.  This is so that <code class="language-text">npm install foo@1.2.3</code> will install
to the sensible root of your package, even if you happen to have <code class="language-text">cd</code>ed
into some other folder.</p>
<p>Starting at the $PWD, npm will walk up the folder tree checking for a
folder that contains either a <code class="language-text">package.json</code> file, or a <code class="language-text">node_modules</code>
folder.  If such a thing is found, then that is treated as the effective
"current directory" for the purpose of running npm commands.  (This
behavior is inspired by and similar to git's .git-folder seeking
logic when running git commands in a working dir.)</p>
<p>If no package root is found, then the current folder is used.</p>
<p>When you run <code class="language-text">npm install foo@1.2.3</code>, then the package is loaded into
the cache, and then unpacked into <code class="language-text">./node_modules/foo</code>.  Then, any of
foo's dependencies are similarly unpacked into
<code class="language-text">./node_modules/foo/node_modules/...</code>.</p>
<p>Any bin files are symlinked to <code class="language-text">./node_modules/.bin/</code>, so that they may
be found by npm scripts when necessary.</p>
<h4 id="global-installation" style="position:relative;"><a href="#global-installation" aria-label="global installation permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Global Installation</h4>
<p>If the <code class="language-text">global</code> configuration is set to true, then npm will
install packages "globally".</p>
<p>For global installation, packages are installed roughly the same way,
but using the folders described above.</p>
<h4 id="cycles-conflicts-and-folder-parsimony" style="position:relative;"><a href="#cycles-conflicts-and-folder-parsimony" aria-label="cycles conflicts and folder parsimony permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Cycles, Conflicts, and Folder Parsimony</h4>
<p>Cycles are handled using the property of node's module system that it
walks up the directories looking for <code class="language-text">node_modules</code> folders.  So, at every
stage, if a package is already installed in an ancestor <code class="language-text">node_modules</code>
folder, then it is not installed at the current location.</p>
<p>Consider the case above, where <code class="language-text">foo -&gt; bar -&gt; baz</code>.  Imagine if, in
addition to that, baz depended on bar, so you'd have:
<code class="language-text">foo -&gt; bar -&gt; baz -&gt; bar -&gt; baz ...</code>.  However, since the folder
structure is: <code class="language-text">foo/node_modules/bar/node_modules/baz</code>, there's no need to
put another copy of bar into <code class="language-text">.../baz/node_modules</code>, since when it calls
require("bar"), it will get the copy that is installed in
<code class="language-text">foo/node_modules/bar</code>.</p>
<p>This shortcut is only used if the exact same
version would be installed in multiple nested <code class="language-text">node_modules</code> folders.  It
is still possible to have <code class="language-text">a/node_modules/b/node_modules/a</code> if the two
"a" packages are different versions.  However, without repeating the
exact same package multiple times, an infinite regress will always be
prevented.</p>
<p>Another optimization can be made by installing dependencies at the
highest level possible, below the localized "target" folder.</p>
<h4 id="example" style="position:relative;"><a href="#example" aria-label="example permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Example</h4>
<p>Consider this dependency graph:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">foo
+-- blerg@1.2.5
+-- bar@1.2.3
<span class="token operator">|</span>   +-- blerg@1.x <span class="token punctuation">(</span>latest<span class="token operator">=</span><span class="token number">1.3</span>.7<span class="token punctuation">)</span>
<span class="token operator">|</span>   +-- baz@2.x
<span class="token operator">|</span>   <span class="token operator">|</span>   <span class="token variable"><span class="token variable">`</span>-- quux@3.x
<span class="token operator">|</span>   <span class="token operator">|</span>       <span class="token variable">`</span></span>-- bar@1.2.3 <span class="token punctuation">(</span>cycle<span class="token punctuation">)</span>
<span class="token operator">|</span>   <span class="token variable"><span class="token variable">`</span>-- asdf@*
<span class="token variable">`</span></span>-- baz@1.2.3
    <span class="token variable"><span class="token variable">`</span>-- quux@3.x
        <span class="token variable">`</span></span>-- bar</code></pre></div>
<p>In this case, we might expect a folder structure like this:</p>
<div class="gatsby-highlight" data-language="bash"><pre class="language-bash"><code class="language-bash">foo
+-- node_modules
    +-- blerg <span class="token punctuation">(</span><span class="token number">1.2</span>.5<span class="token punctuation">)</span> <span class="token operator">&lt;</span>---<span class="token punctuation">[</span>A<span class="token punctuation">]</span>
    +-- bar <span class="token punctuation">(</span><span class="token number">1.2</span>.3<span class="token punctuation">)</span> <span class="token operator">&lt;</span>---<span class="token punctuation">[</span>B<span class="token punctuation">]</span>
    <span class="token operator">|</span>   <span class="token variable"><span class="token variable">`</span>-- node_modules
    <span class="token operator">|</span>       +-- baz <span class="token punctuation">(</span><span class="token number">2.0</span>.2<span class="token punctuation">)</span> <span class="token operator">&lt;</span>---<span class="token punctuation">[</span>C<span class="token punctuation">]</span>
    <span class="token operator">|</span>       <span class="token operator">|</span>   <span class="token variable">`</span></span>-- node_modules
    <span class="token operator">|</span>       <span class="token operator">|</span>       <span class="token variable"><span class="token variable">`</span>-- quux <span class="token punctuation">(</span><span class="token number">3.2</span>.0<span class="token punctuation">)</span>
    <span class="token operator">|</span>       <span class="token variable">`</span></span>-- asdf <span class="token punctuation">(</span><span class="token number">2.3</span>.4<span class="token punctuation">)</span>
    <span class="token variable"><span class="token variable">`</span>-- baz <span class="token punctuation">(</span><span class="token number">1.2</span>.3<span class="token punctuation">)</span> <span class="token operator">&lt;</span>---<span class="token punctuation">[</span>D<span class="token punctuation">]</span>
        <span class="token variable">`</span></span>-- node_modules
            `-- quux <span class="token punctuation">(</span><span class="token number">3.2</span>.0<span class="token punctuation">)</span> <span class="token operator">&lt;</span>---<span class="token punctuation">[</span>E<span class="token punctuation">]</span></code></pre></div>
<p>Since foo depends directly on <code class="language-text">bar@1.2.3</code> and <code class="language-text">baz@1.2.3</code>, those are
installed in foo's <code class="language-text">node_modules</code> folder.</p>
<p>Even though the latest copy of blerg is 1.3.7, foo has a specific
dependency on version 1.2.5.  So, that gets installed at [A].  Since the
parent installation of blerg satisfies bar's dependency on <code class="language-text">blerg@1.x</code>,
it does not install another copy under [B].</p>
<p>Bar [B] also has dependencies on baz and asdf, so those are installed in
bar's <code class="language-text">node_modules</code> folder.  Because it depends on <code class="language-text">baz@2.x</code>, it cannot
re-use the <code class="language-text">baz@1.2.3</code> installed in the parent <code class="language-text">node_modules</code> folder [D],
and must install its own copy [C].</p>
<p>Underneath bar, the <code class="language-text">baz -&gt; quux -&gt; bar</code> dependency creates a cycle.
However, because bar is already in quux's ancestry [B], it does not
unpack another copy of bar into that folder.</p>
<p>Underneath <code class="language-text">foo -&gt; baz</code> [D], quux's [E] folder tree is empty, because its
dependency on bar is satisfied by the parent folder copy installed at [B].</p>
<p>For a graphical breakdown of what is installed where, use <code class="language-text">npm ls</code>.</p>
<h4 id="publishing" style="position:relative;"><a href="#publishing" aria-label="publishing permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>Publishing</h4>
<p>Upon publishing, npm will look in the <code class="language-text">node_modules</code> folder.  If any of
the items there are not in the <code class="language-text">bundledDependencies</code> array, then they will
not be included in the package tarball.</p>
<p>This allows a package maintainer to install all of their dependencies
(and dev dependencies) locally, but only re-publish those items that
cannot be found elsewhere.  See <a href="../../configuring-npm/package.json"><code class="language-text">package.json</code></a> for more information.</p>
<h3 id="see-also" style="position:relative;"><a href="#see-also" aria-label="see also permalink" class="header-link-class before"><svg aria-hidden="true" height="20" version="1.1" viewBox="0 0 16 16" width="20"><path fill-rule="evenodd" d="M4 9h1v1H4c-1.5 0-3-1.69-3-3.5S2.55 3 4 3h4c1.45 0 3 1.69 3 3.5 0 1.41-.91 2.72-2 3.25V8.59c.58-.45 1-1.27 1-2.09C10 5.22 8.98 4 8 4H4c-.98 0-2 1.22-2 2.5S3 9 4 9zm9-3h-1v1h1c1 0 2 1.22 2 2.5S13.98 12 13 12H9c-.98 0-2-1.22-2-2.5 0-.83.42-1.64 1-2.09V6.25c-1.09.53-2 1.84-2 3.25C6 11.31 7.55 13 9 13h4c1.45 0 3-1.69 3-3.5S14.5 6 13 6z"></path></svg></a>See also</h3>
<ul>
<li><a href="../../configuring-npm/package-json">package.json</a></li>
<li><a href="../../cli-commands/install">npm install</a></li>
<li><a href="../../cli-commands/pack">npm pack</a></li>
<li><a href="../../cli-commands/cache">npm cache</a></li>
<li><a href="../../cli-commands/config">npm config</a></li>
<li><a href="../../configuring-npm/npmrc">npmrc</a></li>
<li><a href="../../using-npm/config">config</a></li>
<li><a href="../../cli-commands/publish">npm publish</a></li>
</ul></div><div class="FoundTypo__Container-sc-1e373sc-0 fMOzaj"><p><span role="img" aria-label="eyes-emoji">👀</span> Found a typo? <a href="https://github.com/npm/cli/">Let us know!</a></p><p>The current stable version of npm is <a href="https://github.com/npm/cli/">here</a>. To upgrade, run: <code class="language-text">npm install npm@latest -g</code></p><p>To report bugs or submit feature requests for the docs, please post <a href="https://npm.community/c/support/docs-needed">here</a>. Submit npm issues <a href="https://npm.community/c/bugs">here.</a></p></div><script>
          var anchors = document.querySelectorAll(".sidebar a, .documentation a")
          Array.prototype.slice.call(anchors).map(function(el) {
            if (el.href.match(/file:\/\//)) {
              el.href = el.href + "/index.html"
            }
          })
          </script></div></div></div></div></div><script id="gatsby-script-loader">/*<![CDATA[*/window.pagePath="/configuring-npm/folders";/*]]>*/</script><script id="gatsby-chunk-mapping">/*<![CDATA[*/window.___chunkMapping={"app":["/app-041f7e4f56e7debd8d98.js"],"component---src-templates-page-js":["/component---src-templates-page-js-7faf8ceb01991e80d244.js"],"component---src-pages-404-js":["/component---src-pages-404-js-6c8c4e2e908a7101a231.js"],"component---src-pages-index-js":["/component---src-pages-index-js-6b93f80c513be8d7330c.js"]};/*]]>*/</script><script src="../../component---src-templates-page-js-7faf8ceb01991e80d244.js" async=""></script><script src="../../app-041f7e4f56e7debd8d98.js" async=""></script><script src="../../commons-4df35f6dbd2fdc25d817.js" async=""></script><script src="../../styles-de5e304580bcba768a01.js" async=""></script><script src="../../webpack-runtime-b622568e0ef6e093f777.js" async=""></script></body></html>