{"remainingRequest": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!E:\\ThisGo\\bluebell_frontend\\src\\App.vue?vue&type=style&index=0&id=7ba5bd90&lang=less", "dependencies": [{"path": "E:\\ThisGo\\bluebell_frontend\\src\\App.vue", "mtime": 1596371580000}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1756017427362}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1756017426269}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1756017426179}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\less-loader\\dist\\cjs.js", "mtime": 1756017421263}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1756017427196}, {"path": "E:\\ThisGo\\bluebell_frontend\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1756017426207}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CkBpbXBvcnQgdXJsKCIuL2Fzc2V0cy9jc3MvaWNvbmZvbnQuY3NzIik7Cmh0bWwsIGJvZHl7CiAgICB3aWR0aDogMTAwJTsKICAgIGhlaWdodDogMTAwJTsKICAgIGZvbnQtZmFtaWx5OiBJQk1QbGV4U2FucywgQXJpYWwsIHNhbnMtc2VyaWY7CiAgICBiYWNrZ3JvdW5kOiAjZWVlZWVlOwp9Cmh0bWwsIGJvZHksIGRpdiwgc3BhbiwgYXBwbGV0LCBvYmplY3QsIGlmcmFtZSwgaDEsIGgyLCBoMywgaDQsIGg1LCBoNiwgcCwgYmxvY2txdW90ZSwgcHJlLCBhLCBhYmJyLCBhY3JvbnltLCBhZGRyZXNzLCBiaWcsIGJ1dHRvbiwgY2l0ZSwgY29kZSwgZGVsLCBkZm4sIGVtLCBpbWcsIGlucHV0LCBpbnMsIGtiZCwgcSwgcywgc2FtcCwgc21hbGwsIHN0cmlrZSwgc3Ryb25nLCBzdWIsIHN1cCwgdHQsIHZhciwgYiwgdSwgaSwgY2VudGVyLCBkbCwgZHQsIGRkLCBvbCwgdWwsIGxpLCBmaWVsZHNldCwgZm9ybSwgbGFiZWwsIGxlZ2VuZCwgdGFibGUsIGNhcHRpb24sIHRib2R5LCB0Zm9vdCwgdGhlYWQsIHRyLCB0aCwgdGQsIGFydGljbGUsIGFzaWRlLCBjYW52YXMsIGRldGFpbHMsIGVtYmVkLCBmaWd1cmUsIGZpZ2NhcHRpb24sIGZvb3RlciwgaGVhZGVyLCBoZ3JvdXAsIG1lbnUsIG5hdiwgb3V0cHV0LCBydWJ5LCBzZWN0aW9uLCBzdW1tYXJ5LCB0aW1lLCBtYXJrLCBhdWRpbywgdmlkZW8gewogICAgbWFyZ2luOiAwOwogICAgcGFkZGluZzogMDsKICAgIGJvcmRlcjogMDsKICAgIG91dGxpbmU6IDA7CiAgLnBhZ2UgewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IGF1dG87CiAgfQp9Cgo="}, {"version": 3, "sources": ["App.vue"], "names": [], "mappings": ";AAoBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "App.vue", "sourceRoot": "src", "sourcesContent": ["<template>\n  <div id=\"app\">\n    <div class=\"page\">\n      <HeadBar />\n      <router-view />\n    </div>\n  </div>\n</template>\n\n\n<script>\nimport HeadBar from \"@/components/HeadBar.vue\";\nexport default {\n  components: {\n    HeadBar\n  }\n};\n</script>\n\n<style lang=\"less\">\n@import url(\"./assets/css/iconfont.css\");\nhtml, body{\n    width: 100%;\n    height: 100%;\n    font-family: IBMPlexSans, Arial, sans-serif;\n    background: #eeeeee;\n}\nhtml, body, div, span, applet, object, iframe, h1, h2, h3, h4, h5, h6, p, blockquote, pre, a, abbr, acronym, address, big, button, cite, code, del, dfn, em, img, input, ins, kbd, q, s, samp, small, strike, strong, sub, sup, tt, var, b, u, i, center, dl, dt, dd, ol, ul, li, fieldset, form, label, legend, table, caption, tbody, tfoot, thead, tr, th, td, article, aside, canvas, details, embed, figure, figcaption, footer, header, hgroup, menu, nav, output, ruby, section, summary, time, mark, audio, video {\n    margin: 0;\n    padding: 0;\n    border: 0;\n    outline: 0;\n  .page {\n    width: 100%;\n    height: auto;\n  }\n}\n\n</style>\n"]}]}