---
section: cli-commands 
title: npm-test
description: Test a package
---

# npm-test(1)

## Test a package

### Synopsis

```bash
npm test [-- <args>]

aliases: t, tst
```

### Description

This runs a package's "test" script, if one was provided.

### See Also

* [npm run-script](/cli-commands/run-script)
* [npm scripts](/using-npm/scripts)
* [npm start](/cli-commands/start)
* [npm restart](/cli-commands/restart)
* [npm stop](/cli-commands/stop)
