{"version": 3, "file": "triple-slash-reference.js", "sourceRoot": "", "sources": ["../../src/rules/triple-slash-reference.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8CAAgC;AAYhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,wFAAwF;YAC1F,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,oBAAoB,EAClB,iFAAiF;SACpF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,GAAG,EAAE;wBACH,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC;qBAC1B;oBACD,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,eAAe,CAAC;qBAC3C;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,GAAG,EAAE,QAAQ;YACb,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,eAAe;SACvB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QACpC,IAAI,WAA0B,CAAC;QAC/B,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,MAAM,UAAU,GAGT,EAAE,CAAC;QAEV,SAAS,oBAAoB,CAAC,MAAwB;YACpD,UAAU,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBAC7B,IAAI,SAAS,CAAC,UAAU,KAAK,MAAM,CAAC,KAAK,EAAE;oBACzC,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI,EAAE,SAAS,CAAC,OAAO;wBACvB,SAAS,EAAE,sBAAsB;wBACjC,IAAI,EAAE;4BACJ,MAAM,EAAE,SAAS,CAAC,UAAU;yBAC7B;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QACD,OAAO;YACL,iBAAiB,CAAC,IAAI;gBACpB,IAAI,WAAW,EAAE;oBACf,MAAM,MAAM,GAAG,IAAI,CAAC,MAA0B,CAAC;oBAC/C,oBAAoB,CAAC,MAAM,CAAC,CAAC;iBAC9B;YACH,CAAC;YACD,yBAAyB,CAAC,IAAI;gBAC5B,IAAI,WAAW,EAAE;oBACf,MAAM,MAAM,GAAI,IAAI,CAAC,eAAsD;yBACxE,UAA8B,CAAC;oBAClC,oBAAoB,CAAC,MAAM,CAAC,CAAC;iBAC9B;YACH,CAAC;YACD,OAAO,CAAC,IAAI;gBACV,IAAI,GAAG,KAAK,QAAQ,IAAI,IAAI,KAAK,QAAQ,IAAI,KAAK,IAAI,QAAQ,EAAE;oBAC9D,OAAO;iBACR;gBACD,WAAW,GAAG,IAAI,CAAC;gBACnB,MAAM,eAAe,GAAG,0DAA0D,CAAC;gBACnF,MAAM,cAAc,GAAG,UAAU,CAAC,iBAAiB,CAAC,WAAW,CAAC,CAAC;gBAEjE,cAAc,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;oBAC/B,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE;wBAC3B,OAAO;qBACR;oBACD,MAAM,eAAe,GAAG,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;oBAE5D,IAAI,eAAe,EAAE;wBACnB,IACE,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,OAAO,CAAC;4BACrD,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,MAAM,IAAI,IAAI,KAAK,OAAO,CAAC;4BACnD,CAAC,eAAe,CAAC,CAAC,CAAC,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC,EACjD;4BACA,OAAO,CAAC,MAAM,CAAC;gCACb,IAAI,EAAE,OAAO;gCACb,SAAS,EAAE,sBAAsB;gCACjC,IAAI,EAAE;oCACJ,MAAM,EAAE,eAAe,CAAC,CAAC,CAAC;iCAC3B;6BACF,CAAC,CAAC;4BACH,OAAO;yBACR;wBACD,IAAI,eAAe,CAAC,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,KAAK,eAAe,EAAE;4BAC/D,UAAU,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;yBAC9D;qBACF;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}