{"version": 3, "file": "no-misused-promises.js", "sourceRoot": "", "sources": ["../../src/rules/no-misused-promises.ts"], "names": [], "mappings": ";;;;;;;;;;;;AACA,iDAAmC;AACnC,4DAA4B;AAE5B,8CAAgC;AAShC,kBAAe,IAAI,CAAC,UAAU,CAAwC;IACpE,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,4DAA4D;YACzE,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,UAAU,EACR,yEAAyE;YAC3E,WAAW,EAAE,sDAAsD;SACpE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,kBAAkB,EAAE;wBAClB,IAAI,EAAE,SAAS;qBAChB;oBACD,gBAAgB,EAAE;wBAChB,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,kBAAkB,EAAE,IAAI;YACxB,gBAAgB,EAAE,IAAI;SACvB;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,CAAC;QACxD,MAAM,cAAc,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACvD,MAAM,OAAO,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAExD,MAAM,iBAAiB,GAA0B;YAC/C,qBAAqB,EAAE,oBAAoB;YAC3C,gBAAgB,EAAE,oBAAoB;YACtC,YAAY,EAAE,oBAAoB;YAClC,WAAW,EAAE,oBAAoB;YACjC,iBAAiB,CAAC,IAAI;gBACpB,sEAAsE;gBACtE,qDAAqD;gBACrD,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,IAAI,IAAI,CAAC,QAAQ,KAAK,GAAG,EAAE;oBACzB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACjC;YACH,CAAC;YACD,cAAc,EAAE,oBAAoB;SACrC,CAAC;QAEF,MAAM,gBAAgB,GAA0B;YAC9C,cAAc,EAAE,cAAc;YAC9B,aAAa,EAAE,cAAc;SAC9B,CAAC;QAEF,SAAS,oBAAoB,CAAC,IAA0C;YACtE,IAAI,IAAI,CAAC,IAAI,EAAE;gBACb,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;aAC7B;QACH,CAAC;QAED,SAAS,gBAAgB,CAAC,IAAyB;YACjD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC9D,IAAI,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAE;gBACrC,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EAAE,aAAa;oBACxB,IAAI;iBACL,CAAC,CAAC;aACJ;QACH,CAAC;QAED,SAAS,cAAc,CACrB,IAAsD;YAEtD,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAErD,IAAI,CAAC,CAAC;YACR,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACvD,IAAI,UAAU,CAAC,IAAI,KAAK,CAAC,EAAE;gBACzB,OAAO;aACR;YAED,KAAK,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE;gBACxD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;oBAC1B,SAAS;iBACV;gBAED,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAClE,IAAI,eAAe,CAAC,OAAO,EAAE,MAAuB,CAAC,EAAE;oBACrD,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,YAAY;wBACvB,IAAI,EAAE,QAAQ;qBACf,CAAC,CAAC;iBACJ;aACF;QACH,CAAC;QAED,yBACK,CAAC,kBAAkB,CAAC,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,EAC7C,CAAC,gBAAgB,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,CAAC,EAC7C;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,8EAA8E;AAC9E,2EAA2E;AAC3E,+EAA+E;AAC/E,wBAAwB;AACxB,SAAS,gBAAgB,CAAC,OAAuB,EAAE,IAAa;IAC9D,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;IAE7C,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC,EAAE;QAC3E,MAAM,QAAQ,GAAG,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAE7C,2EAA2E;QAC3E,SAAS;QACT,IAAI,QAAQ,KAAK,SAAS,EAAE;YAC1B,OAAO,KAAK,CAAC;SACd;QAED,wEAAwE;QACxE,uEAAuE;QACvE,gDAAgD;QAChD,MAAM,QAAQ,GAAG,OAAO,CAAC,yBAAyB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QACnE,IAAI,oBAAoB,GAAG,KAAK,CAAC;QACjC,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;YACtD,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,iBAAiB,EAAE,EAAE;gBACnD,IACE,SAAS,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC;oBACjC,eAAe,CAAC,OAAO,EAAE,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,EACvD;oBACA,oBAAoB,GAAG,IAAI,CAAC;oBAC5B,MAAM;iBACP;aACF;YAED,mEAAmE;YACnE,4CAA4C;YAC5C,IAAI,oBAAoB,EAAE;gBACxB,MAAM;aACP;SACF;QAED,yEAAyE;QACzE,8BAA8B;QAC9B,IAAI,CAAC,oBAAoB,EAAE;YACzB,OAAO,KAAK,CAAC;SACd;KACF;IAED,4EAA4E;IAC5E,qCAAqC;IACrC,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAS,eAAe,CACtB,OAAuB,EACvB,KAAgB,EAChB,IAAa;IAEb,MAAM,IAAI,GAAwB,OAAO,CAAC,eAAe,CACvD,OAAO,CAAC,yBAAyB,CAAC,KAAK,EAAE,IAAI,CAAC,CAC/C,CAAC;IACF,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAClD,IAAI,OAAO,CAAC,iBAAiB,EAAE,CAAC,MAAM,KAAK,CAAC,EAAE;YAC5C,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,yEAAyE;AACzE,6EAA6E;AAC7E,yBAAyB;AACzB,SAAS,kBAAkB,CACzB,OAAuB,EACvB,IAA0C;IAE1C,MAAM,iBAAiB,GAAG,IAAI,GAAG,EAAU,CAAC;IAC5C,MAAM,qBAAqB,GAAG,IAAI,GAAG,EAAU,CAAC;IAChD,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;IAExD,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAClD,2EAA2E;QAC3E,MAAM,UAAU,GAAG,oBAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC1C,CAAC,CAAC,OAAO,CAAC,iBAAiB,EAAE;YAC7B,CAAC,CAAC,OAAO,CAAC,sBAAsB,EAAE,CAAC;QACrC,KAAK,MAAM,SAAS,IAAI,UAAU,EAAE;YAClC,KAAK,MAAM,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,SAAS,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE;gBAC/D,MAAM,IAAI,GAAG,OAAO,CAAC,yBAAyB,CAC5C,SAAS,EACT,IAAI,CAAC,UAAU,CAChB,CAAC;gBACF,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;oBAClD,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,iBAAiB,EAAE,EAAE;wBACnD,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;wBAC7C,IAAI,OAAO,CAAC,aAAa,CAAC,UAAU,EAAE,oBAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;4BACxD,iBAAiB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;yBAC9B;6BAAM,IACL,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,UAAU,CAAC,EAC5D;4BACA,qBAAqB,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;yBAClC;qBACF;iBACF;aACF;SACF;KACF;IAED,2EAA2E;IAC3E,wCAAwC;IACxC,KAAK,MAAM,QAAQ,IAAI,qBAAqB,EAAE;QAC5C,iBAAiB,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KACpC;IAED,OAAO,iBAAiB,CAAC;AAC3B,CAAC;AAED,uEAAuE;AACvE,SAAS,eAAe,CAAC,OAAuB,EAAE,IAAmB;IACnE,MAAM,IAAI,GAAG,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;IAEtE,KAAK,MAAM,OAAO,IAAI,OAAO,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE;QAClD,KAAK,MAAM,SAAS,IAAI,OAAO,CAAC,iBAAiB,EAAE,EAAE;YACnD,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;YAC7C,IAAI,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,UAAU,CAAC,EAAE;gBACrD,OAAO,IAAI,CAAC;aACb;SACF;KACF;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}