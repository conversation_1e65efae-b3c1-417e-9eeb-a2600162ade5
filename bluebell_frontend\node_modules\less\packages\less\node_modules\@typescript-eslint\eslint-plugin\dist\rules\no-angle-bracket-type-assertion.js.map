{"version": 3, "file": "no-angle-bracket-type-assertion.js", "sourceRoot": "", "sources": ["../../src/rules/no-angle-bracket-type-assertion.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8CAAgC;AAEhC,kBAAe,IAAI,CAAC,UAAU,CAAC;IAC7B,IAAI,EAAE,iCAAiC;IACvC,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,yEAAyE;YAC3E,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,OAAO;SACrB;QACD,QAAQ,EAAE;YACR,QAAQ,EACN,0EAA0E;SAC7E;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,EAAE,CAAC;QAC3C,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,UAAU;oBACrB,IAAI,EAAE;wBACJ,IAAI,EAAE,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC;qBAC9C;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}