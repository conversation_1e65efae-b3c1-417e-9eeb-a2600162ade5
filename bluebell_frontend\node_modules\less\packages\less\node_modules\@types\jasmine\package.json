{"name": "@types/jasmine", "version": "3.4.5", "description": "TypeScript definitions for Jasmine", "license": "MIT", "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/theodorejb", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/david<PERSON>sson", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/gmoothart", "githubUsername": "gmo<PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lukas-zech-software", "githubUsername": "lukas-zech-software"}, {"name": "<PERSON>", "url": "https://github.com/Engineer2B", "githubUsername": "Engineer2B"}, {"name": "<PERSON>", "url": "https://github.com/cyungmann", "githubUsername": "<PERSON><PERSON><PERSON>"}, {"name": "Giles Roadnight", "url": "https://github.com/Roaders", "githubUsername": "Roaders"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/devoto13", "githubUsername": "devoto13"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/fdim", "githubUsername": "fdim"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kolodny", "githubUsername": "kolodny"}, {"name": "<PERSON>", "url": "https://github.com/stephenfarrar", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/ndunks", "githubUsername": "ndunks"}, {"name": "<PERSON>", "url": "https://github.com/zvirja", "githubUsername": "zvirja"}], "main": "", "types": "index", "typesVersions": {">=3.1.0-0": {"*": ["ts3.1/*"]}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jasmine"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "95fd696e9847f92b1e29e3cb0f84a1f0d35c4a11e3bb99da7e5c049b4c48f5d9", "typeScriptVersion": "2.8"}