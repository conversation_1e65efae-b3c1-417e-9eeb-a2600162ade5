{"version": 3, "file": "generic-type-naming.js", "sourceRoot": "", "sources": ["../../src/rules/generic-type-naming.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,8CAAgC;AAKhC,kBAAe,IAAI,CAAC,UAAU,CAAsB;IAClD,IAAI,EAAE,qBAAqB;IAC3B,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,QAAQ,EAAE,kBAAkB;YAC5B,WAAW,EAAE,KAAK;SACnB;QACD,QAAQ,EAAE;YACR,iBAAiB,EACf,uDAAuD;SAC1D;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;aACf;SACF;KACF;IACD,cAAc,EAAE;QACd,4CAA4C;QAC5C,gCAAgC;KACjC;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC;QACpB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAK,CAAC,CAAC;QAEhC,OAAO;YACL,eAAe,CAAC,IAAI;gBAClB,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBAE5B,IAAI,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;oBAC7B,OAAO,CAAC,MAAM,CAAC;wBACb,IAAI;wBACJ,SAAS,EAAE,mBAAmB;wBAC9B,IAAI,EAAE;4BACJ,IAAI;4BACJ,IAAI;yBACL;qBACF,CAAC,CAAC;iBACJ;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}