h1, h2, h3 {
  a, p {
    &:hover {
      color: red;
    }
  }
}

#all { color: blue; }
#the { color: blue; }
#same { color: blue; }

ul, li, div, q, blockquote, textarea {
  margin: 0;
}

td {
  margin: 0;
  padding: 0;
}

td, input {
  line-height: 1em;
}

a {
  color: red;

  &:hover { color: blue; }

  div & { color: green; }

  p & span { color: yellow; }
}

.foo {
  .bar, .baz {
    & .qux {
      display: block;
    }
    .qux & {
      display: inline;
    }
    .qux& {
      display: inline-block;
    }
    .qux & .biz {
      display: none;
    }
  }
}

.b {
 &.c {
  .a& {
   color: red;
  }
 }
}

.b {
 .c & {
  &.a {
   color: red;
  }
 }
}

.p {
  .foo &.bar {
    color: red;
  }
}

.p {
  .foo&.bar {
    color: red;
  }
}

.foo {
  .foo + & {
    background: amber;
  }
  & + & {
    background: amber;
  }
}

.foo, .bar {
  & + & {
    background: amber;
  }
}

.foo, .bar {
  a, b {
    & > & {
      background: amber;
    }
  }
}

.other ::fnord { color: red }
.other::fnord { color: red }
.other {
  ::bnord {color: red }
  &::bnord {color: red }
}
// selector interpolation
@theme: blood;
@selector: ~".@{theme}";
@{selector} {
  color:red;
}
@{selector}red {
  color: green;
}
.red {
  #@{theme}.@{theme}&.black:@{theme} {
    color:black;
  }
}
@num: 3;
:nth-child(@{num}) {
  selector: interpolated;
}
.test-rule {
  &:nth-child(@{num}) {
    selector: interpolated;
  }
  &:nth-child(odd):not(:nth-child(3)) {
    color: #ff0000;
  }
 }
[prop],
[prop=10%],
[prop="value@{num}"],
[prop*="val@{num}"],
[|prop~="val@{num}"],
[*|prop$="val@{num}"],
[ns|prop^="val@{num}"],
[@{num}^="val@{num}"],
[@{num}=@{num}],
[@{num}] {
  attributes: yes;
}

/*
Large comment means chunk will be emitted after } which means chunk will begin with whitespace...
blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank
blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank
blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank
blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank
blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank blank
*/
@{selector} {
  color: red;
}
.only-nested {
  .level2 {
    .foo:not(.tst&:hover) {
      test: only-nested;
    }
  }
}
.nestend-and-non-nested {
  .foo&:not(.tst&:hover) {
    test: nestend-and-non-nested;
  }
}
.selector:not(&:hover) {
  test: global scope;
}
// https://github.com/less/less.js/issues/2206
.extend-this {
  content: '\2661';
}
.first-level {
  .second-level {
    .active&:extend(.extend-this) { }
    &.active2:extend(.extend-this) { }
  }
}
